import { openDB, IDBPDatabase } from 'idb';

// 缓存数据库实例
let dbInstance: IDBPDatabase<unknown> | null = null;

const getDBInstance = async () => {
  if (!dbInstance) {
    try {
      dbInstance = await openDB('CloudQueryDB', 1, {
        upgrade(db) {
          if (!db.objectStoreNames.contains('persistedReducer')) {
            db.createObjectStore('persistedReducer');
          }
        },
      });
    } catch (error) {
      console.error('Failed to open IndexedDB:', error);
      throw error;
    }
  }
  return dbInstance;
};


export const indexedDBStorage = {
  getItem: async (key: IDBValidKey) => {
    try {
      const db = await getDBInstance();
      if (!db) throw new Error('Database instance is not available.');
      return db.get('persistedReducer', key);
    } catch (error) {
      console.error('Error in getItem:', error);
      throw error;
    }
  },
  setItem: async (key: IDB<PERSON>alid<PERSON>ey, value: unknown) => {
    if (key === undefined) {
      throw new Error('Key cannot be undefined in setItem.');
    }
    try {
      const db = await getDBInstance();
      if (!db) throw new Error('Database instance is not available.');
      return db.put('persistedReducer', value, key);
    } catch (error) {
      console.error('Error in setItem:', error);
      throw error;
    }
  },
  removeItem: async (key: IDBValidKey) => {
    try {
      const db = await getDBInstance();
      if (!db) throw new Error('Database instance is not available.');
      return db.delete('persistedReducer', key);
    } catch (error) {
      console.error('Error in removeItem:', error);
      throw error;
    }
  },
};