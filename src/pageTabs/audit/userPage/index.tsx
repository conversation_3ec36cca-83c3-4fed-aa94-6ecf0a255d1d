/**
 * 用户审计
 */
import React, { useEffect, useState } from 'react'
import { Input, Table, Spin, Badge, Breadcrumb } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { getUserlist } from 'src/api'
import styles from './index.module.scss'
import { useRequest, useDispatch } from 'src/hook';
import { renderTableFields } from 'src/util'
import { useTranslation } from 'react-i18next'
import { 
	setOverviewPageState,
	setOverviewPageDetailParams,
	setUserAuditPageState,
	setUserAuditPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'
import { useSelector } from 'react-redux'
import UserDetail from "src/pageTabs/audit/userPage/Detail";

interface PaginationI {
	current: number,
	pageSize: number,
	total: number
}
const UserAudit = () => {
	const dispatch = useDispatch()
	const { t } = useTranslation()
	const { userAuditPageState, userAuditPageDetailParams } = useSelector((state: any) => state.overview)
	const [searchValue, setSearchValue] = useState('')
	const [sort, setSort] = useState('')
	const location = userAuditPageDetailParams;

	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState<PaginationI>({
		current: 1,
		pageSize: 10,
		total: 0
	})

	const { run: getUserAuditList, loading } = useRequest(getUserlist, {
		manual: true,
		debounceInterval: 300,
		onSuccess: (res: any) => {
			const { data = [], total = 0 } = res
			setPagination((p: any) => {
				return {
					...p,
					total,
				}
			})
			setDataSource(data)
		}
	})

	useEffect(()=>{
		return () => {
			dispatch(setUserAuditPageState(''))
			dispatch(setUserAuditPageDetailParams({}))
		}
	},[])

	useEffect(() => {
		// 修改成默认进来是没有时间筛选的
		let timeRangeTmp = [
			undefined,
			undefined
		];
		if (location?.state?.timeRange) {
			timeRangeTmp = location?.state?.timeRange;
		}
		const params = {
			sort,
			fuzzySearchKeyword: searchValue,
			executeBeginMs: timeRangeTmp[0],
			executeEndMs: timeRangeTmp[1],
		} as any
		if (location?.state?.isOnLineUser === 1) {
			params.onLineFlag = location?.state?.onLineFlag
		}
		if (searchValue) {
			setPagination({
				current: 1,
				pageSize: pagination.pageSize,
				total: 0
			})
		}
		getUserAuditList(params)
	}, [searchValue, sort, location?.state])

	// 搜索值
	const handleSearch = (e: any) => {
		const value = e.target.value;
		setSearchValue(value?.trim());
	};

	// 查看详情
	const goToDetail = (record: any) => {
		gotoUserAuditDetail({ userId: record?.userId })
	}


	// 用户审计-用户详情
	const gotoUserAuditDetail = (params: any) => {
		dispatch(setUserAuditPageState('user_detail'))
		dispatch(setUserAuditPageDetailParams(params))
	}

	const handleTableChange = (
		pagination: any,
		filters: Record<string, any>,
	) => {
		const { current, pageSize } = pagination
		const { loginTime } = filters
		setPagination((p: any) => {
			return {
				...p,
				current,
				pageSize,
			}
		})
		setSort(loginTime?.[0] || '')
	}

	const columns: any[] = [
		{
			title: t("auays:tb_title.username"),
			dataIndex: 'userId',
			width: 200,
			fixed: 'left',
			ellipsis: true,
			render: (txt: string, record: any) => <span className={styles.options} onClick={() => goToDetail(record)}>{`${renderTableFields(txt)}(${renderTableFields(record?.userName)})`}</span>
		},
		{
			title: t("auays:tb_title.client_ip"),
			dataIndex: 'clientIp',
			width: 160,
			ellipsis: true,
			render: (txt: string) => <span>{renderTableFields(txt)}</span>
		},
		{
			title: t("auays:tb_title.department"),
			dataIndex: 'dept',
			width: 160,
			ellipsis: true,
			render: (txt: string) => <span>{renderTableFields(txt)}</span>
		},
		{
			title: t("auays:tb_title.online_time"),
			dataIndex: 'loginTime',
			width: 200,
			render: (txt: string) => <span>{renderTableFields(txt)}</span>,
			filters: [
				{
					value: 'ASC',
					text: t("auays:tb_filter.time_asc"),
				}, {
					value: 'DESC',
					text: t("auays:tb_filter.time_desc"),
				}
			],
			filterMultiple: false
		},
		{
			title: t("auays:tb_title.offline_time"),
			dataIndex: 'logoutTime',
			width: 200,
			render: (txt: string) => <span>{renderTableFields(txt)}</span>
		},
		{
			title: t("auays:tb_title.online_status"),
			dataIndex: 'status',
			width: 120,
			ellipsis: true,
			render: (val: boolean) => (
				<Badge
					status={val ? 'success' : 'error'}
					text={val ? t("auays:tb_rd.status.online") : t("auays:tb_rd.status.offline")}
				/>
			)
		},
		{
			title: t("auays:tb_title.online_duration"),
			dataIndex: 'onlineDuration',
			width: 160,
			ellipsis: true,
			render: (txt: string) => <span>{renderTableFields(txt)}</span>
		}
	]

	// 渲染审计概览
	const gotoAuditView = () => {
		dispatch(setOverviewPageState(''))
		dispatch(setOverviewPageDetailParams({}))
	}

	// 渲染用户审计-用户详情
	if(userAuditPageState === 'user_detail'){
		return <UserDetail />
	}

	return (
		<div className="cq-container">
			<Spin spinning={loading}>
				<div style={{ padding: '10px 20px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }} className="div-breadcrumb">
					{/* <SimpleBreadcrumbs items={BREADCRUMB_ITEMS} /> */}
					<Breadcrumb className={styles.breadcrumb} separator=''>
						<Breadcrumb.Item>{t("auays:bc_title.audit_analysis")}</Breadcrumb.Item>
						<Breadcrumb.Separator>|</Breadcrumb.Separator>
						{
							location?.state?.tertiaryDirectoryMark === 1 ?
								<>
									<Breadcrumb.Item><span className='breadcrumbLink' onClick={gotoAuditView}>{t("auays:bc_title.audit_overview")}</span></Breadcrumb.Item>
									<Breadcrumb.Separator>/</Breadcrumb.Separator>
								</>
								: <></>
						}
						<Breadcrumb.Item>{t("auays:bc_title.user_audit")}</Breadcrumb.Item>
					</Breadcrumb>
					<Input 
					  allowClear
						placeholder={t("auays:inp_ph.search_username")}
						style={{ width: 200 }}
						prefix={<SearchOutlined />}
						onChange={handleSearch}
						value={searchValue}
					/>
				</div>
				<div className={styles.userAuditWrap}>
					<Table
						rowKey='userId'
						columns={columns}
						dataSource={dataSource}
						pagination={{
							...pagination,
							showQuickJumper: true,
							showSizeChanger: true,
							showTotal: (total) => t("auays:tb.show_total", { total: total || 0 })
						}}
						scroll={{ x: 1400, y: `calc(100vh - 278px)` }}
						onChange={handleTableChange}
					/>
				</div>
			</Spin>
		</div>
	)
}

export default UserAudit