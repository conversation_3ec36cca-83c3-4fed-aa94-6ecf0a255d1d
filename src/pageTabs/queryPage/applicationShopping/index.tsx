import React, { useEffect, useState } from 'react'
import { CloseOutlined } from '@ant-design/icons'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { ResponseNodeEntity, deleteCartDataThin, getCartDataThin, startFlowInstance, updateCartDataThin } from 'src/api'
import { useForm } from 'antd/lib/form/Form'
import { Button, Form, Modal, Input, Tree, Tooltip, message, Popover, Popconfirm, Spin } from 'antd'
import { setVisibleApplicationShop } from 'src/pageTabs/queryPage/queryPageSlice'
import moment from 'moment'
import { Iconfont } from 'src/components'
import { DataNode } from 'antd/lib/tree'
import { getSdtThinPermissionIconType } from 'src/util';
import { getSpecialConnectionTypePermissions } from 'src/constants'
import 'moment/locale/zh-cn'
import styles from './index.module.scss'
import { FormItemEffectiveTime, FormItemMultiConectionApprover } from 'src/pageTabs/flowPages/flowFormItems'
import { useTranslation } from 'react-i18next'

moment.locale('zh-cn')

interface RenderNode extends ResponseNodeEntity, DataNode {
  valid?: boolean
  enable?: boolean
  dataSourceType?: string
}

const FormLayout = {
  labelCol: { span: 4 },
  wrapperCol: { offset: 0, span: 16 },
}

export const ApplicationShopping = () => {
  const { t } = useTranslation()
  const [form] = useForm()
  const dispatch = useDispatch()
  const [visibleFlag, setVisibleFlag] = useState(false)
  const [expandKeyArr, setExpandKeyArr] = useState<any>([])
  const [treeData, setTreeData] = useState<RenderNode[]>([])

  const { visibleApplicationShop, addToCartFlag } = useSelector((state) => state.queryPage)
  // 获取清单
  const { data: list, refresh, cancel, loading: cartLoading, run } = useRequest(getCartDataThin, {
    refreshDeps: [addToCartFlag],
    formatResult(data) {
      const newBusinessDataList = data?.businessDataList;
   
      setTreeData(setTreeValue(newBusinessDataList));
      return {
        businessDataList: setTreeValue(newBusinessDataList),
        resourceCount: data?.resourceCount
      }
    }
  })

  useEffect(() => {
    if (visibleFlag && treeData?.length) {
      form.setFieldsValue({connAndAssigneeMapList:treeData?.map((i: any) => ({nodeName: i?.nodeName?.split('.')?.[0],connectionId:  i?.connectionId, dataSourceType: i?.dataSourceType})) })
    }
  },[visibleFlag,JSON.stringify(treeData)])

  // 更新权限
  const { run: updateRun, loading: updateLoading } = useRequest(updateCartDataThin, {
    manual: true,
    onSuccess() {
      refresh()
    }
  })

  // 提权申请
  const { data: startFlow, loading: thinCartSubmissionLoading, run: runStartFlow } = useRequest(startFlowInstance, {
    manual: true,
    onSuccess(data) {
      setExpandKeyArr([])
      message.success(t('sdo_rights_acquired'))
      refresh()
      setVisibleFlag(false)
      form.resetFields()
    }
  })

  const { loading: delLoading, run: delRun } = useRequest(deleteCartDataThin, {
    manual: true,
    onSuccess() {
      setExpandKeyArr([])
      message.success(t('sdo_deleted'))
      refresh()
    }
  })

  const setTreeValue = (arr: any) => {
    const newArr = arr?.filter((item: any, index: number) => {
      item.key = item?.nodePath
      if (item?.children && item?.children?.length) {
        item.children = setTreeValue(item?.children)
      }
      setExpandKeyArr((v: any[])=>[...v, item?.key])
      return item
    })
    return newArr
  }

  if (!visibleApplicationShop) {
    return (
      <div
        className={styles.nodeDetailInActiveWrapper}
        onClick={() => dispatch(setVisibleApplicationShop(true))}
      >
        <Iconfont type='icon-gouwuche' style={{ color: '#277AFE', fontSize: '16px', marginBottom: '10px' }} />
        <div className={styles.count}>
          {list?.resourceCount >= 99 ? '99' : (list?.resourceCount ? list?.resourceCount : 0)}
        </div>
        <div className={styles.nodeName}>
          {t('sdo_application_form')}
        </div>
      </div>
    )
  }
  const titleRender = (node: RenderNode) => {
    const {
      nodeName,
      nodeType,
      dataSourceType,
      sourceOperationList,
      flowUUID,
      id
    } = node

    const getNodeIcon = () => {
      switch (nodeType) {
        case 'connectionGroup':
          return (
            <Iconfont
              style={{ color: '#3f84e9' }}
              type="icon-folder-fill"
              className="mr8"
            ></Iconfont>
          )
        case 'connection':
          return (
            <Iconfont
              type={'icon-connection-' + dataSourceType}
              className="mr8"
            ></Iconfont>
          )
        case 'trigger':
          return <Iconfont
            type={'icon-' + nodeType + '_off'}
            className="mr8"
          ></Iconfont>
        case 'schema':
          return <Iconfont
            type={'icon-connection-' + dataSourceType}
            className="mr8"
          ></Iconfont>
        default:
          let defaultIconType = `icon-${nodeType}`
          return <Iconfont
            type={defaultIconType}
            className="mr8"
          ></Iconfont>
      }
    }
    const nodeIcon = getNodeIcon()

    return (
      <div
        className={styles.sdtTitleWrap}
      >
        <Popover
          content={nodeName}
        >
          <span>
            {nodeIcon}
            {nodeName?.length <= 15 ? nodeName : nodeName?.slice(0, 15) + '...'}
          </span>
        </Popover>

        <span style={{ textAlign: 'right' }}>
          {
            getSpecialConnectionTypePermissions(dataSourceType)[nodeType]?.map((type: string) => {
              let permissionTypeStatus = sourceOperationList[type]
              const permissionType = getSdtThinPermissionIconType(type);
              let iconType = permissionTypeStatus === '1' ? `icon-type-${permissionType}` : (
                permissionTypeStatus === '0' ? `icon-type-${permissionType}_disabled` : `icon-type-${permissionType}_add`
              )
              return (
                <Tooltip title={type} key={type}>
                  <Iconfont
                    type={iconType}
                    className={styles.permissionLimitIcon}
                    onClick={() => {
                      if (permissionTypeStatus !== '1') {
                        let params = {
                          ...sourceOperationList,
                        }
                        params[type] = params[type] === '0' ? '2' : '0'
                        updateRun({
                          id,
                          flowUUID,
                          operationMap: params
                        })
                      } else {
                        message.warning(t('sdo_existing_permissions'))
                      }
                    }}
                  />
                </Tooltip>
              )
            }
            )}
          <Popconfirm
            placement="topLeft"
            title={t('sdo_confirm_delete_node')}
            onConfirm={() => {
              delRun({
                nodePathWithType: node?.nodePath,
                flowType: 'THIN'
              })
            }}
            okText={t('sdo_ok')}
            cancelText={t('sdo_cancel')}
          >
            <Tooltip title={t('sdo_del')}>
              <CloseOutlined className={styles.ml10} />
            </Tooltip>
          </Popconfirm>
        </span>
      </div>
    )
  }

  const onSubmit = () => {
    form.validateFields().then().then((values: any) => {
           
      let params: any = {
        priGranType: 'THIN',
        flowType: 'dataManipulation',
        title: values?.title,
        remark: values?.remark,
        ...(values?.connAndAssigneeMapList ? {connAndAssigneeMapList: values?.connAndAssigneeMapList } : {})
      }
      if (values.timeType !== 'forever') {
        const beginTime = values?.time?.[0]?.format('YYYY-MM-DD HH:mm:ss');
        const endTime =  values?.time?.[1]?.format('YYYY-MM-DD HH:mm:ss');
        const s = moment(beginTime)?.valueOf();
        const e = moment(endTime)?.valueOf();
        
        if (e < moment()?.valueOf()) {
          return message.error(t('sdo_time_range_end_greater_current'))
        }else if (s > e) {
          return message.error(t('sdo_time_range_start_less_end'));
        }
        params = {
          ...params,
          beginTime,
          endTime
       }
      }
      runStartFlow(params)
    })
  }

  return (
    <div className={styles.nodeDetailActiveWrapper}>
      <Iconfont
        type='icon-shouhui'
        className={styles.iconBack}
        onClick={() => dispatch(setVisibleApplicationShop(false))}
      />
      <div className={styles.nodeDetailHeader}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Tooltip title={t('sdo_close')}>
            <Iconfont type='icon-gouwuche' style={{ color: '#277AFE', fontSize: '16px', marginRight: '8px' }} />
          </Tooltip>
          <div className={styles.count}>
            {list?.resourceCount >= 99 ? '99' : (list?.resourceCount ? list?.resourceCount : 0)}
          </div>
          {t('sdo_applications_list')}
        </div>
        <div>

          <Popconfirm
            placement="topLeft"
            title={t('sdo_confirm_clear_application_list')}
            onConfirm={() => {
              delRun({
                nodePathWithType: '/',
                flowType: 'THIN'
              })
              // stopLocalInstanceProgress(selectedProcess)
            }}
            okText={t('sdo_ok')}
            cancelText={t('sdo_cancel')}
          >
            <span style={{ marginRight: '10px', cursor: 'pointer' }}>{t('sdo_clear')}</span>
          </Popconfirm>
          {/* <CloseOutlined
            className={styles.nodeDetailClose}
            onClick={() => dispatch(setVisibleApplicationShop(false))}
          /> */}
        </div>
      </div>

      <div className={styles.nodeDetailContent}>
        <Spin spinning={cartLoading || delLoading}>
          <Tree
            className={styles.tree}
            treeData={treeData}
            titleRender={titleRender as any}
            showIcon={false}
            selectable={false}
            defaultExpandAll={true}
            expandedKeys={expandKeyArr.length ? expandKeyArr : []}
            onExpand={(expandedKeys, { expanded: bool, node }) => {
              setExpandKeyArr(expandedKeys)
            }}
          />
        </Spin>
        <Modal
          visible={visibleFlag}
          closable={false}
          title={t('sdo_submit_application_form')}
          footer={[
            <Button 
              type='primary'
              loading={thinCartSubmissionLoading}
              onClick={() => onSubmit()}
            >
              {t('sdo_submit_application')}
            </Button>,
            <Button onClick={() => {
              setVisibleFlag(false)
              form.resetFields()
            }}>{t('sdo_cancel')}</Button>
          ]}
          width={700}
        >
          <Form form={form} {...FormLayout} >
            <FormItemEffectiveTime
              form={form}
              effectiveTimeRequired={false}
              effectiveTimeKey="timeType"
              timeKey= "time"
            />
            <Form.Item
              label={t('sdo_title')}
              name="title"
              rules={[{ required: true }, { min: 2, max: 20 }]}
            >
              <Input allowClear />
            </Form.Item>
            <Form.Item
              label={t('sdo_remark')}
              name="remark"
            // rules={[{ required: true }]}
            >
              <Input.TextArea allowClear maxLength={100} />
            </Form.Item>
            <FormItemMultiConectionApprover 
              flowType = 'THIN'
              isRender={visibleApplicationShop}
              isMultiConnection={true}
              isRefresh={visibleFlag}
             />
          </Form>
        </Modal>
      </div>
      <div className={styles.footerBtn}>
        <Button
          type='primary'
          onClick={() => setVisibleFlag(true)}
          style={{ flex: 1 }}
          disabled={!Boolean(list?.resourceCount)}
        >
          {t('sdo_submit_application')}
        </Button>
      </div>
    </div>
  )
}
