import React, { memo, useState, useMemo } from 'react';
import { Button, Dropdown, Menu } from 'antd';
import { useRequest, useDispatch, useSelector } from 'src/hook';
import { getCountOfQueryResult, QueryBase } from 'src/api';
import { useHandleExternalInfo } from 'src/hook/useHandleExternalInfo';
import { updateTabsInfo } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice';
import { useTranslation } from 'react-i18next';
import styles from '../../toolbar.module.scss';

// 导入 SVG 图标
import BigLeftIcon from 'src/assets/resultTabsAssets/bigLeft.svg';
import LeftIcon from 'src/assets/resultTabsAssets/left.svg';
import RightIcon from 'src/assets/resultTabsAssets/right.svg';
import DownIcon from 'src/assets/resultTabsAssets/down.svg';

interface N1PaginationProps {
  currentPageNumber: number;      // 当前页码
  pageSize: number;               // 每页条数
  startRowInPage: number;         // 当前页起始行号
  endRowInPage: number;           // 当前页结束行号
  hasNextPage: boolean;           // 是否有下一页
  isLoading?: boolean;            // 是否正在加载
  canGoPrev: boolean;             // 是否可以上一页
  onPageChange: (newPageNumber: number) => void;
  onPageSizeChange: (newPageSize: number) => void;
  // 查询总数所需的参数
  executePayload?: QueryBase;     // 执行参数
  txMode?: 'auto' | 'manual';     // 事务模式
  // 新增：集成 ResultPagSizeSelector 的功能
  activeTabKey?: string;          // 当前活跃的 tab key
  settingPageSize?: number;       // 系统设置的页面大小
  // 新增：从父组件传递的总数
  totalFromParent?: number | null; // 父组件计算的总数（N+1分页时自动计算）
  // 新增：从后端返回的筛选 SQL（用于判断是否为过滤模式）
  filterSql?: string | null;
}

const N1Pagination: React.FC<N1PaginationProps> = memo(({
  currentPageNumber,
  pageSize,
  startRowInPage,
  endRowInPage,
  hasNextPage,
  isLoading = false,
  canGoPrev,
  onPageChange,
  onPageSizeChange,
  executePayload,
  txMode = 'auto',
  activeTabKey,
  settingPageSize,
  totalFromParent,
  filterSql
}) => {
  const { t } = useTranslation();
  const { handleExternalInfo } = useHandleExternalInfo();
  const dispatch = useDispatch();
  const { tabInfoMap } = useSelector((state) => state.queryTabs);
  
  // 查询总数相关
  const [hasRequiredTotalCount, setHasRequiredTotalCount] = useState(false);
  const [prevFilterSql, setPrevFilterSql] = useState<string | null>(filterSql || null);
  const {
    data: fetchTotalResponse,
    run: queryingCount,
    loading: queryingCountLoading,
  } = useRequest(getCountOfQueryResult, {
    manual: true,
  });

  // 监听 filterSql 变化，重置相关状态
  React.useEffect(() => {
    if (filterSql !== prevFilterSql) {
      // 筛选条件变化时重置总数查询状态
      setHasRequiredTotalCount(false);
      setPrevFilterSql(filterSql || null);
    }
  }, [filterSql, prevFilterSql]);

  // 集成 ResultPagSizeSelector 的页面大小选项逻辑
  const DEFAULT_PAGESIZE_OPTIONS = [100, 200, 500, 1000];
  
  const pageSizeOptions: number[] = useMemo(() => {
    if (settingPageSize) {
      // 筛选出不超过 settingPageSize 的预设选项
      const realPageSize = settingPageSize - 1
      const filteredOptions = DEFAULT_PAGESIZE_OPTIONS.filter(option => option <= realPageSize);

      // 如果 settingPageSize 不在预设选项中且小于 1000，添加到选项中
      if (!DEFAULT_PAGESIZE_OPTIONS.includes(realPageSize) && realPageSize < 1000) {
        return [...filteredOptions, realPageSize].sort((a, b) => a - b);
      }
      
      return filteredOptions;
    }
    return DEFAULT_PAGESIZE_OPTIONS;
  }, [settingPageSize]);

  // 处理查询总数
  const handleQueryCount = () => {
    if (!executePayload) return;
    
    const params = {
      ...executePayload,
      autoCommit: txMode === 'auto'
    };
    
    // 如果有 filterSql（过滤模式），则使用它替换 statements
    if (filterSql) {
      (params as any).statements = [filterSql];
    }
    
    queryingCount(params).then(() => {
      setHasRequiredTotalCount(true);
    });
  };

  // 获取实际总数
  const realTotalCount = React.useMemo(() => {
    if (fetchTotalResponse) {
      handleExternalInfo({ type: 'LOG', msg: fetchTotalResponse.executeLogInfo.message });
      return fetchTotalResponse.count;
    }
    return null;
  }, [fetchTotalResponse, handleExternalInfo]);

  // 智能总数显示逻辑：优先使用父组件传递的总数
  const displayTotal = React.useMemo(() => {
    // 1. 优先使用父组件传递的总数（N+1分页自动计算的准确总数）
    if (totalFromParent !== null && totalFromParent !== undefined && totalFromParent >= 0) {
      return totalFromParent;
    }
    // 2. 其次使用用户主动查询的总数
    if (hasRequiredTotalCount && realTotalCount !== null) {
      return realTotalCount;
    }
    // 3. 都没有则返回 null
    return null;
  }, [totalFromParent, hasRequiredTotalCount, realTotalCount]);

  // 计算实际的结束行号：考虑筛选条件变化和N+1分页逻辑
  const actualEndRowInPage = React.useMemo(() => {
    // 当没有下一页时，无论在第几页，都只显示实际的行数
    // 这适用于：
    // 1. 第一页且数据总数小于分页大小
    // 2. 筛选条件变化后，当前页是最后一页
    // 3. N+1分页逻辑中，当前页已经是最后一页
    if (!hasNextPage) {
      return endRowInPage;
    }
    
    // 有下一页时，显示完整的分页大小范围
    return endRowInPage;
  }, [hasNextPage, endRowInPage]);

  // 处理页面大小变化（集成 ResultPagSizeSelector 的 Redux 更新逻辑）
  const handlePageSizeChange = (newPageSize: number) => {
    if (newPageSize !== pageSize) {
      // 1. 更新 Redux store（参考 ResultPagSizeSelector 的逻辑）
      if (activeTabKey && tabInfoMap[activeTabKey]) {
        dispatch(updateTabsInfo({
          ...tabInfoMap[activeTabKey],
          resultPageSize: newPageSize
        }));
      }
      
      // 2. 调用原有的页面大小变化回调
      // 这会触发 ResultGridPagination 中的 handlePageSizeChange，
      // 进而更新 curPagination 并调用 handleRefresh，
      // 最终更新 ag-grid 的 cacheBlockSize
      onPageSizeChange(newPageSize);
    }
  };

  // 页容量选择菜单（使用动态选项）
  const pageSizeMenu = (
    <Menu
      style={{ padding: '8px' }}
      onClick={({ key }) => {
        const newPageSize = Number(key);
        handlePageSizeChange(newPageSize);
      }}
    >
      {pageSizeOptions.map(size => (
        <Menu.Item key={size}>
          {size} {t('piece')}/{t('page')}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <div className={styles.paginationContainer}>
      {/* 首页按钮 */}
      <Button
        type="text"
        icon={<img
          src={BigLeftIcon}
          alt={t('firstPage')}
          style={{
            width: '14px',
            height: '14px'
          }}
        />}
        disabled={!canGoPrev || isLoading}
        onClick={() => onPageChange(1)}
        size="small"
        className={styles.paginationButton}
        title={t('firstPage')}
      />

      {/* 上一页按钮 */}
      <Button
        type="text"
        icon={<img
          src={LeftIcon}
          alt={t('previousPage')}
          style={{
            width: '14px',
            height: '14px'
          }}
        />}
        disabled={!canGoPrev || isLoading}
        onClick={() => onPageChange(currentPageNumber - 1)}
        size="small"
        className={styles.paginationButton}
        title={t('previousPage')}
      />

      {/* 可点击的当前行范围显示（集成页容量选择功能） */}
     <Dropdown overlay={pageSizeMenu} trigger={['click']} disabled={isLoading} overlayClassName={styles.pageSizeDropdown}>
       <span
         className={`${styles.pageSizeSelector} ${isLoading ? styles.disabled : ''}`}
         title={t('modifyPageSize')}
       >
         {startRowInPage}-{actualEndRowInPage}
         <img
           src={DownIcon}
           alt={t('modifyPageSize')}
           style={{ width: '10px', height: '10px', marginLeft: '4px' }}
         />
       </span>
     </Dropdown>

      {/* 智能总数显示：优先显示父组件计算的总数 */}
      {displayTotal !== null ? (
        executePayload ? (
          // 当有执行参数时，显示可点击的总数（支持刷新）
          <span
            className={`${styles.totalCount} ${!queryingCountLoading && !isLoading ? styles.clickable : ''}`}
            onClick={() => !queryingCountLoading && !isLoading && handleQueryCount()}
            title={queryingCountLoading || isLoading ? '' : t('refreshTotalCount')}
          >
            /{queryingCountLoading ? t('querying') : displayTotal}
          </span>
        ) : (
          // 无执行参数时，显示静态总数
          <span className={styles.totalCount}>
            /{displayTotal}
          </span>
        )
      ) : (
        hasNextPage && !hasRequiredTotalCount && (
          <span
            className={`${styles.countIndicator} ${executePayload && !queryingCountLoading && !isLoading ? styles.clickable : ''}`}
            onClick={() => executePayload && !queryingCountLoading && !isLoading && handleQueryCount()}
            title={queryingCountLoading || isLoading ? '' : t('queryTotalCount')}
          >
            {queryingCountLoading ? t('querying') : `/${endRowInPage + 1}+`}
          </span>
        )
      )}

      {/* 下一页按钮 */}
      <Button
        type="text"
        icon={<img
          src={RightIcon}
          alt={t('nextPage')}
          style={{
            width: '14px',
            height: '14px'
          }}
        />}
        disabled={!hasNextPage || isLoading}
        onClick={() => onPageChange(currentPageNumber + 1)}
        size="small"
        className={styles.paginationButton}
        title={t('nextPage')}
      />
    </div>
  );
});

export default N1Pagination;
