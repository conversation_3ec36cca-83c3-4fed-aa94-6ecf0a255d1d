import {
  IDatasource,
  IGetRowsParams,
  ColDef
} from '@ag-grid-community/core'
import { QueryBase, executeQuery } from 'src/api'
import { DataSourceType } from 'src/api'

export interface EnhancedDataSourceConfig {
  connectionId: string | number
  dataSourceType: DataSourceType
  databaseName: string
  operatingObject: string
  statement: string
  tabKey: string
  columnDefs: ColDef[]
  pageSize: number
  onError?: (error: any) => void
  onLoading?: (loading: boolean) => void
  onDataUpdate?: (data: any[], totalCount?: number) => void
}

export interface EnhancedRequestParams {
  startRow: number
  endRow: number
  sortModel: any[]
  filterModel: any
  page: number
}

/**
 * 增强的 AG Grid 数据源实现
 * 基于社区版 infinite 行模型，提供类似 ServerSide 的功能和更好的性能
 */
export class EnhancedDatasource implements IDatasource {
  private config: EnhancedDataSourceConfig
  private destroyed = false
  private cache = new Map<string, { data: any[], timestamp: number }>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

  constructor(config: EnhancedDataSourceConfig) {
    this.config = config
  }

  /**
   * AG Grid 调用此方法获取数据
   */
  async getRows(params: IGetRowsParams): Promise<void> {
    if (this.destroyed) {
      params.failCallback()
      return
    }

    try {
      this.config.onLoading?.(true)

      const enhancedParams = this.convertToEnhancedParams(params)

      // 检查缓存
      const cacheKey = this.generateCacheKey(enhancedParams)
      const cachedData = this.getCachedData(cacheKey)

      if (cachedData) {
        console.log('🎯 使用缓存数据:', cacheKey)
        params.successCallback(cachedData.data, this.calculateLastRow(cachedData.data, enhancedParams.startRow))
        this.config.onDataUpdate?.(cachedData.data)
        return
      }

      // 调用后端 API 获取数据
      const response = await this.fetchData(enhancedParams)

      if (response.success) {
        // 缓存数据
        this.setCachedData(cacheKey, response.data)

        const lastRow = this.calculateLastRow(response.data, enhancedParams.startRow, response.totalCount)
        params.successCallback(response.data, lastRow)
        this.config.onDataUpdate?.(response.data, response.totalCount)
      } else {
        console.error('Enhanced datasource fetch failed:', response.error)
        this.config.onError?.(response.error)
        params.failCallback()
      }
    } catch (error) {
      console.error('Enhanced datasource error:', error)
      this.config.onError?.(error)
      params.failCallback()
    } finally {
      this.config.onLoading?.(false)
    }
  }

  /**
   * 将 AG Grid 的 ServerSide 请求转换为后端 API 参数
   */
  private convertToServerSideParams(request: IServerSideGetRowsRequest): ServerSideRequestParams {
    return {
      startRow: request.startRow,
      endRow: request.endRow,
      sortModel: this.convertSortModel(request.sortModel || []),
      filterModel: this.convertFilterModel(request.filterModel || {}),
      groupKeys: request.groupKeys,
      pivotKeys: request.pivotKeys,
      pivotMode: request.pivotMode,
      groupCols: request.groupCols,
      valueCols: request.valueCols,
      pivotCols: request.pivotCols,
    }
  }

  /**
   * 转换排序模型，添加列索引信息
   */
  private convertSortModel(sortModel: any[]): any[] {
    return sortModel.map((item) => ({
      ...item,
      index: this.config.columnDefs.findIndex((col) => col.field === item.colId)
    }))
  }

  /**
   * 转换过滤模型，格式化为后端期望的格式
   */
  private convertFilterModel(filterModel: any): any {
    const formattedFilterModel = Object.keys(filterModel)
      .map((key) => {
        const model = filterModel[key]
        if (model.operator) {
          // 双重过滤
          return {
            field: key,
            type: model.operator,
            condition1: {
              type: model.condition1?.type,
              filter: model.condition1?.filter,
              filterTo: model.condition1?.filterTo,
            },
            condition2: {
              type: model.condition2?.type,
              filter: model.condition2?.filter,
              filterTo: model.condition2?.filterTo,
            },
          }
        } else {
          // 单一过滤
          return {
            field: key,
            type: model.type,
            filter: model.filter,
            filterTo: model.filterTo,
          }
        }
      })
      .filter((item) => item.filter !== undefined && item.filter !== null && item.filter !== '')

    return formattedFilterModel
  }

  /**
   * 调用后端 API 获取数据
   */
  private async fetchData(params: ServerSideRequestParams): Promise<{
    success: boolean
    data?: any[]
    totalCount?: number
    error?: any
  }> {
    try {
      const payload: QueryBase & {
        offset: number
        rowCount: number
        sortModel?: any[]
        filterModel?: any[]
      } = {
        connectionId: this.config.connectionId,
        dataSourceType: this.config.dataSourceType,
        databaseName: this.config.databaseName,
        operatingObject: this.config.operatingObject,
        statements: [this.config.statement],
        tabKey: this.config.tabKey,
        offset: params.startRow,
        rowCount: params.endRow - params.startRow,
        sortModel: params.sortModel,
        filterModel: params.filterModel,
      }

      const response = await executeQuery(payload)
      
      if (response && response.length > 0) {
        const result = response[0]
        
        if (result.executeError) {
          return {
            success: false,
            error: result.executeError
          }
        }

        // 处理数据，添加 editable 字段
        let resultData = this.handleRowDataWithEditable(result.detailedResultData || [])
        
        // Oracle 特殊处理
        if (this.config.dataSourceType === 'Oracle') {
          resultData = this.getExpandedCurResultData(result.detailedResultData || [])
        }

        // 计算总行数
        // 注意：这里需要根据实际的后端 API 响应来调整
        const totalCount = result.totalCount || resultData.length + params.startRow

        return {
          success: true,
          data: resultData,
          totalCount: totalCount
        }
      }

      return {
        success: false,
        error: 'No data returned from server'
      }
    } catch (error) {
      return {
        success: false,
        error: error
      }
    }
  }

  /**
   * 为行数据添加 editable 字段
   */
  private handleRowDataWithEditable(data: any[]): any[] {
    return data.map((row, index) => ({
      ...row,
      editable: true, // 根据业务逻辑设置
      _rowIndex: index
    }))
  }

  /**
   * Oracle 数据扩展处理
   */
  private getExpandedCurResultData(data: any[]): any[] {
    // 这里需要根据原有的 Oracle 处理逻辑来实现
    // 暂时返回原数据，后续需要从原代码中提取具体逻辑
    return this.handleRowDataWithEditable(data)
  }

  /**
   * 刷新数据源
   */
  refresh(): void {
    // ServerSide 模型会自动处理刷新
    // 这个方法可以用于清理缓存或重置状态
  }

  /**
   * 销毁数据源，清理资源
   */
  destroy(): void {
    this.destroyed = true
    // 清理任何正在进行的请求或资源
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ServerSideDataSourceConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}

/**
 * 创建 ServerSide 数据源的工厂函数
 */
export function createServerSideDatasource(config: ServerSideDataSourceConfig): ServerSideDatasource {
  return new ServerSideDatasource(config)
}
