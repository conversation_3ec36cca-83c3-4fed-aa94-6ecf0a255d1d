import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { <PERSON><PERSON><PERSON>, Dropdown, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lide<PERSON> } from 'antd'
import { Iconfont, LinkButton } from 'src/components'
import classNames from 'classnames'
import styles from './toolbar.module.scss'
import Service from 'src/service'
import { PaneType } from '../../queryTabs/queryTabsSlice'
import { DataSourceType, getCountOfQueryResult, QueryBase, getUserConfig } from 'src/api'
import { useRequest, useSelector } from 'src/hook'
import { useHandleExternalInfo } from 'src/hook/useHandleExternalInfo'
import { EyeInvisibleOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import N1Pagination from './components/ResultGirdPagination'
import { SearchOptions } from './ResultSearchBar'

const toolbarActionTypes = [
  'refresh',
  'add',
  'delete',
  'confirm',
  'cancel',
  'view',
  'plaintext',
  'save',
  'applyDesens',
  'search',
] as const
export type ToolbarActionType = typeof toolbarActionTypes[number]

interface ToolbarAction {
  action: ToolbarActionType
  title: string
  isDivider?: boolean
  iconName: string
}

interface IToolTipContent {
  condition: boolean
  title: string
}

interface ToolbarProps {
  showActions?: boolean
  enabledActions?: ToolbarActionType[]
  readOnly?: boolean
  onRefresh?: () => void
  onAddRow?: () => void
  onDeleteRow?: () => void
  onConfirmModify?: () => void
  onScalePage?: (num: number) => void
  onCancelModify?: () => void
  onViewCell?: () => void
  isDataExportPermissionEnabled: boolean
  showExported: boolean
  applyDataExportPermission: () => void
  onOpenExportModal: (key: string) => void
  onOpenFieldsModal?: () => void
  paneType?: PaneType
  refreshable?: boolean
  /** 当前连接的类型**/
  connectionType?: DataSourceType;
  // 执行 SQL 的时候必需的关键参数，获取结果总行数时是对 SQL 语句用COUNT(*)包裹后的结果
  executePayload?: QueryBase
  scale?: number
  type?: string
  permissionResult?: any
  dataExport?: any
  /** 列过滤展示 */
  filterNames?: any[]
  txMode?: 'auto' | 'manual'
  isDesensitized?: boolean
  onSave?: () => void
  onApplyDensens?: () => void
  onSearch?: (searchText: string, options: SearchOptions) => void;
  maskedPathMap?: {[k in string]: boolean} //脱敏字段字段
  setVisibleSearchBox?: (visible: boolean) => void
  isSearchActive?: boolean
  // N+1 分页相关属性
  showPagination?: boolean
  currentPageNumber?: number
  pageSize?: number
  startRowInPage?: number
  endRowInPage?: number
  hasNextPage?: boolean
  isLoading?: boolean
  onPageChange?: (newPageNumber: number) => void
  onPageSizeChange?: (newPageSize: number) => void
  onQueryTotalCount?: () => void
  // 新增：总数传递
  totalFromParent?: number | null
  // 新增：传递给分页组件的参数
  activeTabKey?: string
  settingPageSize?: number
  // 新增：从后端返回的筛选 SQL（用于判断是否为过滤模式）
  filterSql?: string | null
}

export const ResultToolbar: React.FC<ToolbarProps> = (props) => {
  const { t } = useTranslation()
  const [hasRequiredTotalCount, setHasRequiredTotalCount] = useState(false)
  const [isShowFullExportBtn, setIsShowFullExportBtn] = useState<boolean>(true)
  const { handleExternalInfo } = useHandleExternalInfo()
  const { exportSetting = {} } = useSelector((state) => state.login);
  const { exportMenus = [], exportToolPermission = [] } = exportSetting

  const toolbarActions: ToolbarAction[] = [
    {
      action: 'refresh',
      title: t('sdo_refresh'),
      isDivider: true,
      iconName: 'icon-sync-alt',
    },
    {
      action: 'add',
      title: t('add'),
      iconName: 'icon-add-ag',
    },
    {
      action: 'delete',
      title: t('sdo_del'),
      isDivider: true,
      iconName: 'icon-jianqu-copy-copy',
    },
    {
      action: 'confirm',
      title: t('sdo_lock_in'),
      iconName: 'icon-queren-copy1',
    },
    {
      action: 'cancel',
      title: t('sdo_cancel'),
      isDivider: true,
      iconName: 'icon-chehui',
    },
    {
      action: 'view',
      title: t('sdo_view_rows_or_cells'),
      iconName: 'icon-chakan',
    },
    {
      action: 'plaintext',
      title: t('sdo_view_plaintext'),
      iconName: 'icon-chakanmingwen',
    },
    {
      action: 'save',
      title: t('sdo_save'),
      iconName: 'icon-queren-copy',
    },
    {
      action: 'applyDesens',
      title: t('sdo_sensitive_resources'),
      iconName: 'icon-shenqing',
    },
    {
      action: 'search',
      title: t('sdo_search_current_page'),
      iconName: 'icon-search',
    }
  ]
  
  const defaultEnabledActions = toolbarActions.map(({ action }) => action)

  const {
    data: fetchTotalResponse,
    run: queryingCount,
    loading: queryingCountLoading,
  } = useRequest(getCountOfQueryResult, {
    manual: true,
  })

  const { data: userConfigData } = useRequest(
    getUserConfig,
    {
      onSuccess: (res) => {
        // 默认显示 全部导出按钮
        setIsShowFullExportBtn(res?.showFullExport === false ? false : true)
      },
    },
  )


  const realTotalCount = useMemo(
    () => {
      if (fetchTotalResponse) { 
        handleExternalInfo({ type: 'LOG', msg: fetchTotalResponse.executeLogInfo.message })
        return fetchTotalResponse.count
      }
      return
    },
    [fetchTotalResponse, handleExternalInfo],
  )

  const {
    showActions = true,
    enabledActions = defaultEnabledActions,
    readOnly,
    onRefresh,
    onAddRow,
    onDeleteRow,
    onConfirmModify,
    onScalePage,
    onCancelModify,
    onViewCell,
    applyDataExportPermission,
    isDataExportPermissionEnabled,
    onOpenExportModal,
    onOpenFieldsModal,
    onSave,
    onApplyDensens,
    paneType,
    showExported,
    refreshable,
    connectionType,
    executePayload,
    scale = 100,
    type,
    permissionResult,
    filterNames,
    txMode,
    isDesensitized,
    maskedPathMap = {},
    setVisibleSearchBox,
    isSearchActive = false,
    // N+1 分页相关属性
    showPagination = false,
    currentPageNumber = 1,
    pageSize = 20,
    startRowInPage = 1,
    endRowInPage = 20,
    hasNextPage = false,
    isLoading = false,
    onPageChange,
    onPageSizeChange,
    totalFromParent,
    activeTabKey,
    settingPageSize,
    filterSql,
  } = props

  // exportFlow值为false时，表示导出是由权限控制的
  const { exportSelectValue, exportDefault, exportFlow} = permissionResult || {}

  const isActionEnable = useCallback(
    (action: ToolbarActionType) => {
      /** db2 时 禁用其他操作 只返回刷新**/
      if (connectionType === "DB2") {
        return action === 'refresh'
      }
      
      /** 星环 不支持增加和删除 **/
      if (connectionType === 'Inceptor' || connectionType === 'StarRocks') {
        if (action === 'delete' || action === 'add') return false
      }
      // 只读情况下（editable为false），新增行和删除行不可用
      if (readOnly) {
        return !['delete', 'add', 'save', 'confirm', 'cancel'].includes(action)
      }
      //可申请敏感资源
      if (maskedPathMap && Object.keys(maskedPathMap).some(key => !maskedPathMap[key])) return true;

      // view 不需要和其他权限比如删除等绑定到一起
      if ((paneType !== 'tSql') || action === 'view') {
        return (refreshable !== undefined && refreshable) && enabledActions.includes(action)
      } else {
        return !readOnly && enabledActions.includes(action)
      }
    },
    [enabledActions, readOnly, paneType, refreshable],
  )

  const getToolbarActionIcon = (
    action: ToolbarActionType,
    iconName: ToolbarAction['iconName'],
  ) => {
    let icon = null
    switch (action) {
      case 'refresh':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              !isActionEnable('refresh') && styles.disabled,
            )}
            onClick={() => {
              if (isActionEnable('cancel') && onCancelModify) {
                onCancelModify()
              }
              if (isActionEnable('refresh') && onRefresh) {
                onRefresh()
              }
            }}
          />
        )
        break
      case 'add':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              !isActionEnable('add') && styles.disabled,
            )}
            onClick={() => {
              if (isActionEnable('add') && onAddRow) onAddRow()
            }}
          />
        )
        break
      case 'delete':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              !isActionEnable('delete') && styles.disabled,
            )}
            onClick={() => {
              if (isActionEnable('delete') && onDeleteRow) onDeleteRow()
            }}
          />
        )
        break
      case 'confirm':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              !isActionEnable('confirm') && styles.disabled,
            )}
            onClick={() => {
              if (isActionEnable('confirm') && onConfirmModify)
                onConfirmModify()
            }}
          />
        )
        break
      case 'cancel':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              !isActionEnable('cancel') && styles.disabled,
            )}
            onClick={() => {
              if (isActionEnable('cancel') && onCancelModify) onCancelModify()
            }}
          />
        )
        break
      case 'view':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              !isActionEnable('view') && styles.disabled,
            )}
            onClick={() => {
              onViewCell && onViewCell()
            }}
          />
        )
        break
      case 'plaintext':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
            )}
            onClick={() => {
              onOpenFieldsModal && onOpenFieldsModal()
            }}
          />
        )
        break
      case 'save':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              !isActionEnable('save') && styles.disabled,
            )}
            onClick={() => {
              if(isActionEnable('save') && onSave){
                onSave()
              }
            }}
          />
        )
        break;
      case 'applyDesens':
        icon = (
          <Iconfont /** icon-lock 有脱敏 **/
          type={ Object.keys(maskedPathMap).every(key => !!maskedPathMap[key]) ? "icon-lock_sensitive" : "icon-lock"}
          className={styles.toolbarIcon}
          onClick={() => {
            //敏感申请都通过的不可以再次申请
            if(isActionEnable('applyDesens') && !Object.keys(maskedPathMap).every(key => !!maskedPathMap[key]) && onApplyDensens){
              onApplyDensens()
            }
          }}
        />
        )
        break;
      case 'search':
        icon = (
          <Iconfont
            type={iconName}
            className={classNames(
              styles.toolbarIcon,
              isSearchActive && styles.activeSearchIcon
            )}
            onClick={() => {
              if (setVisibleSearchBox) {
                setVisibleSearchBox(!isSearchActive);
              }
            }}
          />
        )
        break;
    }
    return icon
  }

  const handleExportClick = (key: any) => {
    onOpenExportModal(key)
  }

  const handleQueryCount = () => {
    const params = {
      ...executePayload,
      autoCommit: txMode === 'auto'
    }
    queryingCount(params).then(() => {
      setHasRequiredTotalCount(true)
    })
  }

  const ToolTipContent = ({ condition, title }: IToolTipContent) => {
    return condition && !exportFlow ? <Tooltip
      title={
        <Button onClick={() => applyDataExportPermission()} size="small" type="link">
          {t('sdo_req_export_permiss')}
        </Button>
      }
      overlayClassName={styles.exportTooltip}
      placement="bottom"
    >
      {title}
    </Tooltip> : <>{title}</>
  }

  const ExplainToolTipContent = ({title}: {title: string}) => {
    return <Tooltip
      title={t('sdo_result_export_explain_noPerm')}
      placement="bottom"
    >
      {title}
    </Tooltip>
  }

  // 后端接口未翻译该值：user/sys/query-export-setting exportMenus
  const exportRender = () => {
   
    //执行计划展示
    if (type === 'explain') {
     return (
      <Menu onClick={({ key }) => handleExportClick(key as any)}>
         <Menu.Item key="ResultExport" disabled={true}>
          <ExplainToolTipContent title={t('sdo_regular_export')} />
        </Menu.Item>
        {connectionType !== "MongoDB" && <Menu.Item key="SelectedResultExport" disabled={!exportSelectValue}>
          <ToolTipContent condition={!exportSelectValue} title={t('sdo_select_export')} />
        </Menu.Item>}
        { <Menu.Item key="ResultAllExport" disabled={true}>
          <ExplainToolTipContent title={t('sdo_export_all')} />
        </Menu.Item>}
      </Menu>
     )
    }
    return (
      (
        <Menu onClick={({ key }) => handleExportClick(key as any)}>
          {(showExported) && <Menu.Item key="ResultExport" disabled={!exportDefault}>
            <ToolTipContent condition={!exportDefault} title={t('sdo_regular_export')} />
          </Menu.Item>}
          {connectionType !== "MongoDB" && <Menu.Item key="SelectedResultExport" disabled={!exportSelectValue}>
            <ToolTipContent condition={!exportSelectValue} title={t('sdo_select_export')} />
          </Menu.Item>}
          {isShowFullExportBtn && <Menu.Item key="ResultAllExport" disabled={!exportDefault}>
            <ToolTipContent condition={!exportDefault} title={t('sdo_export_all')} />
          </Menu.Item>}
        </Menu>
      )
    )
  }

  const applyDataExportPermissionRender = useMemo(() =>
    Service.moduleService.isModuleExist('/flow') &&
      !exportFlow &&
      !(
        (exportToolPermission?.length === 1 && exportToolPermission?.[0] === 'CQ_DATA_EXPORT_STRUCTURE') ||
        exportToolPermission?.length === 0
      )
      ? (
        <Button onClick={() => applyDataExportPermission()} size="small" type="link">
          {t('sdo_req_export_permiss')}
        </Button>
      ) : <span className={styles.exportButton}>{t('sdo_no_export_permission')}</span>,
    [applyDataExportPermission, exportFlow, JSON.stringify(exportToolPermission), t])

  return (
    <div className={styles.toolbar}>
      <div className={styles.toolbarLeft}>
        {
          showActions &&
          toolbarActions.map(({ action, title, isDivider, iconName }) => {
            if((action === 'plaintext' && !isDesensitized) || (action === 'applyDesens' && maskedPathMap && !Object.keys(maskedPathMap)?.length)){
              return undefined
            }
            if(action === 'search' && !showPagination){
              return undefined
            }
            return <span key={action}>
              <Tooltip title={title} placement="bottomLeft">
                {getToolbarActionIcon(action, iconName)}
              </Tooltip>
              {isDivider && (
                <Divider type="vertical" className={styles.divider}></Divider>
              )}
            </span>
          })
        }
        {/* N+1 分页组件 */}
        {showPagination && onPageChange && onPageSizeChange && (
          <N1Pagination
            currentPageNumber={currentPageNumber}
            pageSize={pageSize}
            startRowInPage={startRowInPage}
            endRowInPage={endRowInPage}
            hasNextPage={hasNextPage}
            isLoading={isLoading}
            canGoPrev={currentPageNumber > 1}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
            executePayload={executePayload}
            txMode={txMode}
            activeTabKey={activeTabKey || executePayload?.tabKey}
            settingPageSize={settingPageSize}
            totalFromParent={totalFromParent}
            filterSql={filterSql}
          />
        )}
      </div>
      <div className={styles.toolbarRight}>
        {
        type !== 'explain' && 
          <div className={styles.scaleWrapper}>
            <div className={styles.scaleContent}>
              <Tooltip title={t('sdo_zoom_out')}>
                <Iconfont
                  type="icon-jianqu-copy-copy"
                  className={classNames(styles.toolbarIcon,
                    scale <=70 &&styles.disabled
                  ) }
                  onClick={() => onScalePage  && scale >70 && onScalePage(scale - 10)}
                />
              </Tooltip>
              <Slider
                value={scale}
                max={200}
                min={70}
                step={10}
                className={styles.toolbarSilder}
                tipFormatter={(val?: number) => `${val}x`}
                onChange={(val: number) => {
                  if (onScalePage) onScalePage(val)
                }}
              />
              <Tooltip title={t('sdo_zoom_in')}>
                <Iconfont
                  type="icon-add-ag"
                  className={classNames(styles.toolbarIcon,
                    scale >=200&& styles.disabled ) }
                  onClick={() => scale <200 && onScalePage && onScalePage(scale + 10)}
                />
              </Tooltip>
            </div>
          </div>
        }
      {/* 有isDataExportPermissionEnabled导出权限且系统设置中有权限 */}
      {isDataExportPermissionEnabled ?
        <Dropdown overlay={exportRender} overlayClassName={styles.pageSizeDropdown}>
          <Iconfont
            type="icon-xiazai"
            className={classNames(styles.toolbarIcon)}
          />
        </Dropdown>
        :!isDataExportPermissionEnabled && exportMenus.length === 0 ?
        <></>
        :!isDataExportPermissionEnabled && exportMenus.length > 0 &&
        applyDataExportPermissionRender
        }
        {filterNames && filterNames?.length > 0 &&
          <Tooltip title={`${t('sdo_column_filtering_fields')}：${filterNames}`}>
            <EyeInvisibleOutlined style={{ fontSize: '16px' }} />
          </Tooltip>
        }
        {refreshable && connectionType !== 'Hive' && connectionType !== 'Inceptor' && !showPagination &&
          (hasRequiredTotalCount ? (
            <span>{`${t('sdo_result_total_rows')}: ${realTotalCount || 0}`}</span>
          ) : (
            <LinkButton
              onClick={handleQueryCount}
              loading={queryingCountLoading}
              // 与工具栏的样式风格统一
              className={styles.toolbarIcon}
              style={{ fontSize: 14, fontWeight: 'normal', padding: '0 5px' }}
            >
              {t('sdo_query_result_total_rows')}
            </LinkButton>
        ))}
      </div>
    </div>
  )
}
