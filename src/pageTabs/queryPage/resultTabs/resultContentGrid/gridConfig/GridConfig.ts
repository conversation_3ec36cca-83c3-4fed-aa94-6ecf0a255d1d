import { AgGridReactProps } from '@ag-grid-community/react'
import { ColDef } from '@ag-grid-community/core'
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model'
import locale from './locale'
import locale_en from './locale_en'
import { BLOCK_SIZE } from 'src/constants'
import {
  BooleanEditor,
  SelectEditor,
  SelectInputEditor,
  SimpleMarkRenderer,
  SimpleTextRenderer,
  TextEditor,
  ColumnSelectEditor,
  ColumnMultiSelectEditor,
  ColumnSpecialMultiSelector,
  AntdSelectEditor,
  NumericEditor,
  RowIndexRenderer,
  SimpleTextRendererWithContextMenu,
  RowIndexRendererWithContextMenu,
  RowIndexFromPropsRendererWithContextMenu
} from 'src/components'

export const GridConfigBase = (sys_locales: string): AgGridReactProps => {
  return (
    {
      localeText: sys_locales === 'zh'? locale : locale_en,
      rowHeight: 28,
      headerHeight: 32,
      rowSelection: 'multiple',
      tooltipShowDelay: 500,
      enableCellTextSelection: true,
      // ensureDomOrder: true,
      components: {
        // renderer
        booleanRenderer: SimpleMarkRenderer,
        simpleTextRenderer: SimpleTextRenderer,
        SimpleTextRendererWithContextMenu,
        RowIndexRenderer,
        RowIndexRendererWithContextMenu,
        RowIndexFromPropsRendererWithContextMenu,
        // editor
        textEditor: TextEditor,
        selectEditor: SelectEditor,
        selectInputEditor: SelectInputEditor,
        booleanEditor: BooleanEditor,
        columnSelectEditor: ColumnSelectEditor,
        columnMultiSelectEditor: ColumnMultiSelectEditor,
        columnSpecialMultiSelector: ColumnSpecialMultiSelector,
        antdSelectEditor:AntdSelectEditor,
        numericEditor:NumericEditor
      },
      /**
       * If true, then dots in field names (e.g. address.firstline) are not treated as deep references.
       * Allows you to use dots in your field name if you prefer.
       */
      suppressFieldDotNotation: true,
      animateRows: false,
      // 自定义选择配置 - 禁用默认选择行为
      suppressRowClickSelection: true,  // 禁用默认行选择
      enableRangeSelection: false,      // 禁用AG Grid内置区域选择
      // suppressClickEdit: true,
    }
  )
}

export const defaultColDef: ColDef = {
  editable: true,
  resizable: true,
  suppressKeyboardEvent: (params) => {
    const { editing, event } = params
    // 非编辑状态下禁用退格键
    return !editing && event.code === 'Backspace'
  },
  tooltipValueGetter: ({ value }) => value,
  cellClass: ({ colDef }) => {
    if (!colDef?.headerComponentParams){
      return 'aggrid-cell'
    }
    if (colDef?.headerComponentParams.isDesens) {
      return 'aggrid-cell aggrid-cell-isDesens'
    }
    return 'aggrid-cell'
  },
}

export const infiniteModeOptions = {
  modules: [InfiniteRowModelModule],
  editType: 'fullRow' as const,
  rowModelType: 'infinite' as const,
  infiniteInitialRowCount: 1,
  maxBlocksInCache: 0, // 不缓存数据块，避免内存累积
  rowBuffer: 50, // 进一步减少缓冲区，配合防抖使用 问题的关键
  cacheOverflowSize: 0, // 不允许缓存溢出
  // blockLoadDebounceMillis: 150, // 适当的防抖延迟，配合滚动防抖
  // 启用虚拟化优化
  suppressRowVirtualisation: false, // 确保行虚拟化开启
  suppressColumnVirtualisation: false, // 确保列虚拟化开启
  // 🧪 事件优化配置，减少性能记录和DOM操作
  // suppressScrollOnNewData: true, // 新数据时不自动滚动，减少滚动事件
  // debounceVerticalScrollbar: true, // 🎯 关键！防抖垂直滚动条事件，减少detached DOM
  // suppressHorizontalScroll: false, // 保持水平滚动
  // suppressScrollOnNewData: false, // 允许新数据时滚动
  // 🧪 添加更多防抖选项
  // scrollbarWidth: 17, // 明确滚动条宽度，避免重复计算
}

// 🧪 实验性配置：移除所有自定义组件的版本
export const infiniteModeOptionsNoCustomComponents = {
  ...infiniteModeOptions,
  // 禁用所有自定义组件相关功能
  suppressContextMenu: true, // 禁用右键菜单
  suppressRowClickSelection: false, // 保持行选择
  suppressCellSelection: false, // 保持单元格选择
}

export { BLOCK_SIZE }