import React, { useMemo, useEffect, useState, useCallback, useRef } from 'react'
import { Form, Input, Spin, Switch, Toolt<PERSON>, Button } from 'antd'
import { GridApi, ColDef } from '@ag-grid-community/core'
import useRequest from '@ahooksjs/use-request'
import { ModalProps } from 'antd/lib/modal'
import classNames from 'classnames'
import { isEmpty } from 'lodash'
import { ResizeTableSome, UIModal } from 'src/components'
import EwResize from './components/EwResize'
import {
  CellRenderType,
  PlainRowData,
  CellRenderResultData,
  RowData,
} from 'src/api'
import styles from './cellViewer.module.scss'
import { getImageSrcFromHex, setTooltipTitleSty } from 'src/util'
import {
  RowViewer_ColumnsTitle,
  RowViewer_ColumnsTitleWidth,
  RowViewer_NoShowIndex
} from 'src/constants'
import { useTranslation } from 'react-i18next'

const ReadOnlyTypes: CellRenderType[] = [
  'base64-image',
  'base64-jpg',
  'base64-png',
  'binary',
  'binary-image',
  'binary-jpg',
  'binary-png',
]

export const isCellTag = (value: string) => {
  if (value.length > 50) return false
  if (!~value.indexOf('<')) return false
  const CellTagRegex = /([0-9] bytes|\.[0-9]{2} (KB|MB|GB)|too large)/
  return CellTagRegex.test(value)
}

interface CellViewerModalProps {
  gridApi: GridApi | null
  rowData?: Record<string, unknown>
  editable: boolean;
  resultData: RowData[]
  visible: boolean
  rowIndex?: number | null
  setVisible: (visible: boolean) => void
  updateFocusedCell: (newData: PlainRowData, oldData: PlainRowData) => void
  downloadFocusedCell: (newData: PlainRowData, oldData: PlainRowData) => void
  fetchFocusedCell: (
    newData: PlainRowData,
    oldData: PlainRowData,
  ) => Promise<CellRenderResultData[]>
  nextRowIndex?: () => void
  lastRowIndex?: () => void
  isLastRowIndex?: boolean
  setRowViewerResultData?: () => void
  resultAllowCopy?: boolean;
}

interface columnsProps {
  dataIndex: string,
  key: string,
  title: string | null,
  width?: number | string,
  [p: string]: any,
  render: (text: any, record: any) => any
}

export const RowViewerModal: React.FC<CellViewerModalProps> = (props) => {
  const {
    gridApi,
    rowData,
    visible,
    resultData,
    setVisible,
    updateFocusedCell,
    fetchFocusedCell,
    editable,
    rowIndex,
    nextRowIndex,
    lastRowIndex,
    isLastRowIndex,
    setRowViewerResultData,
    resultAllowCopy,
  } = props

  const { t } = useTranslation()

  const {
    data: completeRow,
    run: tryGetCompleteCell,
    loading: loadingGetCompleteCell,
  } = useRequest(
    () => {
      const oldRowData = getCurrentRow()
      const fields = Object.keys(oldRowData)
      if (!fields || !fields.length) return
      const detailRowData = Promise.all(
        fields.map((field) => fetchCellDetail(field, oldRowData)),
      )
      return detailRowData
    },
    { manual: true },
  )

  useEffect(() => {
    if (!visible) return
    rowEditorForm.resetFields()
    tryGetCompleteCell()
  }, [tryGetCompleteCell, visible, rowIndex])

  //查询resultData信息
  const getResultDetailCell = (field: string) => {

    const focusedCell:  any = gridApi?.getFocusedCell();
    const { rowIndex } = focusedCell;
    //结果集resultData cell renderType
    const cellContentDetail = resultData?.[rowIndex]?.[field];
    return cellContentDetail?.renderType || ''
  }
  
  const fetchCellDetail = async (field: string, oldRowData: any) => {
    const oldValue = oldRowData[field]
    if (isCellTag(String(oldValue))) {
      return fetchFocusedCell({ [field]: 'DoesNotMatter' }, oldRowData).then(
        (res) => {    
          if (!res[0]) return { key: field, value: '', brief: oldValue }
          const { resultData } = res[0]
          return { key: field, ...resultData[0]?.[field], brief: oldValue }
        },
      )
    } else {
      const renderType = getResultDetailCell(field);
      return new Promise((resolve) =>
        resolve({ key: field, value: oldValue, brief: oldValue, renderType: renderType }),
      ) as Promise<any>
    }
  }

  const getCurrentRowInfo = useCallback(() => {
    if (!gridApi) return
    const columnDefs: ColDef[] = (gridApi
      .getColumnDefs() || []) //oracle 自定义父节点单独设置了headerName所以不能过滤
      .filter((el) => !(el.hasOwnProperty('headerName') && !el?.headerName))
    if (columnDefs.length < 1) return

    const columnsInfo = columnDefs.map((col) => {
      const data = Object.assign(
        {},
        { dataType: col.cellEditorParams.cellEditorType },
        {
          comment: col.headerComponentParams.comment || '',
        },
      )
      return Object.assign({}, { colId: col.colId }, { data: data })
    })

    const currentRowColumnData = rowData || {}
    const keyArr = Object.keys(currentRowColumnData)

    const rowInfoData = keyArr.map((key) => {
      const index = columnsInfo.findIndex(({ colId }) => colId === key)
      return { [key]: currentRowColumnData[key], ...columnsInfo[index]?.data }
    })

    return rowInfoData
  }, [gridApi, rowData])

  const getCurrentRow = () => {
    return rowData || {}
  }

  const handleSaveCell = async () => {
    const values = await rowEditorForm.getFieldsValue(
      true,
      ({ touched }) => touched,
    )
    if (!isEmpty(values)) {
      const oldRowData = getCurrentRow()
      updateFocusedCell(values, oldRowData)
      setRowViewerResultData?.()
    }
    setVisible(false)
  }

  const [isEditing, setIsEditing] = useState(false)

  const ButtonProps: Partial<ModalProps> = {
    okText: t('sdo_ok'),
    okButtonProps: { onClick: handleSaveCell },
    cancelText: t('sdo_cancel'),
    cancelButtonProps: { onClick: () => setVisible(false) },
  }

  const [rowEditorForm] = Form.useForm()

  const [modalWidth, setModalWidth] = useState(800);

  const modalTitle = (
    <span style={{ display: 'flex'}}>
      <span>
        <span>{t('sdo_line_details')}</span>
        <EwResize
          minWidth={800}
          maxWidth={1200}
          getModalWidth={
            (width: number) => {
              setModalWidth(width)
            }
          }
        />
      </span>
      <Switch
        size="small"
        checkedChildren={t('sdo_edit')}
        unCheckedChildren={t('sdo_view')}
        checked={isEditing}
        disabled={!editable}
        onChange={(v) => setIsEditing(v)}
        style={{ margin: 'auto 4px' }}
      ></Switch>
    </span>
  )

  //特殊处理CQ-1330
  const formatValue = (formatValue: any) => {

    if (formatValue === null) {
      return `<null>`
    }else if (typeof formatValue === 'object' && formatValue !== null) {
      return `<${JSON.stringify(formatValue)}>`
    }else if (typeof formatValue === 'boolean') {
      return formatValue?.toString()
    }else {
      return formatValue ?? ''
    } 
  }

  const tooltipRef = useRef<any>(null)

  const handleVisibleChange = useCallback(() => {
    setTooltipTitleSty(tooltipRef, 'customTooltip');
  }, [tooltipRef])
  
  // 创建 Table columns
  const formatColumns = (rowValues: any) => {
    const cellCanCopySty = resultAllowCopy? "" : styles.notSelect;
    const tooltipSty = classNames("customTooltip", resultAllowCopy ? '' : styles.notSelect);
    let columnsTmp: columnsProps[] = [];
    let dataSourceTmp: any[] = [];
    if (rowValues.length > 0) {
      const len = Object.keys(rowValues[0]).length * 2;
      for (let i = 0; i < len; i++) {
        if (!RowViewer_NoShowIndex.includes(i)) {
          columnsTmp.push({
            dataIndex: i.toString(),
            key: i.toString(),
            title: t(RowViewer_ColumnsTitle?.[i.toString()]),
            width: RowViewer_ColumnsTitleWidth?.[i.toString()],
            ellipsis: true,
            fixed: (i === 0) ? true : false, 
            render: (text: any, record: any) => {
              return (
                <Tooltip
                  ref={tooltipRef}
                  title={<div className={tooltipSty}>{text}</div>}
                  placement='topLeft'
                  overlayStyle={{ whiteSpace: 'pre-line' }}
                  onVisibleChange={handleVisibleChange}
                >
                  <span className={cellCanCopySty}>{text}</span>
                </Tooltip>
              )
            },
          });
        }
      }
      dataSourceTmp = formatDatasource(rowValues);
    }
    return {
      columns: columnsTmp,
      dataSource: dataSourceTmp
    }
  }

  // 构造 Table datasource
  const formatDatasource = (
    rowValues: any, 
  ) => {
    let resArr = []
    for (let i = 0; i < rowValues.length; i++) {
      const arr = Object.entries(rowValues[i]).reduce((acc: any, [key, value] : any) => {
        let defaultContent = formatValue(value);

        if (['binary-image','binary-jpg','binary-png', 'binary-gif'].includes(getResultDetailCell(key))) {
          const src = getImageSrcFromHex(value);
          defaultContent =   <img
            src={src}
            alt=""
            style={{ width: '100%' }}
            onContextMenu={(e) => {
              e.preventDefault();
            }}
          ></img>
        }
        acc.push(`${key}`, defaultContent);
        return acc;
      }, []);

      resArr.push(arr);
    }
    return resArr
  }

  const getRenderedContentInfo = useCallback(() => {
    const rowValues = getCurrentRowInfo() as any
    if (!rowValues) return
    const {columns, dataSource} = formatColumns(rowValues)
    return (
      <>
      <ResizeTableSome
        columns={columns}
        rowKey={(record: any) => record?.key}
        dataSource={dataSource}
        pagination={false}
        bordered={true}
        autoWidthField={['0', '1', '3', '5']}
        className={styles.rowTable}
        scroll={{y: 350}}
      />
      <div style={{float: 'right',marginTop: 10}}>
        {
          (rowIndex || rowIndex === 0) && 
          <span>{t('sdo_current_row')}&nbsp;{rowIndex + 1}</span>
        }
          <Button
            type='link'
            disabled={rowIndex === 0}
            onClick={() => lastRowIndex?.()}
          >
            {t('sdo_prev_row_detl')}
          </Button>
          <Button
            type='link'
            onClick={() => nextRowIndex?.()}
            disabled={isLastRowIndex}
          >
            {t('sdo_next_row_detl')}
          </Button>
      </div>
      </>
    )
  }, [getCurrentRowInfo, rowIndex, isLastRowIndex, t])

  const renderContentInfo = useMemo(() => {
    return getRenderedContentInfo()
  }, [getRenderedContentInfo])

  const checkCellEditable = (cellCompleteData: any) => {
    const isReadOnly = ReadOnlyTypes.includes(
      cellCompleteData?.renderType || null,
    )
    return cellCompleteData?.editable || !isReadOnly
  }

  const formItemRender = (cellCompleteData: any) => {
    const { key, value } = cellCompleteData
    const valueShow = value?.length > 100 ? value.slice(0, 100) : value
    const cellEditable = checkCellEditable(cellCompleteData)
    return (
      <Form.Item className={styles.cellLabel} name={key} initialValue={valueShow} label={<Tooltip title={key}>{key}</Tooltip>} key={key}>
        <Input style={{ width: '100%' }} disabled={!cellEditable}></Input>
      </Form.Item>
    )
  }

  return (
    <UIModal
      title={modalTitle}
      visible={visible}
      onCancel={() => setVisible(false)}
      width={modalWidth}
      afterClose={() => {
        rowEditorForm.resetFields()
        setIsEditing(false)
      }}
      {...ButtonProps}
      className={styles.rowViewerModalSty}
    >
      <div
        style={isEditing? {
          display: 'flex',
          justifyContent: 'center',
          wordBreak: 'break-all',
          overflow: 'hidden',
        }:{}}
      >
        <Spin
          wrapperClassName={classNames(styles.fullWidth)}
          spinning={loadingGetCompleteCell}
        >
          {!isEditing && renderContentInfo}
          {isEditing && (
            <Form
              form={rowEditorForm}
              style={{ width: '100%' }}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 18 }}
            >
              {completeRow?.map((cellCompleteData) =>
                formItemRender(cellCompleteData),
              )}
            </Form>
          )}
        </Spin>
      </div>
    </UIModal>
  )
}
