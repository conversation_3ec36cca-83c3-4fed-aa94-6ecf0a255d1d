import {
  ColDef,
  Column,
  GridReadyEvent,
  IDatasource,
  IGetRowsParams,
  RowEditingStartedEvent,
  RowEditingStoppedEvent,
} from '@ag-grid-community/core'
import type { CellKeyDownEvent, ICellRendererParams } from '@ag-grid-community/core'
import { AgGridReact } from '@ag-grid-community/react'
import { message, Modal, Spin } from 'antd'
import classNames from 'classnames'
import { cloneDeep, isEmpty, isEqual } from 'lodash'
import React, { useCallback, useEffect, useMemo, useRef, useState, memo } from 'react'
import {
  ActioneType,
  downloadGridCell,
  executeSqlSegment,
  executeSqlStatement,
  explainSqlStatement,
  getCompleteCell,
  makeResultAllExport,
  PlainRowData,
  QueryBase,
  QueryResult,
  ResultColumnInfo,
  ResultModify,
  getSegmentResults,
  QueryParams,
  formatAndModifyResult,
} from 'src/api'
import { CustomHeader, Iconfont, indexColDef, indexColId } from 'src/components'
import type { TextEditorProps, IGridContext } from 'src/components'
import { useDispatch, useSelector, useRequest } from 'src/hook'
import { openFlowForm } from 'src/pageTabs/flowPages/flowFormsSlice'
import {
  setPageTotalByQueryKey,
  updateResultTabs,
  setActiveResultTabKey,
  setDoubleClickCell
} from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import Service from 'src/service'
import {
  activePaneInfoSelector,
  pushMonacoValue,
  setTabExecutionStatus, setTabExecutionStatusPercentage,
} from '../../queryTabs/queryTabsSlice'
import { useCopyableChange } from '../useCopyableChange'
import { AddResultExportModal } from './AddResultExportModal'
import { AddSelectedResultExportModal } from './AddSelectedResultExportModal'
import { CellViewerModal, isCellTag } from './CellViewerModal'
import styles from './grid.module.scss'
import CustomHeaderStyles from 'src/components/agFrameworkComponents/CustomHeader.module.scss'
import {
  defaultColDef,
  GridConfigBase,
  infiniteModeOptions,
} from './gridConfig/GridConfig'
import { ResultToolbar, ToolbarActionType } from './ResultToolbar'
import { RowViewerModal } from './RowViewerModal'
import { getAlteredRowData, getExpandedCurResultData, getAgGridCellDownKeys } from './util'
import { useGridStatus, IGridStatus } from './useGridStatus'
import { ResultAllExport } from './ResultAllExport'
import {
  saveExecuteActiveTabParams
} from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { useHandleExternalInfo } from 'src/hook/useHandleExternalInfo'
import { DesensitizedFieldsModal } from './DesensitizedFieldsModal'
import { handleCopyCell, handleCopyRow, handleCopyAll, handleCopyCellTitle, handleCopyAllTitle, handleCopyWithTitle, handleCopySelectAllWithTitle, handleCopySelectedAllCellsTitles } from 'src/components/agFrameworkComponents/ResultMenuFunc';
import { copyTextToClipboard } from 'src/util'
import { useTranslation } from 'react-i18next'
import { exportTaskCreatedNot } from 'src/components'
import { useSelectionController } from './hooks/useSelectionController'
export interface FocusedRowData {
  _initData?: any; // 根据需要指定具体类型
  [key: string]: unknown; // 允许其他任意属性
}

interface ResultGridProps {
  tabResultKey?: string
  result: QueryResult
  queryKey: string
  type?: 'explain' | 'execute'
  executePercentage?: number
  executeStatusMessage?: string
  rowNum: number // 分页数
}

let prevCanSave = false // 记录上一次可保存状态
export const ResultGrid: React.FC<ResultGridProps> = memo((props) => {
  const { t } = useTranslation()
  const { locales } = useSelector((state) => state.login)
  const gridConfig = GridConfigBase(locales);
  const dispatch = useDispatch()
  const [rowViewerResultData, setRowViewerResultData] = useState<any>([])
  const [ agGridLoading, setAgGridLoading] = useState<boolean>(false);
  const isSelectAllCancelledRef = useRef<boolean>(true);  // 记录当前是否为取消状态
  const { tabResultKey, result, queryKey, type, rowNum } = props

  const {
    connectionId,
    dataSourceType,
    columnInfos,
    detailedResultData,
    statement,
    statementObject,
    statementObjectWithQuoteCharacter,
    editable,
    operatingObject,
    operatingObjectWithQuoteCharacter,
    dataExport,
    databaseName,
    refreshable,
    unEditableReason,
    scale,
    permissionResult,
    filteredColumns,
    desensitized,
    doubleCheckType,
    isDesensitizedPlaintext,
    currentParams,
    resultData: initCurrentRusultData,
    enableGenerateSql,
    maskedPathMap = {}
  } = result || {}

  // 有脱敏字段并且开启了二次复核
  const isDesensitized = desensitized && ![undefined, null, 'NONE'].includes(doubleCheckType)
  const isExplain = statement.includes('explain')

  const allHotKeys = useSelector(
    (state) => state.setting.hotKeys,
  )
  const { status, dispatchAction } = useGridStatus()
  const [loading, setLoading] = useState<boolean>(true)
  const [actionType, setActionType] = useState<ActioneType>('ROLLING_RESULT')
  const [visibleCellViewer, setVisibleCellViewer] = useState(false)
  const [visibleRowViewer, setVisibleRowViewer] = useState(false)
  const [visibleCommonExport, setVisibleCommonExport] = useState(false)
  const [visibleSelectedExport, setVisibleSelectedExport] = useState(false)
  const [visibleExportAll, setVisibleExportAll] = useState(false)
  const [visibleDesensitizedFields, setVisibleDesensitizedFields] = useState(false)
  const { theme } = useSelector((state) => state.appearance)
  const { plSql } = useSelector(activePaneInfoSelector) || {}
  const { handleExternalInfo } = useHandleExternalInfo()
  const [resultOperationDates, setResultOperationDates] = useState<any[]>([])  // 动态编辑(增删改)结果集数据
  const preEditRowRef = useRef<any>(null)  // 进入编辑状态时暂存编辑前的行数据, 用于 dirty check
  const [focusedRowData, setFocusedRowData] = useState<FocusedRowData | undefined>()
  const [focusedColumn, setFocusedColumn] = useState<Column | null>(null)
  const [focusedRowIndex, setFocusedRowIndex] = useState<number | null>(null)
  const notSupportPageResultDataRef = useRef<any[]>([]); // 不支持分页的所有结果集数据
  const allResultDataRef = useRef<any[]>([]);    // 所有结果集数据
  const cloneResultDataRef = useRef<any[]>([]);    // 结果集克隆数据
  const isInternalDataReloadRef = useRef<boolean>(false); // 标记是否是内部数据重新加载（如删除后的立即刷新）
  const isAfterDeleteRef = useRef<boolean>(false); // 标记是否是删除操作后的状态，用于数据加载
  const [canSave, setCanSave] = useState(false)  // 结果集是否可以保存
  const [canDelete, setCanDelete] = useState(false) //  是否可以删除
  const [enabledActions, setEnabledActions] = useState<any[]>([])
  const [selectedNodes, setSelectedNodes] = useState<any[]>([])
  const [visibleRowCount, setVisibleRowCount] = useState(0);
  // 结果集 allow copy
  const [copyableRef, allowCopy] = useCopyableChange()
  const canCopy = permissionResult?.resultSetCopy && allowCopy;
  //复制单元格权限
  const canCopyCell = permissionResult?.resultCellCopy && allowCopy
  const isFirstRender = useRef(true); // 标识是否首次渲染
  const modelUpdatedHandlerRef = useRef<(() => void) | null>(null); // 保存监听器引用以便后续移除
  
  // 使用自定义选择控制器 hook（无限滚动模式，禁用列选择）
  const {
    customSelectionControllerRef,
    gridContainerRef,
    contextMenuHandlersRef,
    selectionContext,
    contextHandlersReady,
    updateSelectionContext,
    getCellInfoFromMouseEvent,
    handleCellClicked,
    initializeSelectionController,
    setupDragSelection,
    cleanupSelectionController,
  } = useSelectionController({ isScrollLoading: true })

  useEffect(() => {
    let initActions = [...enabledActionsMap[status]]
    if (canSave) {
      initActions.push('save')
    }
    if (canDelete) {
      initActions.push('delete')
    }
    if (focusedRowData && Object.keys(focusedRowData).length) {
      initActions.push('view')
    } else {
      initActions = initActions?.filter((i: string) => i !== 'view')
    }
    setEnabledActions(initActions)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status, canSave, canDelete, focusedRowData])

  useEffect(() => {
    return () => {
      allResultDataRef.current = []
    }
  }, [])

  const formatColumn = (columns: ResultColumnInfo[]) => {
    return columns.map((def) => {
      const {
        columnName,
        comment,
        renderType,
        cellEditor,
        desensitization,
        sortable,
        filterable,
        nullable,
        downloadable,
        editable: columnEditable,
        unEditableReason: columnUnEditableReason,
      } = def
      const isDesens = Service.moduleService.isModuleExist('/flow')
        ? desensitization?.status
        : false

      const cellEditorParams: Pick<
        TextEditorProps,
        'cellEditorType' | 'nullable' | 'renderType' | 'downloadable'
      > = {
        cellEditorType: cellEditor,
        nullable,
        renderType,
        downloadable // 单元格是否支持下载
      }

      return {
        minWidth: 80, // 最小宽度
        field: columnName,
        sortable: !['INSERT', 'UPDATE'].includes(status) && sortable,
        filter: filterable,
        editable: (params: any) => {
          // todo: do not hack
          // 如果是undefined，则消除影响
          let columnEditableFilter =
            columnEditable === undefined ? true : columnEditable
          //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
          if (connectionType === 'DB2') {
            columnEditableFilter = false;
          }
          return (
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value))
            )
          )
        },
        context: {
          editable,
          columnEditable,
          columnUnEditableReason,
          unEditableReason,
          renderType
        },
        cellRenderer: 'SimpleTextRendererWithContextMenu',
        cellEditor: 'textEditor',
        cellEditorParams,
        headerComponentFramework: CustomHeader,
        headerComponentParams: { headerName: columnName, comment, isDesens, copyable: canCopy },
        // 监听双击事件，触发不可编辑的tooltip
        onCellDoubleClicked: (params: any) => {
          const { rowIndex, column } = params
          const { colId } = column
          const columnEditableFilter = columnEditable ?? true
          const cellEditable =
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value)),
            )
          if (!cellEditable) {
            dispatch(setDoubleClickCell({ colId, rowIndex }))
          }
        },
        valueSetter: (params: any) => {
          // 新增_initData属性存储被修改字段初始值
          if (!params.data._initData) {
            params.data._initData = {}
          }
          if (!params.data._initData[columnName]) {
            params.data._initData[columnName] = params.oldValue;
          }
          params.data[columnName] = params.newValue;
          return params.oldValue !== params.newValue;
        },
        valueGetter: (params: any) => {
          return params.data?.[columnName]
        },
        cellClassRules: {
          // 单元格选中高亮 - 排除行头（序号列）
          'ag-cell-selected-manual': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            // 排除序号列（行头）- 使用 indexColId 进行匹配
            if (params.colDef?.colId === indexColId) return false;
            
            // 使用短路求值优化性能：先检查全选状态，如果是全选则直接返回 true
            // 这样可以避免在全选模式下执行更复杂的 isCellSelected 检查
            return customSelectionControllerRef.current.isSelectAllActive() ||
                   customSelectionControllerRef.current.isCellSelected(params.node.rowIndex, columnName);
          },
          // 列选中高亮
          'ag-column-selected-custom': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            return customSelectionControllerRef.current.isColumnSelected(columnName);
          },
        },
      }
    })
  }

  const formatOracleCustomColumn = (columns: ResultColumnInfo[] | any, prefix = '') => {
    return columns.map((def: ResultColumnInfo) => {
      const {
        columnName,
        comment,
        renderType,
        cellEditor,
        desensitization,
        sortable,
        filterable,
        nullable,
        downloadable,
        editable: columnEditable,
        childColumnInfos = [],
        unEditableReason: columnUnEditableReason,
      } = def
      const isDesens = Service.moduleService.isModuleExist('/flow')
        ? desensitization?.status
        : false

      const cellEditorParams: Pick<
        TextEditorProps,
        'cellEditorType' | 'nullable' | 'renderType' | 'downloadable'
      > = {
        cellEditorType: cellEditor,
        nullable,
        renderType,
        downloadable // 单元格是否支持下载
      }

      const curPrefix = prefix ? `${prefix}_${columnName}` : columnName;
      return {
        minWidth: 80, // 最小宽度
        field: curPrefix,
        sortable: !['INSERT', 'UPDATE'].includes(status) && sortable,
        filter: filterable,
        editable: (params: any) => {
          // todo: do not hack
          // 如果是undefined，则消除影响
          let columnEditableFilter =
            columnEditable === undefined ? true : columnEditable;
          //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
          if (connectionType === 'DB2') {
            columnEditableFilter = false;
          }
          return (
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value)),
            )
          )
        },
        context: {
          editable,
          columnEditable,
          columnUnEditableReason,
          unEditableReason,
          renderType
        },
        cellRenderer: 'SimpleTextRendererWithContextMenu',
        cellEditor: 'textEditor',
        cellEditorParams,
        headerName: columnName, //组名
        headerComponent: CustomHeader,
        headerComponentParams: {
          headerName: columnName,
          comment,
          isDesens,
          copyable: canCopy,
        },
        ...(childColumnInfos?.length ? { colId: columnName } : {}), //oracle自定义时候单元行缺少roleId 只有child有
        ...(childColumnInfos?.length ? { children: formatOracleCustomColumn(childColumnInfos, curPrefix) } : {}),
        onCellDoubleClicked: (params: any) => {
          const { rowIndex, column } = params
          const { colId } = column
          const columnEditableFilter = columnEditable ?? true
          const cellEditable =
            editable &&
            columnEditableFilter &&
            renderType !== 'binary' &&
            !Object.values(params?.data || {})?.some((value) =>
              isCellTag(String(value)),
            )
          if (!cellEditable) {
            dispatch(setDoubleClickCell({ colId, rowIndex }))
          }
        },
        valueSetter: (params: any) => {
          // 新增_initData属性存储被修改字段初始值
          if (!params.data._initData) {
            params.data._initData = {}
          }
          if (!params.data._initData[curPrefix]) {
            params.data._initData[curPrefix] = params.oldValue;
          }
          params.data[curPrefix] = params.newValue;
          return params.oldValue !== params.newValue;
        },
        valueGetter: (params: any) => {
          return params.data?.[curPrefix]
        },
        cellClassRules: {
          // 单元格选中高亮
          'ag-cell-selected-manual': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            
            // 使用短路求值优化性能：先检查全选状态，如果是全选则直接返回 true
            // 这样可以避免在全选模式下执行更复杂的 isCellSelected 检查
            return customSelectionControllerRef.current.isSelectAllActive() ||
                   customSelectionControllerRef.current.isCellSelected(params.node.rowIndex, curPrefix);
          },
          // 列选中高亮
          'ag-column-selected-custom': (params: any) => {
            if (!customSelectionControllerRef.current) return false;
            return customSelectionControllerRef.current.isColumnSelected(curPrefix);
          },
        },
      }
    })
  }

  const currentRusultData: any = dataSourceType && dataSourceType === 'Oracle' ? getExpandedCurResultData(detailedResultData) : initCurrentRusultData

  const { tabInfoMap, paneInfoMap, activeTabKey } = useSelector(
    (state) => state.queryTabs,
  )
  const {
    resultTabMap,
  } = useSelector((state) => state.resultTabs)
  const setPageTotal = useCallback((pageTotal: number) => {
    dispatch(setPageTotalByQueryKey({ queryKey, pageTotal }))
  }, [dispatch, queryKey])
  const { copySetting } = useSelector((state) => state.login.userInfo)
  const paneType = tabInfoMap[queryKey].paneType
  const txMode = paneInfoMap[queryKey].txMode
  const { connectionType, pending } = paneInfoMap?.[activeTabKey] ?? {}
  const columnDefs: ColDef[] = connectionType === 'Oracle' ? formatOracleCustomColumn(columnInfos) : formatColumn(columnInfos)
  columnDefs.unshift({
    ...indexColDef,
    cellRenderer: 'RowIndexRendererWithContextMenu',
    pinned: 'left'
  })

  /** 各个编辑状态下激活的 toolbarActions */
  const enabledActionsMap: { [key in IGridStatus]: ToolbarActionType[] } = {
    NORMAL: ['refresh', 'add'],
    UPDATE: ['refresh', 'confirm', 'cancel'],
    INSERT: ['refresh', 'confirm'],  // , 'cancel'
    DELETE: ['refresh', 'add'], // , 'delete'
    CLONE: ['refresh', 'confirm'],  // , 'cancel'
  }

  // 结果页再编辑的参数 columnInfos
  const ColumnInfosEdit = useMemo(
    () =>
      columnInfos.map(({ columnName, cellEditor, defaultValue, dataFormat }) => ({
        columnName,
        columnType: cellEditor,
        defaultValue,
        dataFormat,
      })),
    [columnInfos],
  )

  const filterNames = useMemo(() => {
    if (!filteredColumns) return []
    return filteredColumns.map(item => item.columnName)
  }, [filteredColumns])

  const getRowDataTemplate = useCallback(() => {
    const newRowData = ColumnInfosEdit.reduce((total: any, colInfo: any) => {
      total[colInfo?.columnName] = colInfo.defaultValue ?? null
      return total
    }, {})
    return newRowData
  }, [ColumnInfosEdit])

  /* 执行模块获取的 初始块数据 */
  const isInitializeDataUsed = useRef<boolean>(false)
  /* 获取远程 块数据 */
  const { run: fetchBlockDataOrFromStore } = useRequest(
    async (
      offset: number = 0,
      sortModels: any,
      filterModel: any,
      rowCount: undefined | number = rowNum,
    ) => {
      /* 0 首块数据，第一次获取时使用初始化数据。第二次获取使用远程数据 */
      if (offset === 0 && !isInitializeDataUsed.current) {
        isInitializeDataUsed.current = true
        return Promise.resolve([cloneDeep(result)])
      }
      const payload = {
        connectionId,
        dataSourceType,
        databaseName,
        operatingObject,
        statements: [statement],
        offset,
        rowCount,
        tabKey: queryKey,
        plSql,
        sortModels,
        filterModel,
        autoCommit: txMode === 'auto',
        actionType: actionType,
      }
      // 设置执行 pending
      dispatch(setTabExecutionStatus({ key: queryKey, pending: true }))
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%` }));
      if (type === 'explain') {
        const res = explainSqlStatement(isDesensitizedPlaintext ? currentParams : payload)
        // 恢复面板为可执行状态
        dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
        dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}`}));
        return res
      } else {
        const res = await executeSqlSegment(isDesensitizedPlaintext ? currentParams : payload)
        const { channelId, groupName, messageId, messageData = [], executeStatus } = res;
        if ( executeStatus !== 'PENDDING' && messageData) {
         return formatExecuteStatementResult(res)
        }
        const params = { channelId, groupName, messageId }
        const result = await recursiveQuery(params);
        return result
      }
    },
    {
      manual: true,
      onSuccess: () => {
        setActionType('ROLLING_RESULT')
      }
    },
  )

  //处理查询到的结果
  const formatExecuteStatementResult = useCallback((data: any) => {
   
      const { executionInfos } = data?.messageData || {};
      let queryResult: QueryResult[] = [];
      executionInfos.forEach((item: any) => {
        queryResult.push(item.response);

        const logMessage = {
          ...(item?.executeLogInfo?.message ?? {}),
          executeSqlFlag: true  // 标记是执行sql产生的日志
        }
        handleExternalInfo({ type: 'LOG', msg: logMessage });

        // 结果集刷新后需要更新表头（比如修改脱敏字段后应更新显示）
        if (actionType === 'REFRESH' && item.response) {
          const newColumns: ColDef[] = connectionType === 'Oracle' ? formatOracleCustomColumn(columnInfos) : formatColumn(columnInfos);

          newColumns.unshift({
            ...indexColDef,
            cellRenderer: 'RowIndexRendererWithContextMenu',
          });
          item.response.columnInfos && gridApiRef.current?.api.setColumnDefs(newColumns);
        }
      });
      if (data.executeStatus === 'FAILED') {
        // 日志报错跳转至执行日志tab
        dispatch(setActiveResultTabKey({ queryTabKey: queryKey, resultTabKey: `log/${queryKey}` }));
      }
      // 恢复面板为可执行状态
      dispatch(setTabExecutionStatus({ key: queryKey, pending: false }));
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
      return queryResult;
   
  },[])

  const recursiveQuery = async (params: QueryParams, delay = 1000): Promise<QueryResult[]> => {
    try {
      const data = await getSegmentResults(params);
      if (data && data?.messageData) {
        
        return formatExecuteStatementResult(data);
      }
      if (data && data.executeStatus === 'RUNNING') {
        // 继续下一次查询，等待 3000 毫秒
        return new Promise((resolve) => {
          setTimeout(async () => {
            // 继续下一次查询，增加延迟从100ms开始，每次增加100ms，但不超过3000ms
            const max = 3000;
            let nextDelay = delay;
            if (delay < max) {
              nextDelay = delay + 100;
            }
            const result = await recursiveQuery(params, nextDelay);
            resolve(result);
          }, delay);
        });
      }
      // 添加一个默认返回值
      return [];
    } catch (error) {
      console.error('轮询出错：', error);
      throw error;
    }
  };

  const getLastRow = useCallback((blockData: unknown[], offset: number) => {
    if (blockData?.length < rowNum) {
      return blockData.length + offset
    }
  }, [])

  const formatFilterModel = (filterModel: IGetRowsParams['filterModel']) => {
    const formattedFilterModel = Object.keys(filterModel)
      .map((key) => {
        const model = filterModel[key]
        if (model.operator) {
          // 双重过滤
          const { condition1, condition2, ...rest } = model
          return [
            key,
            {
              ...rest,
              conditions: [model.condition1, model.condition2],
            },
          ]
        }
        return [key, { conditions: [model] }]
      })
      .reduce(
        (prev, [k, v]) => ({ ...prev, [k]: v }),
        {} as Record<string, any>,
      )
    return formattedFilterModel
  }

  const gridApiRef = useRef<GridReadyEvent | null>(null)
  const handleGridReady = (options: GridReadyEvent): void => {
    gridApiRef.current = options;
    calculateVisibleRowCount();

    // 初始化自定义选择控制器（无限滚动模式，禁用列选择）
    initializeSelectionController(options, gridApiRef);

    const scrollGridWrapper = document.getElementById(`${tabResultKey}gridWrapper`);
    if (scrollGridWrapper) {
      const eventTarget = scrollGridWrapper.querySelector('.ag-body-viewport') as HTMLElement;
      const scrollTarget = scrollGridWrapper.querySelector('.ag-body-horizontal-scroll-viewport') as HTMLElement;
      if (eventTarget && scrollTarget) {
        const handleWheel = (e: WheelEvent) => {
          // 当横向滚动意图明显时 (例如，触控板横向滑动)
          if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
            e.preventDefault();
            e.stopPropagation();
            scrollTarget.scrollLeft += e.deltaX;
          } else if (e.shiftKey) { // 兼容按住 Shift 键用鼠标滚轮横向滚动的操作
            e.preventDefault();
            e.stopPropagation();
            scrollTarget.scrollLeft += e.deltaY;
          }
        };
        eventTarget.addEventListener('wheel', handleWheel, { passive: false });
        options.api.addEventListener('gridDestroyed', () => {
          eventTarget.removeEventListener('wheel', handleWheel);
        });
      }
    }
  }

  const calculateVisibleRowCount = () => {
    const containerHeight = document.getElementById(`${tabResultKey}gridWrapper`)?.clientHeight || 400; // 获取容器高度
    const visibleCount = Math.floor((containerHeight - 32) / 28); // 计算可展示行数
    setVisibleRowCount(visibleCount);
    // console.log(`可视窗口中的行数: ${visibleCount}`, containerHeight);
  };

  // 监听窗口大小变化以重新计算可视行数
  useEffect(() => {
    const handleResize = () => {
      calculateVisibleRowCount();
    };

    window.addEventListener('resize', handleResize, { passive: true });
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 给行数据添加可编辑属性等
  const handleRowDataWithEditable = (data: any[]) => {
    return (
      data.map((curItem: any) => {
        const customCurItem = Object.assign({ editable: true }, curItem);
        const keys = Object.keys(curItem);
    
        for (const key of keys) {
          const value = curItem[key];
          customCurItem[key] = value?.value;
    
          if (value?.formatValue) {
            customCurItem[`get${key}`] = () => value.formatValue;
          }
    
          if (value?.renderType?.includes('binary') && value?.size) {
            customCurItem[`${key}CQSize`] = value.size;
          }
    
          if (!value?.editable) {
            customCurItem.editable = false;
          }
        }
        return customCurItem;
      })
    )
  }

  const successCallbackRef = useRef<(rowsThisBlock: any[], lastRow?: number | undefined) => void>(() => { })
  const getRows: (params: IGetRowsParams) => void = useCallback(
    async (params) => {
      const {
        startRow,
        endRow,
        sortModel: sortInfo,
        filterModel,
        successCallback,
        failCallback,
        context,
      } = params
      const { canSave, status } = context as IGridContext
      // 插入和克隆需要在第一条语句插入内容,所以需要记录第一次加载的成功回调
      if (startRow === 0) {
        successCallbackRef.current = successCallback
        // 没数据时插入数据触发的getRows直接返回
        if (status === 'INSERT') {
          return
        }
      }
      // 编辑后没保存数据时直接return (当startRow % rowNum === 0 时，是分页的接口请求会触发getRows)
      // 内部数据重新加载（如删除操作后的立即刷新）不应该被阻止
      if (!isInternalDataReloadRef.current && (canSave || ['CLONE', 'INSERT', 'UPDATE'].includes(status))) {
        failCallback()
        return message.error(t('sdo_save_first'))
      }
      try {
        setLoading(true)
        // 不支持分页不需要重新请求数据
        if (notSupportPageResultDataRef.current?.length) {
          const resultData = notSupportPageResultDataRef.current
          const curResultData = resultData.slice(startRow, endRow)
          let totalRows = resultData.length
          setPageTotal(totalRows)
          successCallback(curResultData, totalRows)
          return
        }
        else if (isDesensitizedPlaintext && startRow === 0) {
          let totalRows = detailedResultData?.length
          setPageTotal(totalRows)
          successCallback(currentRusultData, getLastRow(currentRusultData, startRow))
          return
        }
        // 检查是否是删除操作后的数据加载，直接从 allResultDataRef.current 中获取数据
        else if ((isInternalDataReloadRef.current || isAfterDeleteRef.current) && allResultDataRef.current?.length > 0) {
          const curResultData = allResultDataRef.current.slice(startRow, endRow)
          const totalRows = allResultDataRef.current.length
          setPageTotal(totalRows)

          if (curResultData.length === 0 && startRow >= totalRows) {
            // 请求的数据块超出了现有数据范围，返回空数据并结束
            successCallback([], totalRows)
          } else {
            // 返回切片后的数据
            successCallback(curResultData, getLastRow(curResultData, startRow))
          }
          return
        }
        /* 0 格式化过滤参数 */
        const formattedFilterModel = formatFilterModel(filterModel)
        /* 0 处理排序数据 应后端要求增加一个index列标值*/
        let sortModel: any[] = sortInfo
        if (!isEmpty(sortInfo)) {
          sortModel = sortInfo.map((item: any) => {
            return {
              ...item,
              index: columnDefs.findIndex((col) => col.field === item.colId)
            }
          })
        }
        /* 1 获取块数据 */
        let response = await fetchBlockDataOrFromStore(
          startRow,
          sortModel,
          formattedFilterModel
        )
        let [{ resultData: defaultResultData, detailedResultData: defaultDetailResultData, executeError, supportPage }] = response || []
        /* 2 错误处理 */
        if (executeError) {
          console.error(executeError.message)
        }
        setRowViewerResultData((data: any[]) => {
          return [...data, ...defaultResultData]
        })
        let resultData = handleRowDataWithEditable(defaultDetailResultData);
        //oracle自定义字段
        if (dataSourceType === 'Oracle') {
          resultData = getExpandedCurResultData(defaultDetailResultData);
        }

        /* 3 根据本块数据量计算总数 */
        if (!supportPage) {
          allResultDataRef.current = resultData
          notSupportPageResultDataRef.current = resultData
          let totalRows = resultData.length
          // 不分页一次返回所有数据需单独分配当前数据
          const curResultData = resultData.slice(startRow, endRow)
          setPageTotal(totalRows)
          successCallback(curResultData, totalRows)
        } else {
          if (startRow === 0) {
            // 如果是首次加载就直接赋值，避免因排序筛选等重新加载时的数据混乱
            allResultDataRef.current = resultData
            // 重置内部数据重新加载标志
            isInternalDataReloadRef.current = false
          }
          else {
            // 追加分页数据
            allResultDataRef.current = allResultDataRef.current.concat(resultData)
            isFirstRender.current = false;
            // 重置内部数据重新加载标志（正常滚动加载时）
            isInternalDataReloadRef.current = false
          }
          let rows = getLastRow(resultData, startRow)
          setPageTotal(resultData.length + startRow)
          successCallback(resultData, rows)
        }
      } catch (error) {
        failCallback()
      } finally {
        setLoading(false)
        dispatchAction('reset')
      }
    },
    [
      getRowDataTemplate,
      columnDefs,
      dispatch,
      queryKey,
      fetchBlockDataOrFromStore,
      getLastRow,
      dispatchAction,
    ],
  )

  const datasource: IDatasource = {
    getRows,
  }

  const resetDataStatus = () => {
    setCanSave(false)
    setCanDelete(false)
    setResultOperationDates([])
    setSelectedNodes([])
    allResultDataRef.current = []
    setFocusedRowData({})
    setFocusedColumn(null)
    setFocusedRowIndex(null)
    // 重置内部数据重新加载标志和删除后状态标志
    isInternalDataReloadRef.current = false
    isAfterDeleteRef.current = false
  }

  // 点击刷新按钮
  const handleRefresh = () => {
    if (!gridApiRef.current) return
    const { api, columnApi } = gridApiRef.current
    setActionType('REFRESH')
    dispatchAction('reset')
    // 重置筛选/排序，清空 cache
    api.setFilterModel(null)
    columnApi?.applyColumnState({ defaultState: { sort: null } })
    api.ensureIndexVisible(0)
    api.setRowCount(0)
    api.purgeInfiniteCache()
    api.deselectAll()
    resetDataStatus()
    cloneResultDataRef.current = []
    setLoading(true)
  }

  // 点击添加按钮
  const handleAddRow = () => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    dispatchAction('startInsert')
    const rowCount = api.getInfiniteRowCount()
    if (typeof rowCount === 'undefined') return
    api.setRowCount(rowCount + 1)
    const initRow = getRowDataTemplate()
    const insertkey = new Date().getTime()
    const row = {
      ...initRow,
      __isNewRow: () => {
        return insertkey
      }
    }
    const newData = [row].concat(allResultDataRef.current)
    const successCallback = successCallbackRef.current
    successCallback(newData)
    allResultDataRef.current = newData
    api.startEditingCell({
      rowIndex: 0,
      colKey: columnDefs[1].field!,
    })
  }

  const handleDeleteRowConfirm = () => {
    Modal.confirm({
      title: t('sdo_confirm_del_selected_row_data'),
      onOk: () => handleDeleteRow(),
      centered: true,
      okButtonProps: { danger: true },
    })
  }

  // 点击删除按钮
  const handleDeleteRow = () => {
    if (!gridApiRef.current) return
    const selectedNodesData = selectedNodes?.map(({ data }) => data)
    const selectedRowIndex = selectedNodes?.map(({ rowIndex }) => rowIndex)
    // 删除重置选中值
    selectedNodesData?.forEach((item: any) => {
      if (isEqual(item, focusedRowData)) {
        setFocusedRowData({})
        setFocusedColumn(null)
        setFocusedRowIndex(null)
      }
    })
    let deleteDataArray: any[] = []
    // 判断是否有克隆行在删除行中
    const cloneHasDelCols: boolean = cloneResultDataRef.current?.filter((res: any) => {
      return selectedRowIndex?.includes(res.rowIndex)
    }).length > 0
    // 处理克隆行
    if (cloneHasDelCols) {
      let indexList: number[] = []
      cloneResultDataRef.current = cloneResultDataRef.current?.filter((res: any) => {
        const has = selectedRowIndex?.includes(res.rowIndex)
        if (has) {
          indexList.push(res.rowIndex)
        }
        return !has
      })
      deleteDataArray = selectedNodes?.filter(({ rowIndex }) => !indexList.includes(rowIndex))?.map(({ data }) => ({
        resultOldData: data,
        resultOperating: 'DELETE'
      }))
      setResultOperationDates(s => [...s.filter((item: any) => !item.hasOwnProperty('rowIndex')), ...cloneResultDataRef.current, ...deleteDataArray])
    }
    // 正常删除逻辑
    else {
      deleteDataArray = selectedNodes?.map(({ data }) => ({
        resultOldData: data,
        resultOperating: 'DELETE'
      }))
      setResultOperationDates(s => [...s, ...deleteDataArray])
    }
    // 通过索引过滤,内容过滤没有唯一的key会有问题
    allResultDataRef.current = allResultDataRef.current?.filter((_, index) => {
      const has = selectedRowIndex?.includes(index)
      return !has
    })
    const { api } = gridApiRef.current
    const length = allResultDataRef.current?.length

    // 设置内部数据重新加载标志，允许删除后的立即数据重新加载
    isInternalDataReloadRef.current = true
    // 设置删除后状态标志，允许后续的滚动加载
    isAfterDeleteRef.current = true

    // 重置行数和缓存
    api.setRowCount(0)
    api.purgeInfiniteCache()

    // 重新设置正确的行数，这会触发数据重新加载
    api.setRowCount(length)

    // 删除操作后设置可保存状态
    setCanSave(true)
    dispatchAction('reset')
  }

  // 点击确定按钮
  const handleConfirmModify = () => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    // 保持修改后的状态退出编辑
    api.stopEditing()
  }

  // 点击取消按钮(插入和克隆不支持取消,因为涉及到多步操作有交叉事件不好处理,要取消可以用删除)
  const handleCancelModify = useCallback(() => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    // 要取消编辑（即不接受更改）
    api.stopEditing(true)
    dispatchAction('reset')
  }, [dispatchAction])

  useEffect(() => {
    const allEditable = selectedNodes?.length > 0 && selectedNodes?.every((n: any) => [true, 'true'].includes(n?.data?.editable))
    if (allEditable) {
      dispatchAction('startDelete');
      setCanDelete(true);
    } else if (status === 'DELETE') {
      dispatchAction('reset');
      setCanDelete(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedNodes, status]);

  // 放大缩小页面
  const handleScaleView = (num: number) => {
    //设置单独结果集scale值 queryKey {}
    const resultTabs = Object.keys(resultTabMap).map(key => {
      if (key === tabResultKey) {
        return {
          key,
          info: {
            ...resultTabMap[key].info,
            scale: num
          }
        }
      }
      return resultTabMap[key]
    })
    dispatch(updateResultTabs(resultTabs))
  }

  // 单元格聚焦
  const handleCellFocused = (event: any) => {
    if (event?.rowIndex != null) {
      setFocusedRowIndex(event?.rowIndex)
      const rowNode = event?.api?.getDisplayedRowAtIndex(event?.rowIndex)
      setFocusedRowData(rowNode?.data)
      setFocusedColumn(event?.column)
    }
  }

  //结果集包含二进制 则不允许修改
  const isColumnContainBlobField = (curColumns: any) => {
    const binaryColumns = curColumns.filter(({ cellEditorParams }: any) => {
      const { renderType } = cellEditorParams || {};
      return renderType === 'binary';
    });
    if (binaryColumns?.length) {
      return true
    }
    return false
  }

  // 进入编辑状态
  const handleStartRowEditing = (event: RowEditingStartedEvent) => {
    //包含二进制 增加提示
    const curColumns = event.api.getColumnDefs();
    if (curColumns && isColumnContainBlobField(curColumns)) {
      message.warning({ top: 100, content: t('sdo_binary_disallowed_edit') })
    }
    preEditRowRef.current = { ...event.data }
    //进入编辑状态时设置是否点击确认按钮为false
    dispatchAction('startUpdate')
    // 编辑中不可以保存, 先记录编辑前可保存状态方便恢复
    prevCanSave = canSave
    setCanSave(false)
  }

  // 退出编辑状态
  const handleStopRowEditing = async (params: RowEditingStoppedEvent & { edited_markup?: number }) => {
    const { data, rowIndex } = params
    //获取或增加唯一标识符
    const edited_markup = params?.data?.edited_markup || (new Date()).getTime();
    const dataCopy = cloneDeep(data)
    const initData = { ...dataCopy?._initData }
    delete dataCopy._initData
    const oldData = { ...dataCopy, ...initData }
    const newData = { ...dataCopy }
    let resultNewData = getAlteredRowData(oldData, newData)
    const isRowDirty = Object.keys(resultNewData).length;
    // 判断是否是在新插入的行上进行的操作
    const isNewRowUpdate = oldData?.__isNewRow?.() || false
    resultNewData = isNewRowUpdate ? { ...resultNewData, __isNewRow: oldData?.__isNewRow } : { ...resultNewData }
    let resultOperationDates: any[] = [{ resultOldData: oldData, resultNewData, resultOperating: 'UPDATE', edited_markup }]
    // 修改尚未保存的克隆行则不需要再次插入update操作
    if (cloneResultDataRef.current?.length > 0 && status !== 'INSERT') {
      cloneResultDataRef.current = cloneResultDataRef.current.map((iData: any) => {
        if (iData.rowIndex === rowIndex) {
          resultOperationDates = []
          return {
            ...iData,
            resultNewData: data
          }
        }
        else return iData
      })
    }
    if (status === 'INSERT') {
      resultOperationDates = [{ resultNewData, resultOperating: 'INSERT' }]
      if (cloneResultDataRef.current) {
        cloneResultDataRef.current = cloneResultDataRef.current?.map((res: any) => ({
          ...res,
          rowIndex: res.rowIndex + 1
        }))
      }
    } else if (status === 'CLONE') {
      resultOperationDates = []
    } else if (status === 'UPDATE' && !isRowDirty) {
      dispatchAction('reset')
    }
    // 修改克隆行时做替换，不增加update语句
    if (resultOperationDates.length === 0) {
      setResultOperationDates(s => [...s.filter((item: any) => !item.hasOwnProperty('rowIndex')), ...cloneResultDataRef.current])
      setCanSave(true)
    }
    else if (isNewRowUpdate && status === 'UPDATE') {
      // 插入行被update时替换原有数据，不增加update语句
      setResultOperationDates(s => {
        const newDatas = s.map((item: any) => {
          const { resultNewData } = item
          if (resultNewData?.__isNewRow?.() === isNewRowUpdate) {
            return { ...item, resultNewData: resultOperationDates[0].resultNewData }
          }
          else return item
        })
        return newDatas
      })
    }
    // 更新操作且有值才保存
    else if (isRowDirty || (resultOperationDates[0]?.resultOperating !== 'UPDATE' && !isRowDirty)) {
  
      //原逻辑直接push导致每编辑一次就会增加一条数据，增加标识，防止重复添加
      setResultOperationDates(prevRes => {
        const index = prevRes.findIndex(item => (item?.edited_markup === edited_markup && item?.resultOperating === resultOperationDates[0]?.resultOperating));
        if (index !== -1) {
          // 如果找到相同的 id，覆盖
          return prevRes.map((item, i) => 
            i === index ? { ...item, ...resultOperationDates[0] } : item
          );
        } else {
          // 如果没有找到，新增
          return [...prevRes, ...resultOperationDates];
        }
      });
    
      setCanSave(true)
    }
    // 恢复编辑前可保存的状态
    if (prevCanSave && !canSave) {
      setCanSave(true)
    }
    const successCallback = successCallbackRef.current
    //只要是编辑过的行，增加唯一标识符
    if (rowIndex !== null && rowIndex !== undefined) {
      allResultDataRef.current[rowIndex] = {...data, edited_markup }
    }
    successCallback(allResultDataRef.current)
    dispatchAction('reset')
  }

  // 结果集生成功能
  const handleResOpe = async (opeDatas: any[]) => {
    const { api } = gridApiRef.current || {};
    if (!api) {
      return
    }
    const params: any = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: opeDatas,
    }
    const statements = await formatAndModifyResult(params)
    const tailText = statements?.reduce((pre: string, cur: string, index: number) => {
      if (index < statements.length - 1) return pre + (cur || '') + '\n'
      else return pre + (cur || '')
    }, '')
    copyTextToClipboard(tailText);
    dispatch(
      pushMonacoValue({
        key: activeTabKey,
        tailText: '\n' + tailText,
      }),
    )
  }

  // 保存
  const handleSave = async () => {
    const { api } = gridApiRef.current || {};
    if (!api) {
      return
    }
    let isCloneSuccess = false
    const params: any = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: resultOperationDates.map((item: any) => ({
        ...item,
        rowIndex: undefined,
        resultNewData: {
          ...item.resultNewData,
          __isNewRow: undefined
        },
        resultOldData: {
          ...item.resultOldData,
          __isNewRow: undefined
        }
      })),
    }
    // 没有修改直接返回
    if (!resultOperationDates?.length) {
      setCanSave(false)
      return
    }
    setLoading(true)
    try {
      const statements = await formatAndModifyResult(params)
      const executeParams = {
        connectionId,
        dataSourceType,
        operatingObject,
        databaseName,
        statements: statements || [],
        tabKey: queryKey,
        autoCommit: txMode === 'auto',
        actionType: 'UPDATE' as const,
      }
      // 设置执行 pending
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%` }));
      dispatch(setTabExecutionStatus({ key: queryKey, pending: true }))
      dispatch(saveExecuteActiveTabParams(executeParams))
      const executeResults = await executeSqlStatement(executeParams)
      executeResults?.executionInfos?.forEach((item) => {
        const { response } = item
        if (response?.executeError) {
          message.error(response.executeError.message)
          //若执行出错并且为clone模式 保持当前编辑状态
          if (status === 'CLONE' && focusedRowIndex) {
            isCloneSuccess = true
            api?.setFocusedCell(focusedRowIndex, columnDefs[1].field!)
            api?.startEditingCell({
              rowIndex: focusedRowIndex,
              colKey: columnDefs[1].field!,
            })
            return
          }
        }
        handleExternalInfo({ type: 'LOG', msg: item?.executeLogInfo?.message })
      })
    } catch {
    } finally {
      // 重置数据状态
      setLoading(false)
      resetDataStatus()
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
      dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
      //若当前为克隆模式 则失败后不执行刷新和结束编辑
      if (!isCloneSuccess) {
        // 结果集修改之后的查询 actionType 为 'QUERY_AFTER_UPDATE'
        setActionType('QUERY_AFTER_UPDATE')
        api?.purgeInfiniteCache()
        dispatchAction('reset')
        setResultOperationDates([])
      }
      cloneResultDataRef.current = []
    }
  }

  // 克隆单行/多行
  const handleCloneRow = useCallback(async (params: ICellRendererParams) => {
    const selectedRows = params.api.getSelectedRows() ?? [];
    const formatData = selectedRows.map((item: any) => Object.fromEntries(
      Object.entries(item).map(([k, v]) => [k, v === null ? '' : String(v)]),
    ))

    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    const rowCount = api.getInfiniteRowCount()
    if (typeof rowCount === 'undefined') return

    dispatchAction('startClone')
    const successCallback = successCallbackRef.current
    params.api?.deselectAll()
    api.setRowCount(rowCount + selectedRows.length)
    const newData = formatData.concat(allResultDataRef.current)
    successCallback(newData)
    // 记录克隆数据
    const formatResults = formatData.map((iData: any) => ({
      resultNewData: iData,
      resultOperating: 'INSERT'
    }))
    cloneResultDataRef.current = formatResults.concat(cloneResultDataRef.current).map((res: any, index: number) => ({
      // 记录rowIndex，后续用户若在保存前update克隆行的数据，则不再新增update数据
      ...res,
      rowIndex: res?.rowIndex ? (res?.rowIndex + selectedRows.length) : index // 定位行号
    }))
    allResultDataRef.current = newData
    api.startEditingCell({
      rowIndex: 0,
      colKey: columnDefs[1].field!,
    })

  }, [])

  const handlePasteRow = useCallback(async (params: ICellRendererParams) => {
    const { colDef, rowIndex, data } = params
    const { field, editable } = colDef as any
    if (!gridApiRef.current) return
    dispatchAction('startPaste')
    let newData = allResultDataRef.current
    let newRow: any[] = []
    let resultOperationDates: any[] = []
    const text = await navigator.clipboard.readText()
    // 是否含有制表符
    let hasTab: boolean = /\t/.test(text)
    if (hasTab) {
      let rows: any[] = text.split('\r\n').map((row) => row.split('\t'))
      rows.pop()
      const end: number = (newData.length - rowIndex > rows.length) ? (rowIndex + rows.length) : newData.length
      const sliceNum = end - rowIndex
      let newArray = newData.slice(rowIndex, end);
      // 从首行序号点入
      if (!editable) {
        newArray.map((item: any, idx: number) => {
          let i: any = {}
          Object.keys(item).map((key: string, index: number) => {
            i[key] = rows?.[idx]?.[index] || ''
          })
          newRow.push(i)
          resultOperationDates.push({
            resultOperating: "UPDATE",
            resultOldData: {
              ...item
            },
            resultNewData: {
              ...i
            }
          })
        })
      }
      // 从某列具体字段点入
      else {
        newArray.map((item: any, idx: number) => {
          let i: any = {}
          if (idx === 0) {
            let editFlag: boolean = false
            let findIndex: number = 0
            Object.keys(item).map((key: string, index: number) => {
              if (key === field) {
                editFlag = true
                findIndex = index
              }
              if (editFlag) {
                i[key] = rows?.[0]?.[index - findIndex]
              }
              else {
                i[key] = item?.[key]
              }
            })
          }
          else {
            Object.keys(item).map((key: string, index: number) => {
              i[key] = rows?.[idx]?.[index] || ''
            })
          }
          newRow.push(i)
          resultOperationDates.push({
            resultOperating: "UPDATE",
            resultOldData: {
              ...item
            },
            resultNewData: {
              ...i
            }
          })
        })
      }
      newData.splice(rowIndex, sliceNum, ...newRow)
    }
    else if (editable) {
      const newRowData = { ...data, [field]: text }
      newRow.push(newRowData)
      resultOperationDates.push({
        resultOperating: "UPDATE",
        resultOldData: {
          ...data
        },
        resultNewData: {
          ...newRowData
        }
      })
      newData.splice(rowIndex, 1, ...newRow)
    }
    else { return }
    setResultOperationDates(s => [...s, ...resultOperationDates])
    const successCallback = successCallbackRef.current
    successCallback(newData)
    allResultDataRef.current = newData
    setCanSave(true)
  }, [])

  // 结果集生成insert
  const handleResInsert = useCallback(async (params: ICellRendererParams) => {
    const selectedRows = params.api.getSelectedRows();
    const formatSelectDatas = selectedRows?.map((data) => {
      const formatData = Object.fromEntries(
        Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
      )
      return {
        resultNewData: formatData,
        resultOperating: "INSERTSQL"
      }
    })
    handleResOpe([...formatSelectDatas])
  }, [])

  // 结果集生成update
  const handleResUpdate = useCallback(async (params: ICellRendererParams) => {
    const { colDef, data, value, api, columnApi } = params
    let formatSelectDatas: any[] = []
    const selectedRows = api.getSelectedRows();
    if (selectedRows?.length > 1) {
      const columnDefs = columnApi.getAllDisplayedColumns();
      const columns: any[] = columnDefs.slice(1)
      // 判断是否每个列都可编辑
      const editable: boolean = !columns.find((col: any) => !col.colDef?.editable?.())
      formatSelectDatas = selectedRows?.map((data) => {
        const formatData = Object.fromEntries(
          Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
        )
        return {
          resultNewData: editable ? formatData : null,
          resultOldData: formatData,
          resultOperating: "UPDATESQL"
        }
      })
    }
    else {
      const { field, editable } = colDef as any
      const formatData = Object.fromEntries(
        Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
      )
      const newData = editable ? { [field]: value } : null
      formatSelectDatas.push({
        resultNewData: field ? newData : formatData,
        resultOldData: formatData,
        resultOperating: "UPDATESQL"
      })
    }
    handleResOpe([...formatSelectDatas])
  }, [])

  // 结果集生成delete
  const handleResDelete = useCallback(async (params: ICellRendererParams) => {
    const selectedRows = params.api.getSelectedRows();
    const formatSelectDatas = selectedRows?.map((data) => {
      const formatData = Object.fromEntries(
        Object.entries(data).map(([k, v]) => [k, v === null ? null : String(v)]),
      )
      return {
        resultOldData: formatData,
        resultOperating: "DELETESQL"
      }
    })
    handleResOpe([...formatSelectDatas])
  }, [])

  const updateFocusedCell = async (
    newData: PlainRowData,
    oldData: PlainRowData,
  ) => {
    const modifyParams: ResultModify = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: [
        { resultOldData: oldData, resultNewData: newData, resultOperating: 'UPDATE' },
      ],
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
    }

    // 根据执行的操作生成语句, 执行语句并刷新
    setLoading(true)
    try {
      const statements = await formatAndModifyResult(modifyParams)
      const executeParams = {
        connectionId,
        dataSourceType,
        operatingObject,
        databaseName,
        statements: statements || [],
        tabKey: queryKey,
        autoCommit: txMode === 'auto',
        actionType: 'UPDATE' as const,
      }
      // 设置执行 pending
      dispatch(setTabExecutionStatus({ key: queryKey, pending: true }))
      dispatch(saveExecuteActiveTabParams(executeParams))
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%` }));
      const executeResults = await executeSqlStatement(executeParams)
      const { executionInfos } = executeResults
      executionInfos.forEach((item) => {
        const { response, executeLogInfo } = item
        if (response?.executeError) message.error(response.executeError.message)
        handleExternalInfo({ type: 'LOG', msg: executeLogInfo?.message })
      })
    } catch {
    } finally {
      setLoading(false)
      resetDataStatus()
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
      dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
      dispatchAction('reset')
      setActionType('QUERY_AFTER_UPDATE')
      gridApiRef.current?.api.purgeInfiniteCache()
    }
  }

  const RESULT_HOT_KEYS_MAPPING: any = {
    Copy: (e: any) => copySingleRow(e),
    SelectAll: (e: any) => {
      // 使用 CustomSelectionController 的全选方法
      if (customSelectionControllerRef.current) {
        customSelectionControllerRef.current.selectAllRows();
      }
    },
    JumpToFirstLastPage: (e: any) => moveToLastRowOfNextPage(e, 'next'),
    JumpToFirstRowPage: (e: any) => moveToLastRowOfNextPage(e, 'up'),
    QuicklyJumpToFirstRow: (e: any) => moveToFirstOrLastRowAndColumn(e, 'home'),
    QuicklyJumpToLastRow: (e: any) => moveToFirstOrLastRowAndColumn(e, 'end'),
    QuicklyNavigateFirstColumn: (e: any) => moveToFirstOrLastColumn(e, 'left'),
    QuicklyNavigateLastColumn: (e: any) => moveToFirstOrLastColumn(e, 'right'),
  }
  
  const onHandlekeyDowm = (event: any) => {
    if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 's') {
      event.preventDefault(); 
    }
  }

  useEffect(()=>{
    document.getElementById(`${tabResultKey}gridWrapper`)?.addEventListener('keydown', onHandlekeyDowm, { passive: true });
    return () => {
      document.getElementById(`${tabResultKey}gridWrapper`)?.removeEventListener('keydown', onHandlekeyDowm);
    }
  },[])

  const onCellKeyDown = useCallback((e: CellKeyDownEvent) => {
    if (!e.event) {
      return;
    }
    const keyString = getAgGridCellDownKeys(e);
    if (keyString === 'ctrl+c') {
      // 获取正在编辑的单元格
      const editingCells = e.api.getEditingCells();
      // 如果存在正在编辑的单元格，且当前的操作为ctrl+c复制，则不进行任何操作，维持默认行为 
      if (editingCells?.length > 0) {
        return
      }
    }
    e.event.preventDefault();
    const keys = Object.keys(RESULT_HOT_KEYS_MAPPING);
    const action = Object.entries(allHotKeys).find(([key, value]) => {
      if (value?.toLowerCase() === 'ctrl+delete' ) {
        e.api.stopEditing(); // 停止编辑
       }
      if (value?.toLowerCase() === keyString && keys.includes(key)) return true;
      return false;
    })?.[0];

    if (action && Object.keys(RESULT_HOT_KEYS_MAPPING).includes(action)) {
      RESULT_HOT_KEYS_MAPPING[action](e);
    }

  }, [visibleRowCount, allResultDataRef?.current?.length]);

  const copySingleRow = (e: CellKeyDownEvent) => {
    if (canCopy) {
      handleCopyRow(e as any);
    } else {
      message.warning(t('sdo_user_no_copy_permission'));
    }
  }


  // 监听点击事件，如果isSelectAllCancelledRef.current为true，则停止加载状态
  useEffect(() => {
    const handleClick = () => {
      if (!isSelectAllCancelledRef?.current) {
        isSelectAllCancelledRef.current = true;
      }
      setAgGridLoading(false);
    };
  
    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('click', handleClick);
      handleClick();
    };
  }, []);

  const selectAllRows = useCallback((e: CellKeyDownEvent) => {

    const api = e?.api;
    if (!api) return;
  
    try {
      const batchSize = 10; // 每次处理的节点数量
      let nodes: any[] = [];
      api?.forEachNode(node => nodes?.push(node));
      let index = 0;
      let animationFrameId: number | null = null; // 保存动画帧 ID
  
      const selectBatch = () => {
        // 中断执行
        if (isSelectAllCancelledRef?.current) {
          setAgGridLoading(false); // 停止加载状态
          if (animationFrameId !== null) {
            cancelAnimationFrame(animationFrameId); // 取消未完成的动画帧
          }
          return;
        }
        const batch = nodes.slice(index, index + batchSize);
  
        // 单元格选中
        batch?.forEach((node: any) => {
          node.setSelected(true);
        });
  
        index += batchSize;
        if (index < nodes?.length) {
          // 使用 requestAnimationFrame 分批处理：
          // 会自动与浏览器的刷新频率同步
          // 如果用户在页面上产生了其他交互（例如点击、滚动等,浏览器可能会优先处理这些交互事件，从而延迟或暂停 requestAnimationFrame 的回调执行
          animationFrameId = requestAnimationFrame(selectBatch); 
        } else {
          setAgGridLoading(false);
          animationFrameId = null;  // 清除动画帧 ID
        }
      };

      selectBatch();
    } catch (error) {
      setAgGridLoading(false);
      isSelectAllCancelledRef.current = true;
    }
  }, [isSelectAllCancelledRef])

  const moveToFirstOrLastRowAndColumn = useCallback((event: CellKeyDownEvent, type: 'home' | 'end') => {
    let rowNode = event?.api?.getRowNode('0');
    const allColumns = event?.columnApi?.getAllDisplayedColumns();
    let rowIndex = 0;
    let column = allColumns?.[0];
    let focusedField = allColumns?.[1]?.getColId();

    let ensureIndexVisiblePosition: "top" | "bottom" | "middle" | null = 'top';
    if (type === 'end') {
      column = allColumns?.[allColumns?.length - 1];
      rowIndex = (allResultDataRef.current.length - 1) || 0;
      rowNode = event?.api?.getRowNode(rowIndex.toString());
      ensureIndexVisiblePosition = null;
      focusedField = allColumns?.[allColumns?.length - 1]?.getColId();
    }
   
    if (rowNode && event.api) {
      event.api.deselectAll();
      event?.api?.ensureColumnVisible(column?.getColId())
      event.api.ensureIndexVisible(rowIndex, ensureIndexVisiblePosition);
      rowNode?.setSelected(true)
      event?.api.setFocusedCell(rowIndex, focusedField);
    }
  }, [allResultDataRef?.current?.length])
  const moveToFirstOrLastColumn = (event: CellKeyDownEvent, type: 'left' | 'right') => {

    const allColumns = event?.columnApi?.getAllDisplayedColumns();
    let column = allColumns?.[0];
    if (type === 'right') {
      column = allColumns?.[allColumns?.length - 1];
    }

    if (column) {
      event?.api?.deselectAll()
      event?.api?.ensureColumnVisible(column)
    }
  }
  const moveToLastRowOfNextPage = useCallback((event: any, type: 'next' | 'up') => {
    if (!gridApiRef.current || !event?.api) return
    const gridApi = gridApiRef.current?.api
    const allColumns = event?.columnApi?.getAllDisplayedColumns();
    let column = allColumns?.[1];
    let currentRowIndex = gridApi.getFocusedCell()?.rowIndex || 0;
    const focusedField = event.api.getFocusedCell()?.column?.getColId() || column?.getColId();

    let nextPageLastRowIndex = currentRowIndex + visibleRowCount - 1;

    if (type === 'up') {
      if (currentRowIndex < visibleRowCount) {
        nextPageLastRowIndex = 0;
      } else {
        nextPageLastRowIndex = currentRowIndex - visibleRowCount + 1;
      }
    }

    if (currentRowIndex !== undefined) {

      const rowNode = event?.api?.getRowNode(`${nextPageLastRowIndex}`)

      const ensureRowIndex = rowNode?.rowIndex || 0
      event?.api?.deselectAll()
      setTimeout(() => {
        if (type === 'up') {
          event.api.ensureIndexVisible(ensureRowIndex, 'top')
        } else {
          event.api.ensureIndexVisible(ensureRowIndex, 'bottom')
        }
      }, 100)
      if (rowNode) {
        event?.api?.selectNode(rowNode, true)
        setTimeout(() => {
          event?.api.setFocusedCell(nextPageLastRowIndex, focusedField);
        }, 100)
      }
    }
  }, [visibleRowCount]);

  const downloadFocusedCell = async (
    newData: PlainRowData,
    oldData: PlainRowData,
  ) => {
    const modifyParams: ResultModify = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      columnInfos: ColumnInfosEdit,
      resultOperationDates: [
        { resultOldData: oldData, resultNewData: newData, resultOperating: 'RENDER' },
      ],
    }
    const statements = await formatAndModifyResult(modifyParams)
    const executeParams = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statements: statements || [],
      tabKey: queryKey,
    }
    return downloadGridCell(executeParams)
  }

  const fetchFocusedCell = async (
    newData: PlainRowData,
    oldData: PlainRowData,
  ) => {
    const modifyParams: ResultModify = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statementObject,
      columnInfos: ColumnInfosEdit,
      operatingObjectWithQuoteCharacter,
      statementObjectWithQuoteCharacter,
      resultOperationDates: [
        { resultOldData: oldData, resultNewData: newData, resultOperating: 'RENDER' },
      ],
    }
    const statements = await formatAndModifyResult(modifyParams)
    const executeParams = {
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statements: statements || [],
      tabKey: queryKey,
    }
    return getCompleteCell(executeParams)
  }

  const isDataExportPermissionEnabled = Service.moduleService.isModuleExist(
    '/flow',
  )
    ? dataExport.status
    : false

  /* 打开申请流程权限表单 */
  const applyDataExportPermission = async (exportType?: string) => {
    const elements = permissionResult?.allNodePathString?.map((item:any)=>({
      nodePath:item,
      connectionType: dataSourceType
    }))
    
    dispatch(
      openFlowForm({
        type: 'dataExport',
        fields: {
          elements: elements,
          operationList: exportType ? [exportType] : undefined,
        },
      }),
    )
  }

  const onOpenExportModal = (key: string) => {
    const visibleMap = {
      ResultExport: () => setVisibleCommonExport(true),
      SelectedResultExport: () => setVisibleSelectedExport(true),
      ResultAllExport: () => setVisibleExportAll(true),
    }
    if (key in visibleMap) {
      visibleMap[key as keyof typeof visibleMap]()
    }
  }

  const handleExportAllResult = useCallback(
    (data: any) => {
      const params = {
        connectionId: result.connectionId,
        connectionType: result.dataSourceType,
        databaseName: result.databaseName,
        operatingObject: result.operatingObject,
        statement: result.statement,
        containTempTable: result.containTempTable,
        tabKey: result.tabKey,
      }
      return makeResultAllExport(Object.assign({}, params, data))
      .then(() => {
        exportTaskCreatedNot()
      })
    },
    [result],
  )

  const handleViewCell = useCallback(() => {
    setVisibleCellViewer(true)
  }, [])

  useEffect(() => {
    if (!gridApiRef.current) return
    const { api } = gridApiRef.current
    api.ensureIndexVisible(focusedRowIndex || 0)
  }, [focusedRowIndex, rowNum])

  const handleViewRow = useCallback(() => {
    setVisibleRowViewer(true)
  }, [])

  const agContext = useMemo<IGridContext>(
    () => {
      return {
        canSave,
        status,
        disableContextMenu: paneType === 'tSql',
        copyable: canCopy,
        canCopyCell,
        allowClone: !(connectionType === 'DB2' || connectionType === 'StarRocks' || connectionType === 'Inceptor') && editable,
        allowCreateSql: permissionResult?.supportResultSetSqlGenerator,
        createSqlDisabled: permissionResult?.resultSetSqlGenerator && enableGenerateSql,
        handleCopyCell,
        handleCopyRow,
        handleCopyAll: (context: ICellRendererParams) => {
          handleCopyAll(context, allResultDataRef.current)
        },
        handleCopyCellTitle,
        handleCopyAllTitle,
        handleCopyWithTitle,
        handlePasteRow,
        handleViewCell,
        handleViewRow,
        handleCloneRow,
        handleResInsert,
        handleResUpdate,
        handleResDelete,
        // 新增的动态菜单处理函数
        handleCopySelectedColumnTitles: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnTitles : undefined,
        handleCopySelectedColumnData: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnData : undefined,
        handleCopySelectedCellsData: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsData : undefined,
        handleCopySelectedCellsTitles: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsTitles : undefined,
        handleCopySelectedCellsWithTitle: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsWithTitle : undefined,
        handleCopySelectedFullColumnDataWithTitle: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedFullColumnDataWithTitle : undefined,
        getSelectedRowsDataForResultSet: contextHandlersReady ? contextMenuHandlersRef.current?.getSelectedRowsDataForResultSet : undefined,
        // 新增：提供获取选择上下文的方法
        getSelectionContext: () => {
          return customSelectionControllerRef.current?.getCurrentSelectionContext() || null;
        },
        // 新增：选中单元格的方法
        selectCell: (params: ICellRendererParams) => {
          if (customSelectionControllerRef.current && params.column && params.rowIndex != null) {
            const colId = params.column.getColId();
            customSelectionControllerRef.current.selectCell(params.rowIndex, colId);
            updateSelectionContext();
          }
        },
        // 全选逻辑
        handleCopySelectAllWithTitle: (context: ICellRendererParams) => {
          handleCopySelectAllWithTitle(context, allResultDataRef.current)
        },
        handleCopySelectedAllCellsTitles,
      }
    },
    [
      allowCopy,
      canCopyCell,
      connectionType,
      handleCopyCell,
      handleCopyRow,
      handleCopyAll,
      handleCopyCellTitle,
      handleCopyAllTitle,
      handleCopyWithTitle,
      handleViewCell,
      handleViewRow,
      handleCloneRow,
      handleResInsert,
      handleResUpdate,
      handleResDelete,
      handlePasteRow,
      paneType,
      status,
      permissionResult?.resultSetCopy,
      dataSourceType,
      permissionResult?.resultSetSqlGenerator,
      canSave,
      permissionResult?.supportResultSetSqlGenerator,
      enableGenerateSql,
      contextHandlersReady,
      updateSelectionContext
    ],
  )

  const executePayload = useMemo<QueryBase>(
    () => ({
      connectionId,
      dataSourceType,
      operatingObject,
      databaseName,
      statements: [statement],
      tabKey: queryKey,
    }),
    [connectionId, dataSourceType, databaseName, operatingObject, queryKey, statement]
  )

  const handleToolbarView = useCallback(() => {
    if (!focusedColumn) return
    const colId = focusedColumn.getColId()
    if (colId === indexColId) {
      // 是序号列，展示行数据
      setVisibleRowViewer(true)
      return
    }
    // 展示单元格数据
    setVisibleCellViewer(true)
  }, [focusedColumn])

  useEffect(() => {
    if (copySetting) return
    if (visibleRowViewer || visibleCellViewer) {
      document.oncopy = (e) => {
        e.returnValue = false
      }
      document.oncontextmenu = (e) => {
        e.returnValue = false
      }
    } else {
      document.oncopy = (e) => {
        e.returnValue = true
      }
      document.oncontextmenu = (e) => {
        e.returnValue = true
      }
    }
  }, [visibleRowViewer, visibleCellViewer, copySetting])

  const lastRowIndex = () => {
    setFocusedRowIndex((index: number | null) => {
      return index ? index - 1 : index
    })
  }

  const nextRowIndex = () => {
    setFocusedRowIndex((index: number | null) => {
      return (index || index === 0) ? index + 1 : index
    })
  }

  const handleSelectionChanged = (event: any) => {
    const nodes = event?.api?.getSelectedNodes()
    setSelectedNodes(nodes)
  }

  //敏感资源申请
  const onApplyDensens = () => {
    if (maskedPathMap && Object.keys(maskedPathMap)?.length > 0) {
      //无权限的脱敏资源
      const noPermissionSensitiveResourceElements = Object.keys(maskedPathMap).filter(key => !maskedPathMap[key]).map(key => ({
        label: '',
        value: key
      }));

      dispatch(
        openFlowForm({
          type: 'desensitizedResource',
          fields: {
            //@ts-ignore
            elements: noPermissionSensitiveResourceElements,
            //@ts-ignore
            connectionId,
            connectionType,
            nodeType: '',
          },
        })
      );
    }
  }

  // 首次数据渲染完成（含异步数据）回调--自适应调整列宽
  const onFirstDataRendered = useCallback((params: any) => {
    const { columnApi, api } = params;

    const adjustColumns = () => {
      // 仅在获取第一页数据时（包括刷新），进行列宽自适应调整
      if (isFirstRender.current) {
        const colIds = columnApi.getAllColumns()
          ?.filter((col: any) => col.getColId() !== indexColId)
          ?.map((col: any) => col.getColId());

        if (colIds?.length && api.getDisplayedRowCount() > 0) {
          // 能够获取到表头节点的列做自适应调整
          let autoSizeIds: any[] = []
          colIds.forEach((id: number | string) => {
            const headerElement = document.querySelector(`[col-id="${id}"] .${CustomHeaderStyles.headerWrapper}`);
            // 如果表头节点存在，说明已经进行了dom渲染，可以进行列宽自适应调整
            if (headerElement) {
              autoSizeIds.push(id)
            }
          });

          // 先执行自动调整
          columnApi.autoSizeColumns(autoSizeIds, false);
          // 然后遍历所有列，限制最大宽度为300px
          autoSizeIds.forEach(id => {
            const column = columnApi.getColumn(id);
            const currentWidth = column.getActualWidth();
            if (currentWidth > 300) {
              columnApi.setColumnWidth(id, 300);
            }
          });
          // 标记首次渲染完成
          isFirstRender.current = false;
          // 移除 modelUpdated 监听器
          if (modelUpdatedHandlerRef.current) {
            api.removeEventListener('modelUpdated', modelUpdatedHandlerRef.current);
          }
        }
      }
    };

    // 定义 modelUpdated 事件处理函数
    const handleModelUpdated = () => {
      // 数据更新后，延迟调整列宽，autoSizeColumns的调整基于dom节点的宽度计算
      // 故使用 setTimeout 可以将列宽调整的操作推迟到下一个事件循环，确保 DOM 已经完全渲染。
      setTimeout(adjustColumns, 100);
    };

    // 保存监听器引用以便后续移除
    modelUpdatedHandlerRef.current = handleModelUpdated;

    // 监听数据更新事件，重新调整列宽
    api.addEventListener('modelUpdated', handleModelUpdated);
  }, []);

  // 拖拽选区功能事件监听器
  useEffect(() => {
    return setupDragSelection(gridApiRef);
  }, [setupDragSelection]);

  // 清理自定义选择控制器
  useEffect(() => {
    return cleanupSelectionController;
  }, [cleanupSelectionController]);

  return (
    <div className={styles.resultGrid}>
      <div className={styles.resultContent} ref={copyableRef}>
      {
        !columnInfos || columnInfos.length === 0
          ?
          <div className={styles.resultGrid__filter}>
            <Iconfont type='icon-yizhongzhi' style={{ marginRight: '4px' }} />
            {t('sdo_result_empty_filtered_field')}：{filterNames?.join(',')}
          </div>
          :
          <Spin spinning={loading || pending} wrapperClassName={styles.gridSpinContainer}>
            <ResultToolbar
              enabledActions={enabledActions}
              paneType={paneType}
              onRefresh={handleRefresh}
              onAddRow={handleAddRow}
              onDeleteRow={handleDeleteRowConfirm}
              onConfirmModify={handleConfirmModify}
              onScalePage={handleScaleView}
              onCancelModify={handleCancelModify}
              onViewCell={handleToolbarView}
              readOnly={!editable}
              refreshable={refreshable}
              isDataExportPermissionEnabled={isDataExportPermissionEnabled}
              applyDataExportPermission={applyDataExportPermission}
              onOpenExportModal={onOpenExportModal}
              showExported={dataExport.showExported}
              connectionType={connectionType}
              executePayload={executePayload}
              scale={scale === undefined ? 100 : scale}
              type={type}
              permissionResult={permissionResult}
              dataExport={dataExport}
              filterNames={filterNames}
              txMode={txMode}
              onOpenFieldsModal={() => { setVisibleDesensitizedFields(true) }}
              isDesensitized={isDesensitized}
              onSave={handleSave}
              onApplyDensens={onApplyDensens}
              maskedPathMap={maskedPathMap}
            />
            <div
              id={`${tabResultKey}gridWrapper`}
              ref={gridContainerRef}
              className={classNames(
                styles.gridWrapper,
                theme === 'dark' ? 'ag-theme-balham-dark' : 'ag-theme-balham',
                !canCopy && styles.unCopyable,
              )}
              tabIndex={0}
            >
              {
                agGridLoading && 
                <span className={styles.resultMask}>
                  <Spin spinning={agGridLoading}/>
                </span>
              }
              {/* 结果集 Table */}
              <AgGridReact
                {...gridConfig}
                {...infiniteModeOptions}
                columnDefs={columnDefs}
                cacheBlockSize={rowNum}
                defaultColDef={defaultColDef}
                datasource={datasource}
                onGridReady={handleGridReady}
                onCellFocused={handleCellFocused}
                onCellKeyDown={onCellKeyDown} // 单元格按键事件
                onCellClicked={handleCellClicked} // 自定义单元格点击处理
                onRowEditingStarted={handleStartRowEditing}
                onRowEditingStopped={handleStopRowEditing}
                context={agContext}
                suppressDragLeaveHidesColumns={true}
                key={JSON.stringify(detailedResultData)}
                onSelectionChanged={handleSelectionChanged}
                onSortChanged={() => {
                  setRowViewerResultData([])
                }}
                onCellContextMenu={(params) => {
                  // 处理右键菜单显示前的选区逻辑
                  if (contextMenuHandlersRef.current) {
                    contextMenuHandlersRef.current.handleBeforeContextMenu(params);
                  }
                }}
                suppressRowHoverHighlight={true}   // 禁用行高亮
                // 开启值缓存，【会影响方法中调用successCallback的执行，如后优化性能需要开启，需要修改successCallback调用处的逻辑】
                // valueCache={true}
                // 首次数据渲染完成回调
                onFirstDataRendered={onFirstDataRendered}
              />
            </div>
          </Spin>
      }
      </div>
      {
        visibleCellViewer && 
        <CellViewerModal
          gridApi={gridApiRef.current?.api || null}
          rowIndex={focusedRowIndex}
          rowData={focusedRowData}
          initRowData={isEmpty(focusedRowData?._initData) ? focusedRowData : focusedRowData?._initData}
          resultData={detailedResultData}
          //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
          editable={editable && connectionType !== 'DB2'}
          allowResultCopy={permissionResult?.resultCellCopy}
          visible={visibleCellViewer}
          allowBinaryCellDownload={permissionResult?.resultSetBinaryFileDownload}
          setVisible={setVisibleCellViewer}
          updateFocusedCell={updateFocusedCell}
          downloadFocusedCell={downloadFocusedCell}
          fetchFocusedCell={fetchFocusedCell}
          type={type!}
          isExplain={isExplain}
        />
      }
      {visibleRowViewer && (
        <RowViewerModal
          gridApi={gridApiRef.current?.api || null}
          rowData={rowViewerResultData?.find((row: any, index: number | null) => focusedRowIndex === index) || {}}
          resultData={detailedResultData}
          //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
          editable={editable && connectionType !== 'DB2'}
          visible={visibleRowViewer}
          setVisible={setVisibleRowViewer}
          updateFocusedCell={updateFocusedCell}
          downloadFocusedCell={downloadFocusedCell}
          fetchFocusedCell={fetchFocusedCell}
          rowIndex={focusedRowIndex}
          nextRowIndex={nextRowIndex}
          lastRowIndex={lastRowIndex}
          isLastRowIndex={
            (rowViewerResultData?.find((_row: any, index: number | null) => ((focusedRowIndex || 0) + 1) === index) || undefined)
            === undefined
          }
          setRowViewerResultData={() => {
            setRowViewerResultData([])
          }}
          resultAllowCopy={canCopyCell}
        />
      )}
      {
        visibleSelectedExport && 
        <AddSelectedResultExportModal
          result={result}
          visible={visibleSelectedExport}
          setVisible={setVisibleSelectedExport}
          gridApi={gridApiRef.current?.api || null}
          permissionResult={permissionResult}
        />
      }
      {/* 增加visibleCommonExport判断 会导致成功后弹窗不展示 */}
      <AddResultExportModal
        result={result}
        visible={visibleCommonExport}
        setVisible={setVisibleCommonExport}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />
      {/* 组件内走导出申请时候会导致弹框不出现， 因为visibleExportAll=false会被一起关闭*/}
      <ResultAllExport
        result={result}
        visible={visibleExportAll}
        setVisible={setVisibleExportAll}
        hanldeExportAll={handleExportAllResult}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />
      {
        visibleDesensitizedFields && 
        <DesensitizedFieldsModal
          result={result}
          visible={visibleDesensitizedFields}
          setVisible={setVisibleDesensitizedFields}
          doubleCheckType={doubleCheckType}
          filterNames={filterNames}
        />
      }
    </div>
  )
})