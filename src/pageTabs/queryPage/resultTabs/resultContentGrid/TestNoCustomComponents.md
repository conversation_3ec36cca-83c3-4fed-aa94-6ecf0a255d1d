# 🧪 测试方案：移除自定义组件解决内存泄漏

## 问题分析
基于内存快照分析，发现 AG-Grid 中的自定义组件可能是循环引用和内存泄漏的根源：

### 当前使用的自定义组件
1. **cellRenderer: 'SimpleTextRendererWithContextMenu'** - 文本渲染器 + 右键菜单
2. **cellEditor: 'textEditor'** - 自定义文本编辑器  
3. **headerComponentFramework: CustomHeader** - 自定义表头组件
4. **cellRenderer: 'RowIndexFromPropsRendererWithContextMenu'** - 行号渲染器 + 右键菜单

### 循环引用链
```
React组件 ↔ AG-Grid API ↔ cellRenderer ↔ Context ↔ Event Handlers ↔ React组件
```

## 测试方案

### 阶段1：完全移除自定义组件
在 ResultGridPagination.tsx 中临时注释掉所有自定义组件：

```typescript
// 第310行和513行 - 注释掉自定义 cellRenderer
// cellRenderer: 'SimpleTextRendererWithContextMenu',

// 第311行和514行 - 注释掉自定义 cellEditor  
// cellEditor: 'textEditor',
// cellEditorParams,

// 第313行和517行 - 注释掉自定义 headerComponent
// headerComponentFramework: CustomHeader,
// headerComponentParams: { ... },

// 第675行 - 注释掉行号渲染器
// cellRenderer: 'RowIndexFromPropsRendererWithContextMenu',
```

### 阶段2：使用原生 AG-Grid 组件替代
```typescript
// 替代方案：
{
  field: columnName,
  headerName: columnName, // 使用原生表头
  sortable: sortable,
  filter: filterable,
  resizable: true,
  editable: false, // 暂时禁用编辑功能
  // 不使用任何自定义组件
}
```

### 阶段3：验证内存泄漏是否解决
1. 执行相同的滚动操作
2. 拍摄内存快照
3. 对比 detached DOM 数量
4. 检查循环引用是否消失

## 预期结果
- **成功**：detached DOM 不再无限增长
- **副作用**：失去右键菜单、编辑功能、自定义表头等功能
- **结论**：确认自定义组件是内存泄漏的根源

## 后续优化方案
如果测试证实自定义组件是问题根源，可以考虑：

1. **重构自定义组件**：移除循环引用
2. **使用 WeakMap**：存储组件引用
3. **手动清理**：在组件卸载时断开引用链
4. **简化功能**：减少不必要的自定义组件

## 实施步骤
1. 备份当前代码
2. 注释掉所有自定义组件相关代码
3. 测试内存表现
4. 记录结果
5. 恢复代码并制定优化方案
