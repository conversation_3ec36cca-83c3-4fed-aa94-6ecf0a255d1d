import type { ICellRendererParams, GridApi, ColumnApi, CellContextMenuEvent } from '@ag-grid-community/core'
import { copyTextToClipboard } from 'src/util'
import type { CustomSelectionController } from './CustomSelectionController'

export class ContextMenuHandlers {
  private gridApiRef: React.MutableRefObject<{ api: GridApi; columnApi: ColumnApi } | null>
  private customSelectionControllerRef: React.MutableRefObject<CustomSelectionController | null>

  constructor(
    gridApiRef: React.MutableRefObject<{ api: GridApi; columnApi: ColumnApi } | null>,
    customSelectionControllerRef: React.MutableRefObject<CustomSelectionController | null>
  ) {
    this.gridApiRef = gridApiRef
    this.customSelectionControllerRef = customSelectionControllerRef
  }

  // 处理右键菜单显示前的选区逻辑
  handleBeforeContextMenu = (params: CellContextMenuEvent): void => {
    if (!this.customSelectionControllerRef.current) return;

    const customController = this.customSelectionControllerRef.current;

    // 检查点击位置是否已在选区内
    const isInSelection = customController.isCellInSelection({
      rowIndex: params.rowIndex ?? undefined,
      column: params.column,
      node: params.node
    });

    // 如果已在选区内，不做任何操作，直接显示菜单
    if (isInSelection) {
      return;
    }

    // 判断是否点击的是行号列（行头）
    if (customController.isRowNumberColumn(params.column)) {
      // 点击行头：清除所有选区，选中当前行
      customController.clearAllSelections();
      if (params.node) {
        customController.handleRowSelection(params.node, false, false);
      }
      return;
    }

    // 清除所有选区，选中当前单元格
    customController.clearAllSelections();
    
    // 模拟单元格点击事件来选中当前单元格
    const cellClickParams = {
      ...params,
      event: params.event || new MouseEvent('click') // 创建一个模拟的点击事件
    };
    
    customController.onCellClicked(cellClickParams);
  };

  // 处理复制选中列标题
  handleCopySelectedColumnTitles = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current) return
    
    const selectionContext = this.customSelectionControllerRef.current.getCurrentSelectionContext()
    const selectedColumns = selectionContext.selectedColumns
    // 如果没有选中列，直接返回

    if (selectedColumns.length === 0) return
    
    // 获取所有显示的列，按显示顺序排序
    const allDisplayedColumns = this.gridApiRef.current?.columnApi?.getAllDisplayedColumns() || []
    const orderedSelectedColumns = allDisplayedColumns
      .filter(col => selectedColumns.includes(col?.getColId?.() || ''))
      .map(col => {
        const colDef = col?.getColDef?.()
        return colDef?.headerName || colDef?.field || col?.getColId?.() || ''
      })
    
    // 用空格连接标题
    const titleText = orderedSelectedColumns.join(' ')
    copyTextToClipboard(titleText)
  }

  // 处理复制选中列数据
  handleCopySelectedColumnData = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.api) return
    const selectionContext = this.customSelectionControllerRef.current.getCurrentSelectionContext()
    const selectedColumns = selectionContext.selectedColumns
    
    if (selectedColumns.length === 0) return
    
    // 获取所有显示的列，按显示顺序排序
    const allDisplayedColumns = this.gridApiRef.current.columnApi?.getAllDisplayedColumns() || []
    const orderedSelectedColIds = allDisplayedColumns
      .filter(col => selectedColumns.includes(col.getColId()))
      .map(col => col.getColId())
    
    const rowsData: string[] = []
    
    // 遍历所有行
    this.gridApiRef.current.api.forEachNode((rowNode) => {
      const cellValues: string[] = []
      
      // 按列顺序获取每列的值
      orderedSelectedColIds.forEach(colId => {
        const value = this.gridApiRef.current?.api?.getValue(colId, rowNode)
        cellValues.push(value != null ? String(value) : '')
      })
      
      // 用制表符连接同一行的单元格数据
      rowsData.push(cellValues.join('\t'))
    })
    
    // 用换行符连接所有行数据
    const dataText = rowsData.join('\n')
    copyTextToClipboard(dataText)
  }

  // 处理复制单个单元格数据
  handleCopySingleCellData = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.api) return
    
    const selectedCellIds = this.customSelectionControllerRef.current.getSelectionState().selectedCellIds
    if (selectedCellIds.size !== 1) return
    
    // 获取选中的单个单元格
    const cellId = Array.from(selectedCellIds)[0]
    const cellInfo = this.customSelectionControllerRef.current.parseCellId(cellId)
    
    if (!cellInfo) return
    
    // 获取单元格的值
    const rowNode = this.gridApiRef.current.api.getDisplayedRowAtIndex(cellInfo.rowIndex)
    if (!rowNode) return
    
    const value = this.gridApiRef.current.api.getValue(cellInfo.colId, rowNode)
    const cellText = value != null ? String(value) : ''
    
    // 复制到剪贴板
    copyTextToClipboard(cellText)
  }

  // 处理复制选中单元格数据（多个单元格）
  handleCopySelectedCellsData = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.api) return
    
    const selectionContext = this.customSelectionControllerRef.current.getCurrentSelectionContext()
    const selectedCellIds = this.customSelectionControllerRef.current.getSelectionState().selectedCellIds
    if (selectedCellIds.size === 0) return
    
    // 解析所有选中的单元格位置
    const cellPositions: Array<{ rowIndex: number; colId: string; colIndex: number }> = []
    const allDisplayedColumns = this.gridApiRef.current.columnApi?.getAllDisplayedColumns() || []
    
    selectedCellIds.forEach(cellId => {
      const cellInfo = this.customSelectionControllerRef.current!.parseCellId(cellId)
      if (cellInfo) {
        const colIndex = allDisplayedColumns.findIndex(col => col.getColId() === cellInfo.colId)
        if (colIndex !== -1) {
          cellPositions.push({
            rowIndex: cellInfo.rowIndex,
            colId: cellInfo.colId,
            colIndex: colIndex
          })
        }
      }
    })
    
    if (cellPositions.length === 0) return
    
    // 如果是规则选择，使用原有的紧凑格式
    if (selectionContext.isRegularCellSelection) {
      // 按行和列排序
      cellPositions.sort((a, b) => {
        if (a.rowIndex !== b.rowIndex) return a.rowIndex - b.rowIndex
        return a.colIndex - b.colIndex
      })
      
      // 组织数据为矩形格式
      const dataByRow: Map<number, Map<number, string>> = new Map()
      
      cellPositions.forEach(pos => {
        if (!dataByRow.has(pos.rowIndex)) {
          dataByRow.set(pos.rowIndex, new Map())
        }
        
        const rowNode = this.gridApiRef.current?.api?.getDisplayedRowAtIndex(pos.rowIndex)
        if (rowNode) {
          const value = this.gridApiRef.current?.api?.getValue(pos.colId, rowNode)
          dataByRow.get(pos.rowIndex)!.set(pos.colIndex, value != null ? String(value) : '')
        }
      })
      
      // 构建输出文本
      const rowsData: string[] = []
      const sortedRowIndices = Array.from(dataByRow.keys()).sort((a, b) => a - b)
      
      sortedRowIndices.forEach(rowIndex => {
        const rowData = dataByRow.get(rowIndex)!
        const sortedColIndices = Array.from(rowData.keys()).sort((a, b) => a - b)
        const cellValues = sortedColIndices.map(colIndex => rowData.get(colIndex) || '')
        rowsData.push(cellValues.join('\t'))
      })
      
      const dataText = rowsData.join('\n')
      copyTextToClipboard(dataText)
    } else {
      // 不规则选择使用稀疏复制算法
      this.handleIrregularCellsCopy(cellPositions, allDisplayedColumns, false)
    }
  }

  // 处理不规则单元格选择的稀疏复制
  private handleIrregularCellsCopy = (
    cellPositions: Array<{ rowIndex: number; colId: string; colIndex: number }>,
    allDisplayedColumns: any[],
    includeTitle: boolean
  ) => {
    if (cellPositions.length === 0) return

    // 找到边界：最小和最大的行索引、列索引
    const minRowIndex = Math.min(...cellPositions.map(pos => pos.rowIndex))
    const maxRowIndex = Math.max(...cellPositions.map(pos => pos.rowIndex))
    const minColIndex = Math.min(...cellPositions.map(pos => pos.colIndex))
    const maxColIndex = Math.max(...cellPositions.map(pos => pos.colIndex))

    // 创建一个稀疏矩阵来存储数据
    const sparseMatrix: Map<number, Map<number, string>> = new Map()

    // 填充稀疏矩阵
    cellPositions.forEach(pos => {
      if (!sparseMatrix.has(pos.rowIndex)) {
        sparseMatrix.set(pos.rowIndex, new Map())
      }
      
      const rowNode = this.gridApiRef.current?.api?.getDisplayedRowAtIndex(pos.rowIndex)
      if (rowNode) {
        const value = this.gridApiRef.current?.api?.getValue(pos.colId, rowNode)
        sparseMatrix.get(pos.rowIndex)!.set(pos.colIndex, value != null ? String(value) : '')
      }
    })

    const resultLines: string[] = []

    // 如果需要包含标题，先添加标题行
    if (includeTitle) {
      const titleRow: string[] = []
      for (let colIndex = minColIndex; colIndex <= maxColIndex; colIndex++) {
        const column = allDisplayedColumns[colIndex]
        if (column) {
          const colDef = column.getColDef()
          const title = colDef?.headerName || colDef?.field || column.getColId() || ''
          titleRow.push(title)
        } else {
          titleRow.push('')
        }
      }
      resultLines.push(titleRow.join('\t'))
    }

    // 按行索引遍历，构建每一行的数据
    for (let rowIndex = minRowIndex; rowIndex <= maxRowIndex; rowIndex++) {
      const rowData = sparseMatrix.get(rowIndex)
      if (rowData && rowData.size > 0) {
        // 这一行有选中的单元格
        const rowValues: string[] = []
        for (let colIndex = minColIndex; colIndex <= maxColIndex; colIndex++) {
          const cellValue = rowData.get(colIndex)
          rowValues.push(cellValue || '') // 未选中的单元格为空字符串
        }
        resultLines.push(rowValues.join('\t'))
      }
    }

    const finalText = resultLines.join('\n')
    copyTextToClipboard(finalText)
  }

  // 通用方法：从选中的单元格中提取列标题
  private _getTitlesFromSelectedCells(): string[] {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.columnApi) return []
    
    const selectedCellIds = this.customSelectionControllerRef.current.getSelectionState().selectedCellIds
    if (selectedCellIds.size === 0) return []
    
    // 获取所有涉及的列ID（去重）
    const involvedColIds = new Set<string>()
    selectedCellIds.forEach(cellId => {
      const cellInfo = this.customSelectionControllerRef.current!.parseCellId(cellId)
      if (cellInfo) {
        involvedColIds.add(cellInfo.colId)
      }
    })
    
    // 按列在表格中的显示顺序排序
    const allDisplayedColumns = this.gridApiRef.current?.columnApi?.getAllDisplayedColumns() || []
    const orderedColumnTitles = allDisplayedColumns
      .filter(col => involvedColIds.has(col?.getColId?.() || ''))
      .map(col => {
        const colDef = col?.getColDef?.()
        return colDef?.headerName || colDef?.field || col?.getColId?.() || ''
      })
    
    return orderedColumnTitles
  }

  // 处理复制选中单元格的标题（多个单元格）
  handleCopySelectedCellsTitles = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.columnApi) return
    
    const selectedCellIds = this.customSelectionControllerRef.current.getSelectionState().selectedCellIds
    
    if (selectedCellIds.size === 0) return
    
    // 使用通用方法获取标题
    const orderedColumnTitles = this._getTitlesFromSelectedCells()
    
    if (orderedColumnTitles.length === 0) return
    
    const titleText = orderedColumnTitles.join('\t')
    copyTextToClipboard(titleText)
  }

  // 处理单元格区域的与标题一起复制（只复制选中区域的数据和标题）
  handleCopySelectedCellsWithTitle = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.api) return
    
    const selectionContext = this.customSelectionControllerRef.current.getCurrentSelectionContext()
    const selectedCellIds = this.customSelectionControllerRef.current.getSelectionState().selectedCellIds
    if (selectedCellIds.size === 0) return
    
    // 解析所有选中的单元格位置
    const cellPositions: Array<{ rowIndex: number; colId: string; colIndex: number }> = []
    const allDisplayedColumns = this.gridApiRef.current.columnApi?.getAllDisplayedColumns() || []
    
    selectedCellIds.forEach(cellId => {
      const cellInfo = this.customSelectionControllerRef.current!.parseCellId(cellId)
      if (cellInfo) {
        const colIndex = allDisplayedColumns.findIndex(col => col.getColId() === cellInfo.colId)
        if (colIndex !== -1) {
          cellPositions.push({
            rowIndex: cellInfo.rowIndex,
            colId: cellInfo.colId,
            colIndex: colIndex
          })
        }
      }
    })
    
    if (cellPositions.length === 0) return
    
    // 如果是规则选择，使用原有的紧凑格式
    if (selectionContext.isRegularCellSelection) {
      // 按行和列排序
      cellPositions.sort((a, b) => {
        if (a.rowIndex !== b.rowIndex) return a.rowIndex - b.rowIndex
        return a.colIndex - b.colIndex
      })
      
      // 使用通用方法获取标题
      const orderedColumnTitles = this._getTitlesFromSelectedCells()
      
      // 组织数据为矩形格式
      const dataByRow: Map<number, Map<number, string>> = new Map()
      
      cellPositions.forEach(pos => {
        if (!dataByRow.has(pos.rowIndex)) {
          dataByRow.set(pos.rowIndex, new Map())
        }
        
        const rowNode = this.gridApiRef.current?.api?.getDisplayedRowAtIndex(pos.rowIndex)
        if (rowNode) {
          const value = this.gridApiRef.current?.api?.getValue(pos.colId, rowNode)
          dataByRow.get(pos.rowIndex)!.set(pos.colIndex, value != null ? String(value) : '')
        }
      })
      
      // 构建输出文本：标题 + 数据
      const resultLines: string[] = []
      
      // 添加标题行
      resultLines.push(orderedColumnTitles.join('\t'))
      
      // 添加数据行
      const sortedRowIndices = Array.from(dataByRow.keys()).sort((a, b) => a - b)
      sortedRowIndices.forEach(rowIndex => {
        const rowData = dataByRow.get(rowIndex)!
        const sortedColIndices = Array.from(rowData.keys()).sort((a, b) => a - b)
        const cellValues = sortedColIndices.map(colIndex => rowData.get(colIndex) || '')
        resultLines.push(cellValues.join('\t'))
      })
      
      const finalText = resultLines.join('\n')
      copyTextToClipboard(finalText)
    } else {
      // 不规则选择使用稀疏复制算法，包含标题
      this.handleIrregularCellsCopy(cellPositions, allDisplayedColumns, true)
    }
  }

  // 处理复制选中列的标题和数据（列模式下的"与标题一起复制"）
  handleCopySelectedFullColumnDataWithTitle = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.api || !this.gridApiRef.current?.columnApi) return
    
    const selectionContext = this.customSelectionControllerRef.current.getCurrentSelectionContext()
    const selectedColumns = selectionContext.selectedColumns
    
    if (selectedColumns.length === 0) return
    
    // 获取所有显示的列，按显示顺序排序
    const allDisplayedColumns = this.gridApiRef.current.columnApi?.getAllDisplayedColumns() || []
    const orderedSelectedColumns = allDisplayedColumns.filter(col => selectedColumns.includes(col.getColId()))
    const orderedSelectedColIds = orderedSelectedColumns.map(col => col.getColId())
    
    // 生成标题行（用制表符分隔）
    const headerText = orderedSelectedColumns
      .map(col => {
        const colDef = col?.getColDef?.()
        return colDef?.headerName || colDef?.field || col?.getColId?.() || ''
      })
      .join('\t')
    
    // 生成数据行（每行内部用制表符分隔）
    const dataRows: string[] = []
    this.gridApiRef.current.api.forEachNode((rowNode) => {
      const cellValues: string[] = []
      orderedSelectedColIds.forEach(colId => {
        const value = this.gridApiRef.current?.api?.getValue(colId, rowNode)
        cellValues.push(value != null ? String(value) : '')
      })
      dataRows.push(cellValues.join('\t'))
    })
    
    // 组合标题行和数据行（用换行符分隔）
    const finalText = [headerText, ...dataRows].join('\n')
    copyTextToClipboard(finalText)
  }

  // 处理全选模式下的复制标题功能
  handleCopySelectedAllCellsTitles = (params: ICellRendererParams) => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.columnApi) return
    
    // 获取所有显示的列，按显示顺序排序
    const allDisplayedColumns = this.gridApiRef.current.columnApi?.getAllDisplayedColumns() || []
    const orderedColumnTitles = allDisplayedColumns
      .map(col => {
        const colDef = col?.getColDef?.()
        return colDef?.headerName || colDef?.field || col?.getColId?.() || ''
      })
    
    // 用制表符连接标题（与其他复制功能保持一致）
    const titleText = orderedColumnTitles.join('\t')
    copyTextToClipboard(titleText)
  }

  // 统一的复制操作入口（用于键盘快捷键调用）
  handleCopySelection = () => {
    if (!this.customSelectionControllerRef.current) {
      return
    }

    const selectionContext = this.customSelectionControllerRef.current.getCurrentSelectionContext()
    // 检查是否是全选状态
    if (selectionContext.isSelectAllActive) {
      // 全选：调用复制全部数据的方法
      this.handleCopyAllCells()
      return
    }
    
    // 根据选择模式调用相应的复制方法
    switch (selectionContext.selectionMode) {
      case 'cell':
        if (selectionContext.isSingleCell) {
          // 单个单元格选择：调用单个单元格复制方法
          this.handleCopySingleCellData({} as any)
        } else {
          // 多个单元格选择：调用复制选中单元格数据的方法
          this.handleCopySelectedCellsData({} as any)
        }
        break
      case 'column':
        // 列选择：调用复制选中列数据的方法
        this.handleCopySelectedColumnData({} as any)
        break
      case 'row':
        // 行选择：AG-Grid 的行选择通常通过 getSelectedRows() 获取
        // 这里我们需要实现一个行复制的逻辑
        this.handleCopySelectedRows()
        break
      default:
        break
    }
  }

  // 处理复制选中行的数据
  private handleCopySelectedRows = () => {
    if (!this.gridApiRef.current?.api) return
    
    const selectedRows = this.gridApiRef.current.api.getSelectedRows() || []
    if (selectedRows.length === 0) return
    
    // 获取所有显示的列，按显示顺序排序
    const allDisplayedColumns = this.gridApiRef.current.columnApi?.getAllDisplayedColumns() || []
    const orderedColIds = allDisplayedColumns.map(col => col.getColId())
    
    const rowsData: string[] = []
    
    // 遍历选中的行
    selectedRows.forEach(rowData => {
      const cellValues: string[] = []
      
      // 按列顺序获取每列的值
      orderedColIds.forEach(colId => {
        const value = rowData[colId]
        cellValues.push(value != null ? String(value) : '')
      })
      
      // 用制表符连接同一行的单元格数据
      rowsData.push(cellValues.join('\t'))
    })
    
    // 用换行符连接所有行数据
    const dataText = rowsData.join('\n')
    copyTextToClipboard(dataText)
  }

  // 处理复制全部单元格数据
  private handleCopyAllCells = () => {
    if (!this.gridApiRef.current?.api) return
    
    // 获取所有显示的列，按显示顺序排序
    const allDisplayedColumns = this.gridApiRef.current.columnApi?.getAllDisplayedColumns() || []
    const orderedColIds = allDisplayedColumns.map(col => col.getColId())
    
    const rowsData: string[] = []
    
    // 遍历所有行
    this.gridApiRef.current.api.forEachNode((rowNode) => {
      const cellValues: string[] = []
      
      // 按列顺序获取每列的值
      orderedColIds.forEach(colId => {
        const value = this.gridApiRef.current?.api?.getValue(colId, rowNode)
        cellValues.push(value != null ? String(value) : '')
      })
      
      // 用制表符连接同一行的单元格数据
      rowsData.push(cellValues.join('\t'))
    })
    
    // 用换行符连接所有行数据
    const dataText = rowsData.join('\n')
    copyTextToClipboard(dataText)
  }

  // 获取选择相关的行数据（用于结果集生成）
  getSelectedRowsDataForResultSet = (): any[] => {
    if (!this.customSelectionControllerRef.current || !this.gridApiRef.current?.api) return []
    
    const selectionContext = this.customSelectionControllerRef.current.getCurrentSelectionContext()
    
    if (selectionContext.selectionMode === 'column') {
      // 列选择：获取选中列对应的所有行数据
      const allRowsData: any[] = []
      
      this.gridApiRef.current.api.forEachNode((rowNode) => {
        if (rowNode.data) {
          // 对于列选择，我们返回完整的行数据，让后续处理决定如何使用
          allRowsData.push(rowNode.data)
        }
      })
      return allRowsData
    } else if (selectionContext.selectionMode === 'cell') {
      if (selectionContext.isSingleCell) {
        // 单个单元格选择：获取当前单元格所在行数据
        const selectedCellIds = this.customSelectionControllerRef.current.getSelectionState().selectedCellIds
        if (selectedCellIds.size === 1) {
          const cellId = Array.from(selectedCellIds)[0]
          const cellInfo = this.customSelectionControllerRef.current.parseCellId(cellId)
          if (cellInfo) {
            const rowNode = this.gridApiRef.current.api.getDisplayedRowAtIndex(cellInfo.rowIndex)
            if (rowNode) {
              return [rowNode.data]
            }
          }
        }
        return []
      } else {
        // 多个单元格选择：获取涉及的所有行数据
        const selectedCellIds = this.customSelectionControllerRef.current.getSelectionState().selectedCellIds
        const involvedRowIndices = new Set<number>()
        
        selectedCellIds.forEach(cellId => {
          const cellInfo = this.customSelectionControllerRef.current!.parseCellId(cellId)
          if (cellInfo) {
            involvedRowIndices.add(cellInfo.rowIndex)
          }
        })
        
        const involvedRowsData: any[] = []
        // 按行索引排序，确保数据顺序一致
        const sortedRowIndices = Array.from(involvedRowIndices).sort((a, b) => a - b)
        
        sortedRowIndices.forEach(rowIndex => {
          const rowNode = this.gridApiRef.current?.api?.getDisplayedRowAtIndex(rowIndex)
          if (rowNode) {
            involvedRowsData.push(rowNode.data)
          }
        })
        return involvedRowsData
      }
    } else if (selectionContext.selectionMode === 'row') {
      // 行选择：使用 ag-Grid 的选中行
      return this.gridApiRef.current.api.getSelectedRows() || []
    } else {
      // 无选择或未知模式
      return []
    }
  }
}