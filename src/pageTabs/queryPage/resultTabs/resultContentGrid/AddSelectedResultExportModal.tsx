import React, { useState, useMemo, useRef, useEffect } from 'react'
import { Button, Form, Input, Select } from 'antd'
import { GridApi } from '@ag-grid-community/core'
import loadable from '@loadable/component'
import type XLSX from 'xlsx'
import styles from './grid.module.scss'
import { UIModal } from 'src/components'
import { warnIcon } from './AddResultExportModal'
import { checkFileName, renderNotSupportedType } from 'src/util/export'
import { useTranslation } from 'react-i18next'

const Xlsx = loadable.lib(() => import('xlsx'))

const ExportFormats = ['EXCEL'] as const

const ExportSelectOptions = ExportFormats.map((format) => ({
  value: format,
  label: format,
  disabled: false
}))

interface Iprops {
  gridApi: GridApi | null
  visible: boolean
  setVisible: (visible: boolean) => void
  [p: string]: any
}

export const AddSelectedResultExportModal = (props: Iprops) => {
  const { gridApi, visible, setVisible, result } = props
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
  const [addResultExportForm] = Form.useForm()
  const [showPrompt, setShowPrompt] = useState<boolean>(false) //导出提示
  const xlsxRef = useRef<any>()
  const { t } = useTranslation()

  const selectRows = useMemo(() => {
    if (!gridApi || !visible) return []
    return gridApi.getSelectedRows()
  }, [gridApi, visible])

  // 导出提示实现抽屉效果
  useEffect(() => {
    if (visible) {
      const optionsNode: any = document.querySelector(`.${styles.promptBoby}`);
      if (optionsNode && optionsNode.style) {
        optionsNode.setAttribute("real-height", window.getComputedStyle(optionsNode).height);
        optionsNode.style.height = "0px";
      }
    }
    else setShowPrompt(false)
  }, [visible])

  const operate = () => {
    const optionsNode: any = document.querySelector(`.${styles.promptBoby}`);
    const realHeight = optionsNode.getAttribute("real-height");
    if (!showPrompt) {
      optionsNode.style.height = realHeight;
    } else {
      optionsNode.style.height = "0px";
    }
    setShowPrompt(!showPrompt)
  }

  const handleSubmitAddResultExport = () => {
    // todo 暂时只支持 excel
    if (!gridApi) return
    addResultExportForm
      .validateFields()
      .then((values) => {
        setConfirmLoading(true)
        const allColumns = gridApi.getColumnDefs()
        const columns = allColumns?.filter((column: any) => column?.field) || []
        const colFields = columns.map((column: any) => column?.field)
        const showSelectRows = selectRows.map((row: any) => {
          return colFields.reduce((acc: any, cur: any) => {
            return { ...acc, [cur]: row?.[cur]||'' }
          }, {})
        })
        const xlsx: typeof XLSX = xlsxRef.current?.default
        if (!xlsx || !showSelectRows) return
        const { fileName } = values
        var ws = xlsx.utils.json_to_sheet(showSelectRows)
        const wb = xlsx.utils.book_new()
        xlsx.utils.book_append_sheet(wb, ws, fileName)
        xlsx.writeFile(wb, `${fileName}.xlsx`)
        setVisible(false)
        setConfirmLoading(false)
      })
  }

  const initExportFormatValuie = ExportSelectOptions?.filter(i => !i?.disabled)[0]?.value
  return (
    <UIModal
      title={t('sdo_select_row_export')}
      visible={visible}
      onCancel={() => setVisible(false)}
      onOk={handleSubmitAddResultExport}
      afterClose={() => addResultExportForm.resetFields()}
      width={560}
      confirmLoading={confirmLoading}
    >
      <div className={styles.exportPrompt}>
        <div className={styles.promptHearder} style={showPrompt ? { borderBottom: "1px solid #E5E6EB" } : {}}>
          <div className={styles.title}>
            {warnIcon}
            <div className={styles.titleContent}>{t('sdo_export_tips')}</div>
          </div>
          <Button type="link" className={styles.showDetailBtn} onClick={operate}>{showPrompt ? t('sdo_fold_in') : t('sdo_for_details')}</Button>
        </div>
        <div className={styles.promptBoby} id='drawer'>
          <p>{t('sdo_not_supt_ex_field_types')}：<span>{renderNotSupportedType(result?.dataSourceType)}</span></p>
        </div>
      </div>
      <Form
        form={addResultExportForm}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 15 }}
      >
        <Form.Item
          name="fileName"
          label={t('sdo_file_name')}
          rules={[
            { required: true, message: t('sdo_fill_file_name') },
            {
              validator(_, value) {
                try {
                  return checkFileName(value)
                } catch (error) {
                  return Promise.reject(error)
                }
              },
            }
          ]}
        >
          <Input></Input>
        </Form.Item>
        <Form.Item
          name="exportFormat"
          label={t('sdo_export_format')}
          rules={[{ required: true, message: t('sdo_select_export_format') }]}
          initialValue={initExportFormatValuie}
        >
          <Select options={ExportSelectOptions}></Select>
        </Form.Item>
        <Form.Item label={t('sdo_selected_rows')}>
          {selectRows ? selectRows.length : 0}
        </Form.Item>
      </Form>
      <Xlsx ref={xlsxRef} />
    </UIModal>
  )
}
