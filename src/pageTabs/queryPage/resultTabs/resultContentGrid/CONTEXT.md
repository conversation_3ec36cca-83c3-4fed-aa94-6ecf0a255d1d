# 结果表格分页系统 - 功能模块文档

## 结果表格架构

### 核心组件体系
```
ResultGridPagination (主控制器)
├── AG-Grid 数据网格 (无限滚动模式)
├── N+1 分页控制器 (ResultGirdPagination/index.tsx)
├── 自定义选择控制器 (CustomSelectionController)
├── 工具栏系统 (ResultToolbar)
└── 弹窗组件群 (导出、查看器等)
```

### N+1 分页算法
该系统实现了创新的 N+1 分页策略，通过请求 `pageSize + 1` 条数据来判断是否存在下一页：

```typescript
// 核心判断逻辑 (ResultGridPagination.tsx:2322-2334)
if (resultData?.length === (curPagination?.pageSize || rowNum) + 1) {
  hasNext = true;
  actualResultData = resultData.slice(0, pageSize); // 只显示前 pageSize 条
  calculatedTotal = null; // 有下一页时不设置总数，保持 N+1 逻辑
} else {
  hasNext = false;
  actualResultData = resultData;
  calculatedTotal = actualResultData.length + startRow; // 最后一页时计算准确总数
}
```

### 智能总数显示策略
分页组件采用分层总数显示逻辑 (components/ResultGirdPagination/index.tsx:110-122)：
1. **优先级1**: 父组件传递的总数（N+1 自动计算的准确值）
2. **优先级2**: 用户主动查询的总数
3. **回退机制**: 显示 `N+` 格式提示用户查询总数

## 实现模式

### 状态管理架构
```typescript
// 分页状态结构
interface PaginationState {
  pageNumber: number;        // 当前页码
  pageSize: number;         // 每页大小  
  total: number | null;     // 总数（null 表示使用 N+1 逻辑）
  maxNumber: number | null; // 最大页数
}

// 编辑状态机
type IGridStatus = 'NORMAL' | 'UPDATE' | 'INSERT' | 'DELETE' | 'CLONE'
```

### 缓存策略
- **多级缓存**: `cachePageResultDataRef` (当前页) + `cacheAllPageResultDataRef` (所有页)
- **缓存失效**: 筛选、排序、刷新时自动清理缓存
- **性能优化**: 优先从缓存加载，减少 API 调用

### 筛选和排序集成
```typescript
// 筛选逻辑 (ResultGridPagination.tsx:2422-2434)
const onFilterChanged = () => {
  cacheAllPageResultDataRef.current = {}; // 清除缓存
  setHasNextPage(false);                   // 重置下一页状态
  setCurPagination({
    ...curPagination,
    pageNumber: 1,
    total: null // 重置总数，让 N+1 逻辑重新计算
  })
}
```

## 关键文件和结构

### 主要组件文件
- **`ResultGridPagination.tsx`**: 主控制器，实现 N+1 分页逻辑和数据管理
- **`components/ResultGirdPagination/index.tsx`**: N+1 分页 UI 组件
- **`services/CustomSelectionController.ts`**: 自定义选择控制器
- **`hooks/useSearch.ts`**: 搜索功能与分页协调
- **`hooks/useSelectionController.ts`**: 选择状态管理

### 配置和工具
- **`gridConfig/GridConfig.ts`**: AG-Grid 配置和框架组件注册
- **`utils/matchUtils.ts`**: 搜索匹配工具
- **`types/selection.ts`**: 选择功能类型定义

### 关键钩子函数
- **`useGridStatus`**: 编辑状态机管理 (NORMAL/UPDATE/INSERT/DELETE/CLONE)
- **`useSelectionController`**: 处理复杂选区交互（行、列、单元格、拖拽选区）
- **`useSearch`**: 搜索高亮和分页后重新触发逻辑

## 集成点

### Redux 状态集成
- **`queryTabs` slice**: 查询参数、执行状态、分页大小同步
- **`resultTabs` slice**: 结果标签页状态、缩放比例管理
- **状态同步**: 分页大小变更时自动更新 Redux store

### API 集成模式
```typescript
// 数据获取流程
commonGetRows() → fetchBlockDataOrFromStore() → executeSqlSegment()
               ↓
           递归轮询查询状态 → 返回结果数据 → N+1 逻辑处理
```

### 与其他组件协调
- **工具栏系统**: 传递分页状态给 `ResultToolbar`
- **搜索功能**: 分页变化后自动重新执行搜索
- **导出功能**: 支持当前页、选中行、全部结果的导出

## 开发模式

### 性能优化策略
1. **虚拟滚动**: AG-Grid 无限模式，支持大数据集
2. **智能缓存**: 避免重复 API 调用
3. **防抖处理**: 分页加载使用 150ms 防抖
4. **渲染优化**: 使用 `memo`、`useMemo`、`useCallback` 减少重渲染

### 错误处理模式
- **编辑状态检查**: 未保存时阻止分页操作
- **API 错误处理**: 显示错误信息并回退状态
- **数据一致性**: 操作失败时恢复之前状态

### 无限滚动删除机制
针对无限滚动模式下的删除操作，系统采用了专门的处理机制来避免虚拟索引与实际数据索引不匹配的问题：

```typescript
// 数据内容匹配删除 (ResultGrid.tsx:968-972)
allResultDataRef.current = allResultDataRef.current?.filter((item) => {
  const shouldDelete = selectedNodesData?.some((selectedData) => isEqual(item, selectedData))
  return !shouldDelete
})
```

**关键特性**:
- **内容匹配删除**: 使用 `isEqual` 深度比较替代索引匹配，确保删除准确性
- **本地数据源**: 通过 `notSupportPageResultDataRef.current` 实现正确的分页显示
- **删除状态标志**: `isDeletingRef.current` 标志位绕过保存状态检查，允许数据刷新
- **缓存管理**: `api.purgeInfiniteCache()` + `api.setRowCount()` 确保显示同步

### 测试和调试
- **控制台日志**: N+1 逻辑关键步骤的详细日志
- **状态追踪**: 分页状态变化的完整记录
- **性能监控**: 数据加载时间和渲染性能跟踪

## 开发指南

### 添加新的分页功能
1. 在 `commonGetRows` 中处理数据逻辑
2. 更新 `paginationData` 计算
3. 在 N+1 组件中添加 UI 控制
4. 测试缓存失效和状态同步

### 自定义分页行为
- 修改 `gridConfig/GridConfig.ts` 中的无限滚动配置
- 调整 `N1Pagination` 组件的显示逻辑
- 更新 Redux store 同步机制

### 性能调优建议
- 监控 `cacheBlockSize` 对性能的影响
- 优化复杂查询的分页加载策略
- 使用 React DevTools 分析渲染性能