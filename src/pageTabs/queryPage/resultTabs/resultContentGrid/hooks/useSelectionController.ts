import { useRef, useState, useCallback } from 'react';
import type { GridReadyEvent, RowNode, Column, IRowNode } from '@ag-grid-community/core';
import { CustomSelectionController } from '../services/CustomSelectionController';
import { ContextMenuHandlers } from '../services/ContextMenuHandlers';
import type { SelectionContext } from '../types/selection';
import { indexColId } from 'src/components';

interface UseSelectionControllerProps {
  isScrollLoading: boolean; // 是否为无限滚动模式
}

export const useSelectionController = ({ isScrollLoading }: UseSelectionControllerProps) => {
  // 自定义选择控制器相关
  const customSelectionControllerRef = useRef<CustomSelectionController | null>(null);
  const gridContainerRef = useRef<HTMLDivElement>(null);
  const [selectionContext, setSelectionContext] = useState<SelectionContext | null>(null);
  
  // 缓存上一次鼠标所在的单元格位置，用于智能更新策略
  const lastMouseCellRef = useRef<string | null>(null);
  
  // 右键菜单处理器
  const contextMenuHandlersRef = useRef<ContextMenuHandlers | null>(null);
  const [contextHandlersReady, setContextHandlersReady] = useState(false);

  // 更新选择上下文信息
  const updateSelectionContext = useCallback(() => {
    if (customSelectionControllerRef.current) {
      const context = customSelectionControllerRef.current.getCurrentSelectionContext();
      setSelectionContext(context);
    }
  }, []);

  // 从鼠标事件获取单元格信息（用于拖拽功能）
  const getCellInfoFromMouseEvent = useCallback((event: MouseEvent, gridApiRef: React.RefObject<GridReadyEvent | null>): {rowIndex: number, colId: string, node: IRowNode<any>, column: Column} | null => {
    if (!gridApiRef.current?.api || !gridApiRef.current?.columnApi) return null;

    const { api, columnApi } = gridApiRef.current;

    // 从事件目标查找最近的单元格元素
    let target = event.target as HTMLElement;
    while (target && !target.classList.contains('ag-cell')) {
      target = target.parentElement as HTMLElement;
      if (!target || target === document.body) return null;
    }

    if (!target) return null;

    // 从单元格元素获取列ID
    const colId = target.getAttribute('col-id');
    if (!colId) return null;

    // 从单元格元素获取行索引
    let rowElement = target;
    while (rowElement && !rowElement.classList.contains('ag-row')) {
      rowElement = rowElement.parentElement as HTMLElement;
      if (!rowElement || rowElement === document.body) return null;
    }

    if (!rowElement) return null;
    
    const rowIndexAttr = rowElement.getAttribute('row-index');
    if (!rowIndexAttr) return null;
    
    const rowIndex = parseInt(rowIndexAttr, 10);
    if (isNaN(rowIndex)) return null;

    // 获取对应的 RowNode 和 Column 对象
    const rowNode = api.getDisplayedRowAtIndex(rowIndex);
    const column = columnApi.getColumn(colId);

    if (!rowNode || !column) return null;

    return {
      rowIndex,
      colId,
      node: rowNode,
      column
    };
  }, []);

  // 处理单元格点击事件
  const handleCellClicked = useCallback((params: any) => {
    // 检查是否有单元格正在编辑
    const editingCells = params.api?.getEditingCells();
    const isEditing = editingCells && editingCells.length > 0;

    // 如果正在编辑模式，完全跳过自定义处理，让 Ag-Grid 处理编辑逻辑
    if (isEditing) {
      return;
    }

    // 非编辑模式下，阻止 Shift + 点击时的浏览器默认文本选择行为
    if (params.event) {
      params.event.preventDefault();
      // 阻止事件冒泡，防止触发其他选择行为
      params.event.stopPropagation();
    }

    if (customSelectionControllerRef.current) {
      customSelectionControllerRef.current.onCellClicked(params);
      updateSelectionContext();
    }
  }, [updateSelectionContext]);

  // 初始化选择控制器
  const initializeSelectionController = useCallback((options: GridReadyEvent, gridApiRef: React.RefObject<GridReadyEvent | null>) => {
    // 初始化自定义选择控制器
    customSelectionControllerRef.current = new CustomSelectionController(
      options.api, 
      options.columnApi, 
      isScrollLoading
    );
    
    // 初始化右键菜单处理器
    contextMenuHandlersRef.current = new ContextMenuHandlers(gridApiRef, customSelectionControllerRef);
    setContextHandlersReady(true);
  }, [isScrollLoading]);

  // 设置拖拽选区功能事件监听器
  const setupDragSelection = useCallback((gridApiRef: React.RefObject<GridReadyEvent | null>) => {
    if (!gridContainerRef.current || !customSelectionControllerRef.current) {
      return;
    }

    const gridContainer = gridContainerRef.current;
    let isDragging = false;

    const handleMouseDown = (event: MouseEvent) => {
      // 只处理左键点击
      if (event.button !== 0) return;

      // 检查是否有单元格正在编辑
      const editingCells = gridApiRef.current?.api?.getEditingCells();
      const isEditing = editingCells && editingCells.length > 0;

      // 如果正在编辑模式，不处理拖拽选择
      if (isEditing) {
        return;
      }

      const gridCell = getCellInfoFromMouseEvent(event, gridApiRef);

      // 检查是否是有效的数据单元格（排除行头列）
      if (!gridCell || gridCell.colId === indexColId) {
        return;
      }

      if (customSelectionControllerRef.current) {
        const dragStarted = customSelectionControllerRef.current.onTableMouseDown(event, gridCell);
        if (dragStarted) {
          isDragging = true;
          // 重置单元格位置缓存
          lastMouseCellRef.current = `${gridCell.rowIndex}_${gridCell.colId}`;
          updateSelectionContext();
          // 触发 cellClassRules 重新评估
          if (gridApiRef.current?.api) {
            gridApiRef.current.api.refreshCells();
          }
        }
      }
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (!isDragging || !customSelectionControllerRef.current) return;
      
      const currentGridCell = getCellInfoFromMouseEvent(event, gridApiRef);
      
      // 智能更新策略：只在移动到新单元格时才更新
      const currentCellKey = currentGridCell ? `${currentGridCell.rowIndex}_${currentGridCell.colId}` : null;
      
      // 缓存上一次的单元格位置，避免重复计算
      if (currentCellKey !== lastMouseCellRef.current) {
        lastMouseCellRef.current = currentCellKey;
        customSelectionControllerRef.current.onTableMouseMove(currentGridCell);
        // 触发 cellClassRules 重新评估
        if (gridApiRef.current?.api) {
          gridApiRef.current.api.refreshCells();
        }
    }
    };

    const handleMouseUp = () => {
      if (!isDragging || !customSelectionControllerRef.current) return;
      customSelectionControllerRef.current.onTableMouseUp();
      isDragging = false;
      // 清理单元格位置缓存
      lastMouseCellRef.current = null;
      updateSelectionContext();
      if (gridApiRef.current?.api) {
        gridApiRef.current.api.refreshCells();
      }
    };

    // 添加事件监听器
    gridContainer.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      // 清理事件监听器
      gridContainer.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [getCellInfoFromMouseEvent, updateSelectionContext, gridContainerRef.current, customSelectionControllerRef.current]);

  // 清理选择控制器
  const cleanupSelectionController = useCallback(() => {
    if (customSelectionControllerRef.current) {
      // 调用销毁方法，彻底清理资源
      if (typeof customSelectionControllerRef.current.destroy === 'function') {
        customSelectionControllerRef.current.destroy();
      } else {
        customSelectionControllerRef.current.clearAllSelections();
      }
      customSelectionControllerRef.current = null;
    }
    if (contextMenuHandlersRef.current) {
      contextMenuHandlersRef.current = null;
    }
    // 清理状态
    setSelectionContext(null);
    setContextHandlersReady(false);
    // 清理缓存引用
    lastMouseCellRef.current = null;
  }, []);

  return {
    // refs
    customSelectionControllerRef,
    gridContainerRef,
    contextMenuHandlersRef,
    
    // state
    selectionContext,
    contextHandlersReady,
    
    // methods
    updateSelectionContext,
    getCellInfoFromMouseEvent,
    handleCellClicked,
    initializeSelectionController,
    setupDragSelection,
    cleanupSelectionController,
  };
};