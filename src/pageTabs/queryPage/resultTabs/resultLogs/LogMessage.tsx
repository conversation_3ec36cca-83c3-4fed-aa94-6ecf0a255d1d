import React, {
  PropsWith<PERSON>hildren,
  useCallback,
  useEffect,
  useRef,
  useState
} from 'react'
import { useHistory } from 'react-router-dom';
import { useEditorInstance } from 'src/components'
import { useDispatch, useSelector } from 'src/hook'
import type { QueryLogItem } from 'src/types'
import { Button, Tooltip, Typography } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import * as _ from 'lodash'
import { openFlowForm } from 'src/pageTabs/flowPages'
import { openResultLogForm } from './resultLogSlice'
import Service from 'src/service'
import classNames from 'classnames'
import styles from './index.module.scss'
import { setErrorJump, setLastErrorPosition } from '../resultTabsSlice'
import { renderErrorFlag } from 'src/util';
import { Iconfont } from 'src/components';
import { useTranslation } from 'react-i18next';
import { setDataChangeMineApplyPageState, setDataChangeMineApplyDetailParams } from 'src/pageTabs/data-change/dataChangeSlice';
import extractSchemaInfo from './util';
export const OberserverContext = React.createContext({
  sizeMap: new Map<number, number>(),
  observer: new ResizeObserver(() => { }),
});

interface LogMessageProps {
  log: QueryLogItem
  lastMessageId: string | undefined
  index: number
  queryKey: string
  [p: string]: any
}

const { Text } = Typography;

export const LogMessage = ({
  log,
  lastMessageId,
  index,
  queryKey,
}: PropsWithChildren<LogMessageProps>) => {
  const [editorInstance] = useEditorInstance()
  const [connectionId, setConnectionId] = useState<string | number>()
  const [connectionType, setConnectionType] = useState<string>()
  const { t } = useTranslation()
  const history = useHistory()
  const {
    timestamp,
    statement,
    error,
    executionWarning,
    duration,
    affectedRows,
    messageId,
    errorFlag,
    ddlautoCommit,
    executeError
  } = log
  const success = errorFlag === 1
  // sql中操作的对象集合
  const permissionEchoResultList = executeError?.permissionEchoResultList

  // 如果日志返回相关信息，则按照返回信息回填表单，否则只回填数据库元素（数据库层级）
  const { paneInfoMap, executeActiveTabParams } = useSelector(
    (state) => state?.queryTabs
  );
  const info = paneInfoMap[queryKey];

  const treeData = useSelector((state) => state.sdt.treeData);

  // 获取连接节点
  const node = treeData.find((t) => t.connectionId === info.connectionId);
  const nodeName = log?.permissionEchoResult?.nodeName;
  const nodePath = log?.permissionEchoResult?.nodePath
    ? `${log.permissionEchoResult.nodePath}`
    : `/root/${connectionId}`;
  const nodePathWithType = log?.permissionEchoResult?.nodePathWithType;
  const nodeType = log?.permissionEchoResult?.nodeType
    ? log.permissionEchoResult.nodeType
    : 'connection';

  const permissionNodes = [
    {
      connectionId,
      connectionType,
      nodeName,
      nodePath,
      nodePathWithType,
      nodeType,
    },
  ] as any;

  // 用于提权的操作节点
  const upperPermissionNodes = permissionEchoResultList?.map((item: any) => {
    const nodeName = item?.nodeName;
    const nodePath = item?.nodePath || `/root/${connectionId}`
    const nodePathWithType = item?.nodePathWithType;
    const nodeType = item?.nodeType || 'connection';
    return {
      connectionId,
      connectionType,
      nodeName,
      nodePath,
      nodePathWithType,
      nodeType,
    }
  })

  const WasteTime = (props: any) => {
    const { time } = props;
    const timeFormat = time === '-' ? time : `${time}ms`;
    return (
      <div className={styles.wasteTimeWrap} title={timeFormat}>
        {t('sdo_time_consuming')} <QuestionCircleOutlined /> : {timeFormat}
      </div>
    )
  };

  useEffect(() => {
    if (node?.connectionId) {
      setConnectionId(node?.connectionId);
    }
    //存储在folder中时候treeData查找不到
    if (info?.connectionId) {
      setConnectionId(info?.connectionId);
    }
  }, [node?.connectionId, queryKey, info?.connectionId]);

  useEffect(() => {
    if (node?.connectionType) {
      setConnectionType(node?.connectionType);
    }
    //存储在folder中时候treeData查找不到
    if (info?.connectionType) {
      setConnectionType(info?.connectionType);
    }
  }, [node?.connectionType, info?.connectionType]);

  const dispatch = useDispatch();

  const permissionButtonRender = useCallback(
    (log: QueryLogItem) => {
      if (!Service.moduleService.isModuleExist('/flow')) {
        return null;
      }
      const {
        permissionLimits,
        statement,
        permissionEchoResult,
        executeError,
        canApply,
        canDataChange
      } = log;
      const {
        doubleCheck,
        operationType = '',
        level,
        nodePathWithType,
        connectionType,
        connectionName,
        connectionId
      } = permissionEchoResult || {};

      const upperOperationList = executeError?.permissionEchoResultList?.[0]?.opertionTypeList || []

      const permissionData = permissionLimits?.[0];
      if (!permissionData) return null;
      const { permissionType } = permissionData;
      const { flowType, name } = Service.permService.getPermData({
        key: permissionType,
      }) as any;
      //level = 4 拦截并提示 不展是申请操作
      if (!flowType || level === 4) return null;

      const renderOTPBtn = () => {
        return doubleCheck === 'OTP' && <Button
          size='small'
          type='link'
          style={{ margin: '0 4px' }}
          onClick={() => {
            if (connectionId && connectionType) {
              const values = executeActiveTabParams[queryKey] || {};
              dispatch(
                openResultLogForm({
                  type: 'applyOTPCheck',
                  fields: {
                    ...values,
                    connectionId,
                    statements: [statement],
                    permissionEchoResult,
                    queryTabKey: queryKey
                  },
                })
              );
            }
          }}
        >
          {t('sdo_command_review')}
        </Button>
      }

      const renderSMSBtn = () => {
        return <Button
          size='small'
          type='link'
          style={{ margin: '0 4px' }}
          onClick={async () => {

            if (connectionId && connectionType) {
              const values = executeActiveTabParams[queryKey] || {};
              dispatch(
                openResultLogForm({
                  type: 'applySMSCheck',
                  fields: {
                    ...values,
                    connectionId,
                    statements: [statement],
                    permissionEchoResult,
                    queryTabKey: queryKey
                  },
                })
              );
            }
          }}
        >
          {t('sdo_sms_review')}
        </Button>
      }

      // 数据变更-我的申请-申请变更add
      const handleToApplyEdit = () => {
        const params = {
          nodePath: nodePathWithType,
          sqlStatement: statement,
          dataSourceType: connectionType,
          connectionName: connectionName,
          connectionId
        }
        history.push('/data_change_mine_apply')
        dispatch(setDataChangeMineApplyPageState('addOrEdit'))
        dispatch(setDataChangeMineApplyDetailParams(params))
      }
    
      // 数据订正按钮
      const renderDataCorrection = () => {
        return <Button
          type='link'
          style={{ margin: '0 4px' }}
          onClick={handleToApplyEdit}
        >
          {t('sdo_request_data_correction')}
        </Button>
      }

      //高危 level = 2 走数据订正
      if (
        permissionEchoResult?.permissionType === 'highRiskOperation' &&
        level === 2
      ) {
        return (
          <>
            {doubleCheck === 'OTP' ? renderOTPBtn() : doubleCheck === 'SMS' ? renderSMSBtn() : ''}
            {renderDataCorrection()}
          </>
        );
      }

      return (
        <>
          {doubleCheck === 'OTP' ? renderOTPBtn() : doubleCheck === 'SMS' ? renderSMSBtn() : ''}
          {
            ['OTP', 'SMS'].includes(doubleCheck ?? '')
            && ((flowType !== 'dataManipulation' || canApply)
              || (flowType === 'dataManipulation' && canDataChange))
            && t('sdo_or_lower')
          }
          {(flowType !== 'dataManipulation' || canApply) && (
            <Button
              onClick={() => {
                if (connectionId && connectionType) {
                  let elements = flowType === 'dataManipulation' ? upperPermissionNodes : permissionNodes;
                  const operationList = flowType === 'dataManipulation' ? upperOperationList : [operationType];

                  // 这四类权限可以到表，其他只能到schema
                  if(!["DELETE_TABLE", "INSERT_TABLE", "UPDATE_TABLE", "SELECT_TABLE"].includes(operationList?.[0])) {
                    const ele = extractSchemaInfo(elements[0])
                    elements = elements.map((e: any) => {
                      return {
                        ...ele,
                        nodeType: 'schema',
                        connectionId: e?.connectionId,
                        connectionType: e?.connectionType,
                        schemaFlag: true
                      }
                    })
                  }
                  dispatch(
                    // 因为申请提权的操作对象和提权类型可能有多个，因此需要单独处理，相关字段为permissionEchoResultList
                    openFlowForm({
                      type: flowType,
                      fields: {
                        elements,
                        operationList,
                      },
                    })
                  );
                }
              }}
              size='small'
              type='link'
              style={{ margin: '0 4px' }}
            >
              {t('sdo_permission_to')} {t(name)}
            </Button>
          )}
          {
            (flowType !== 'dataManipulation' || canApply)
            && (flowType === 'dataManipulation' && canDataChange)
            && t('sdo_or_lower')
          }
          {(flowType === 'dataManipulation' && canDataChange) && renderDataCorrection()}
        </>
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch, connectionId, connectionType, upperPermissionNodes, permissionNodes]
  );

  const contentRef = useRef<HTMLDivElement>(null);

  const handleJump = (log: any) => {
    const { position, errorLineNumber, selection } = log

    // 默认错误位置是接口返回的位置 postion只读属性需要深拷贝处理
    let errorPosition = _.cloneDeep(position);

    // 1. 如有返回具体的错误行数，优先跳转到详细错误位置
    if(![undefined, null].includes(errorLineNumber)){
      try {
        const model = editorInstance?.getModel();
        const startColumn = model?.getLineFirstNonWhitespaceColumn(errorLineNumber)
        const endColumn = model?.getLineLastNonWhitespaceColumn(errorLineNumber);
        errorPosition = {
          startLine: errorLineNumber,
          startCol: startColumn,
          stopLine: errorLineNumber,
          stopCol: endColumn,
        }
      } catch(error) {
        console.log('setErrorPosition-error', error)
      }
    }

    // 2. 使用后端返回的 selection（用户当时的选中区域）来计算绝对位置
    let needAddLineNumber = 0;
    if (selection) {
      // 使用后端返回的选中区域起始行作为偏移量
      needAddLineNumber = selection.startLineNumber;
    }

    if(needAddLineNumber > 0){
      errorPosition.startLine = errorPosition?.startLine + needAddLineNumber - 1;
      errorPosition.stopLine = errorPosition?.stopLine + needAddLineNumber - 1;
    }

    dispatch(setErrorJump(true))
    dispatch(setLastErrorPosition({
      [queryKey]: {
        startLineNumber: errorPosition?.startLine,
        startColumn: errorPosition?.startCol,
        endLineNumber: errorPosition?.stopLine,
        endColumn: errorPosition?.stopCol,
      }
    }))
  }

  const renderPromptContent = () => {
    const childrenCon = permissionButtonRender(log)?.props?.children
    const { permissionEchoResult } = log
    if ((childrenCon && typeof childrenCon === 'string') || (typeof childrenCon === 'object' && childrenCon?.length && childrenCon?.filter((i: any) => i)?.length)) {
      return (
        <span style={{ fontWeight: 'bold' }} className={classNames(styles.primary, styles.btnsColor)}>{t('sdo_access_permission')} [{permissionButtonRender(log)}] {t('sdo_deal_with_lower')}</span>
      )
    }
    if (permissionEchoResult) {
      return (
        <Text className={styles.text} type='danger'>
          {t('sdo_contact_admin')}
        </Text>
      )
    }
    return null
  }

  return (
    <div className={styles.singleLog} ref={contentRef} data-index={index}>
      <div className={styles.logInfo}>
        <Text className={classNames(styles.text, styles.primary)} strong>
          [{log?.logIndex}]
        </Text>
        <Text
          className={classNames(
            styles.text,
            styles.secondary,
            styles.timestamp
          )}
          type='secondary'
        >
          [{dayjs(timestamp).format('MM-DD HH:mm:ss')}]
          {ddlautoCommit && `[${t('sdo_statement_implicit_commit')}]`}
          {/* 自动回滚的执行日志 */}
          {errorFlag === -2 && `[${t('sdo_timeout_rollback')}]`}
        </Text>
        {/* sql 语句 */}
        <Text
          className={classNames(
            styles.text,
            success && styles.primary,
            (errorFlag === -2 && log?.success) && styles.primary,   // 回滚日志特殊处理
            styles.statement
          )}
          ellipsis
          {
          ...(
            (errorFlag === -2 && !log?.success) || (errorFlag !== -2 && !success)
              ? { type: 'danger' }
              : {}
          )
          }
        >
          <Tooltip
            title={statement ?? ''}
            placement='topLeft'
            overlayClassName={styles.customStatementTooltip}
          >
            {statement}
          </Tooltip>
          {
            (messageId === lastMessageId) && (errorFlag === -2 && !log?.success) || (errorFlag !== -2 && !success) && log?.position &&
            <>
              &nbsp;
              <Tooltip title={t('sdo_click_icon_navigate')}>
                <Iconfont type='icon-jump' style={{ fontSize: 14 }} onClick={() => handleJump(log)} />
              </Tooltip>
            </>
          }
        </Text>
        {/* 执行耗时 */}
        <Tooltip title={t('sdo_execution_time_sql')}>
          <Text
            className={classNames(styles.text, styles.primary, styles.duration)}
            ellipsis
          >
            {duration === null || duration === undefined ? (
              <WasteTime time='-' />
            ) : (
              <WasteTime time={duration} />
            )}
          </Text>
        </Tooltip>
        <Text
          className={classNames(styles.text, styles.status)}
          type={success ? 'success' : 'danger'}
        >
          {
            renderErrorFlag(errorFlag)
          }
        </Text>
        <Text
          className={classNames(
            styles.text,
            styles.primary,
            styles.affectedRows
          )}
          ellipsis
        >
          {t('sdo_affected_rows')}：{affectedRows}
        </Text>
      </div>

      {error && (
        <div className={styles.logError}>
          <Text className={classNames(styles.text, styles.secondary)}>
            {t('sdo_exception_info')}：
          </Text>
          <br />
          <Text className={styles.text} type='danger'>
            {error}
          </Text>
          {
            renderPromptContent()
          }
        </div>
      )}
      {executionWarning && (
        <div className={classNames(styles.logError, styles.btnsColor)}>
          <Text className={classNames(styles.text, styles.secondary)}>
            {t('sdo_warn_info')}：
          </Text>
          <br />
          <Text className={styles.text} type='warning'>
            {executionWarning}
          </Text>
          {permissionButtonRender(log)}
        </div>
      )}
    </div>
  );
};
