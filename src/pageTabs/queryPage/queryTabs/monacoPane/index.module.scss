@import 'src/styles/variables';

.queryEditor {
  display: flex;
  flex-direction: column;
  background-color: $white;

  [data-theme='dark'] & {
    background-color: $blue-43;
    color: $white;
  }

  .editorContainer {
    height: 100%;
    border-bottom: 1px solid $border-color;

    [data-theme='dark'] & {
      border: none;
    }
    :global {
      .monaco-editor {
        [data-theme='dark'] & {
          color: #d4d4d4;
          background-color: #43434a;
        }
      }
      /* 强制覆盖默认样式 */
      .monaco-editor .view-overlays .current-line {
        [data-theme='dark'] & {
          border: 2px solid #282828 !important;
        }
        border: 2px solid #eeeeee !important;
      }

      .monaco-editor .view-overlays .selected-text {
        [data-theme='dark'] & {
          background-color: #3a3d41 !important; //#007acc33 !important; /* 深色主题下选中语句背景颜色 */
        }
        background-color: #e5ebf1 !important; //#007acc1a !important; /* 浅色主题下选中语句背景颜色 */
      }

      .monaco-editor .focused .selected-text {
        [data-theme='dark'] & {
          background-color: #264f78 !important; //#007acc33 !important; /* 深色主题下选中语句背景颜色 */
        }
        background-color:  #add6ff !important;
      }
      .monaco-editor .line-numbers {
        [data-theme='dark'] & {
          color: #838383;
        }
      }
      .monaco-editor-background {
        [data-theme='dark'] & {
          background-color: #252526; //#43434a;
        }
      }
      .mtk6 {
        [data-theme='dark'] & {
          color: #569cd6;
        }
      }
      .mtk1 {
        [data-theme='dark'] & {
          color: #d4d4d4;
        }
      }
      .margin {
        [data-theme='dark'] & {
          background-color: #3e3e45; // #43434a;
        }
      }
    }
  }

  :global {
    .editor-exec-error-markline {
      color: red;
      background-color: #e5e5e5;
      [data-theme='dark'] & {
        background-color: $blue-43;
      }
    }

    .editor-highlight {
      background-color: #0000ff20;

      [data-theme='dark'] & {
        background-color: $blue-43;
      }
    }

    .underline-wavy-decorations {
      text-decoration: underline;
      text-decoration-style: wavy;
      text-decoration-color: red;
    } 
    .ant-select-selector {
      [data-theme='dark'] & {
        background-color: $background-color-theme-dark  !important;
        color: $white  !important;
        border: none !important;
      }
    }

    .ant-select-disabled .ant-select-selector {
      [data-theme='dark'] & {
        opacity: 0.55;
      }
    }

    .ant-upload {
      color: inherit;
      cursor: pointer;
    }

    .editorContainerAlert {
      width: 100%;
      margin-top: -1px;
      padding: 4px 20px;
      .ant-alert-message {
        color: rgb(139, 146, 161) !important;
        font-size: 12px;
      }
      .ant-alert-close-text {
        color: #3262ff;
      }
    }
    .lightBg {
      background-color: rgb(248, 248, 248);
      border-color: white;
    }
    .darkBg {
      background-color: #3B3B3E;
      border-color: #3B3B3E;
    }
  }

}

.resizeHandle {
  height: 16px;
  line-height: 16px;
  position: absolute;
  bottom: 0px;
  left: calc(50% - 16px);
  font-size: 16px;
  cursor: row-resize;
  user-select: none; // 防止拖拽时选中文本
  color: rgba(0, 0, 0, 0.75);

  [data-theme='dark'] & {
    color: rgba(255, 255, 255, 0.75);
  }
}

.auditResultContent {
  display: flex;
  justify-content: center;
  flex-direction: column;
  max-height: 456px;
  overflow: hidden;
  .statement {
    max-height: 278px;
    background: #F6F6F7;
    padding: 16px 16px;
    color: #0F244C;
    border-radius: 4px;
    margin-bottom: 16px;
    .executeStatement {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .statementDetail {
      width: 100%;
      max-height: 206px;
      overflow: auto;
      color:#0F244C;
      display: inline-block;
      font-weight: 400;
    }
  }
  .ruleCount {
    margin-bottom: 6px;
  }
  .matchingRules {
    flex: 1;
    overflow-y: auto;
  }
}