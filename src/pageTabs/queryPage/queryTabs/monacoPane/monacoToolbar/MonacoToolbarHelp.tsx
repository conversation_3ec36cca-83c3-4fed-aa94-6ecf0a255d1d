import React, { memo } from 'react'
import {
  Col,
  <PERSON>,
  Tooltip,
  Dropdown,
  Menu,
  Descriptions,
} from 'antd'
import { Iconfont } from 'src/components'
import styles from './index.module.scss'
import { useSelector } from 'src/hook'
import { useTranslation } from 'react-i18next'
import ShortcutKeyManagement from './ShortcutKeyManagement'

export const MonacoToolbarHelp = memo(() => {
  const { Explain, Execute, Hint, Find, multiEdit, Comment } = useSelector(state => state.setting.hotKeys)
  const { t } = useTranslation()
  const format = (key?: string) => {
    if(!key) return
    return key.slice(0,1)[0].toUpperCase() + key.slice(1, -1) + key.slice(-1).toUpperCase()
  }

  const FavoritesMenu = (
    <Menu className={styles.helpMenu} style={{width:'400px'}}>
      <Descriptions title={t('sdo_shortcut_list')}  column={1} bordered size="small">
        <Descriptions.Item label={t('sdo_execut_select_stm')}>{format(Execute)}</Descriptions.Item>
        <Descriptions.Item label={t('sdo_execut_plan_select_stm')}>{format(Explain)}</Descriptions.Item>
        <Descriptions.Item label={t('sdo_tips')}>{format(Hint)}</Descriptions.Item>
        <Descriptions.Item label={t('sdo_quick_replace')}>{format(Find)}</Descriptions.Item>
        <Descriptions.Item label={t('sdo_mul_line_edit')}>{t(multiEdit)}</Descriptions.Item>
        <Descriptions.Item label={t('sdo_comment_or_not_selected_lines')}>{format(Comment)}</Descriptions.Item>
      </Descriptions>
    </Menu>
  )

  return (
    <Col>
      <Space size={1}>
        <Tooltip title={t('sdo_shortcut_list')} placement="bottomLeft" arrowPointAtCenter>
          <Dropdown
            overlay={<ShortcutKeyManagement/>}
            placement="bottomCenter"
            trigger={['click']}
            mouseLeaveDelay={0.3}
          >
            <Iconfont
              className={styles.monacoToolbarIcon}
              type="icon-kuaijiejian2"
            />
          </Dropdown>
        </Tooltip>
      </Space>
    </Col>
  )
})
