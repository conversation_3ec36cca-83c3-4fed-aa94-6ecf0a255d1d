import React from 'react'
import { List, Typography, Tooltip } from 'antd'
import { UIModal } from 'src/components'
import { useTranslation } from 'react-i18next'
import styles from './index.module.scss';

export const ModalShowMoreCommit = React.memo(
  ({
    commitList,
    visible,
    closeModal,
  }: {
    commitList?: string[]
    visible?: boolean
    closeModal: () => void
  }) => {
    const { t } = useTranslation()
    return (
      <UIModal
        visible={visible}
        title={t('sdo_pending_submission_statement')}
        footer={null}
        onCancel={() => closeModal()}
        width={640}
        className={styles.moreCommitModal}
      >
        <List 
          size="small"
          className={styles.commitList}
          dataSource={commitList}
          renderItem={(item) => (
            <List.Item>
              <Tooltip title={item}>
                <Typography.Text ellipsis>{item}</Typography.Text>
              </Tooltip>
            </List.Item>
          )}
          pagination={{ 
            size:'small',
            position:'bottom',
            showQuickJumper: true,
            showSizeChanger: true,
            showLessItems: true,
            total: commitList?.length,
            showTotal: (total: number) => t('common.table.pagination.total',{total: total || 0} ),
           }}
        />
      </UIModal>
    )
  },
)
