import React, {
  Props<PERSON>ith<PERSON>hildren,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback
} from 'react'
import {
  Form,
  Input,
  message,
  Select,
  Tooltip,
  Tree
} from 'antd'
import { EditorContext, Iconfont, UIModal } from 'src/components'
import styles from './index.module.scss'
import { saveDocument, handleSearchDirName } from 'src/api'
import { Encoding, ENCODINGS, FormLayout } from 'src/constants'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { setMonacoServerFile, updateMonacoTabName } from '../../queryTabsSlice'
import { fileNameValidator, getParentKeysAboutContainSearchValue, filterNodesNotMatch } from 'src/util'
import classNames from 'classnames'
import { keyStringSplitWithMultiKey } from 'src/util/hotKeys'
import { useTranslation } from 'react-i18next'
import { getIsShareItemExtra } from '../common';

const formatTreeValue = (files: any) => {

  const newArr = files
  ?.filter((item: any, index: number) => {
    if (item.isDir) {
      item.key = item?.path;
      item.title = item?.name;
      if (item?.isDir) { 
        item.children = formatTreeValue(item?.children)
        return item
      } else {
        item.children = []
      }
      return item
    }
  })
  return newArr
}

const filterDirNodes = (data: any) => {
  return data
    ?.filter((node: any) => node.isDir !== false) // 过滤掉 isDir=false 的节点
    ?.map((node: any) => {
      if (node.children) {
        node.children = filterDirNodes(node.children); // 递归处理子节点
      }
      return node;
    });
}

export const SaveDocument = ({
  encoding,
}: PropsWithChildren<{ encoding?: Encoding }>) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const [saveDocForm] = Form.useForm()
  const [saveLocalForm] = Form.useForm()
 
 //保存文档快捷键
  const { SaveDocument = 'Ctrl+Shift+S' } = useSelector((state) => state.setting.hotKeys)

  //本地保存
  const localNode = [{title: t('sdo_save_document_to_local'),  key: 'base'}];

  const [visibleSaveDoc, setVisibleSaveDoc] = useState(false)
  const [visibleSaveLocal, setVisibleSaveLocal] = useState<boolean>(false) //保存到本地
  const { editorInstance } = useContext(EditorContext)
  const [searchDirName, setSearchDirName] = useState<string>('')

  const { paneInfoMap, activeTabKey } = useSelector((state) => state.queryTabs)
  const { serverFile } = paneInfoMap[activeTabKey] || {}

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['/'])
  const [selectedNodes, setSelectedNodes] = useState<any[]>()
  const [treeData, setTreeData] = useState<any[]>([])
  const [autoExpandParent, setAutoExpandParent] = useState(false)

  //搜索文件夹名称直接所有匹配数据 不需要loadData
  const {run: runHandleSearchDirName } = useRequest(handleSearchDirName, {
    manual: true,
    throttleInterval: 300,
    onSuccess: (data) =>{
      //过滤系统空间及非文件夹数据 仅保留文件夹数据
      const afterFilteredUserFiles = data?.filter( (userFile) => !userFile?.isSystemSpaceDir);
  
      let filteredDirNodes = filterDirNodes(afterFilteredUserFiles);
      //前端过滤筛选字段 后端返回有文件名
      if (searchDirName) {
        filteredDirNodes = filterNodesNotMatch(afterFilteredUserFiles, searchDirName)
      }
    
      setTreeData(formatTreeValue(filteredDirNodes))
    }
  })

  useEffect(() => {
    if (!visibleSaveDoc) return
    runHandleSearchDirName(searchDirName);
  }, [visibleSaveDoc, searchDirName])

  useEffect(() => {
    if (searchDirName) {
      let keys = getParentKeysAboutContainSearchValue(treeData, searchDirName)
      setExpandedKeys(keys)
      setAutoExpandParent(true)
    }else {
      setExpandedKeys([]);
      setAutoExpandParent(false)
    }
  },[treeData, searchDirName])

  // 重置表单 encoding
  useEffect(() => {
    if (!visibleSaveDoc) return
    saveDocForm.setFields([{ name: 'encode', value: encoding || 'UTF-8' }])
  }, [visibleSaveDoc, encoding, saveDocForm, visibleSaveLocal])

  useEffect(() => {
    saveLocalForm.setFields([{ name: 'encode', value: encoding || 'UTF-8' }])
  }, [encoding, saveLocalForm, visibleSaveLocal])

  const focusRef = useRef<Input>(null)
  useEffect(() => {
    if (!visibleSaveDoc) return
    focusRef.current?.select()
  }, [visibleSaveDoc])

  useEffect(() => {
    return () => {
      setSearchDirName('');
      setSelectedNodes([]);
    }
  },[])
  useEffect(() => {
    if (!editorInstance) return
   
    const keyCode =  keyStringSplitWithMultiKey(SaveDocument as string) || undefined
    if (!keyCode) return

    const disposable = editorInstance.addCommand(keyCode[0], (e) => {
      setVisibleSaveDoc(true)
    })

    return () => {
      // disposable?.dispose()
    }
  }, [editorInstance, SaveDocument])


  // 保存到本地
  const handleSaveLocal = (values: any) => {
    const name = values.fileName
    const content = String(editorInstance?.getValue())
    const blob = new Blob([content], { type: `text/plain;charset=${values.encode}` });
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = URL.createObjectURL(blob);
    a.download = `${name}`; //另存为默认带了后缀
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(a.href);
    setVisibleSaveLocal(false);
    setVisibleSaveDoc(false)
    message.success(t('sdo_save_suc'))
  }
  //保存到个人文件夹
  const handleSave = () => {
    if (!selectedNodes) return message.error(t('sdo_save_document_error_tip'))
    const selectedNode = selectedNodes?.[0];

    saveDocForm.validateFields().then((values) => {

     if (selectedNode?.key === 'base') {
      handleSaveLocal(values);
      return ;
     }else {
      //个人文件夹
      const name = values.fileName
      const path = (String(selectedNode.key) + '/' + name).replace('//', '/')
      const content = String(editorInstance?.getValue())
      saveDocument(content, path, values.encode).then(() => {
        dispatch(
          setMonacoServerFile({ isDir: false, name, path, type: 'file' }),
        )
        dispatch(updateMonacoTabName(name))
      })
     }
      setVisibleSaveDoc(false)
      message.success(t('sdo_save_suc'))
    })
  }

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    //后端搜索
    setSearchDirName(value);
  };


  const selectedNodePath = useMemo(() => {

    if (!selectedNodes?.[0]) return ''
    if (selectedNodes[0]?.key === localNode[0].key) return localNode[0].title;

   let splitedPatrts = (selectedNodes?.[0]?.key as string)?.split('/');
   
   const dirItem = treeData.find((item) => item?.key === `/${splitedPatrts[1]}`);
   splitedPatrts[1] = dirItem?.title
   
   return splitedPatrts?.join('/');

  }, [selectedNodes?.[0]?.key,])

  return (
    <span
      className={classNames(
        styles.saveDocument,
        styles.monacoToolbarIcon,
        styles.multiIcon,
      )}
    >
      <Tooltip title={t('sdo_save_doc', {val: SaveDocument})} placement="bottomLeft" arrowPointAtCenter>
        <Iconfont
          className={styles.multiIconMain}
          type="icon-file-save"
          onClick={() => {
         
            if (!serverFile) {
              setVisibleSaveDoc(true)
              return
            }
            saveDocument(
              String(editorInstance?.getValue()),
              String(serverFile.path),
              encoding,
            ).then(() => {
              message.success(t('sdo_save_suc'))
            })
          }}
        />
      </Tooltip>
      <UIModal
        title={t('sdo_save_local') }
        visible={visibleSaveDoc}
        onOk={handleSave}
        okText={t('sdo_save')}
        onCancel={() => setVisibleSaveDoc(false)}
        afterClose={() => {
          saveDocForm.resetFields()
          setExpandedKeys([])
        }}
        width={520}
        bodyStyle={{ padding: '16px 24px 0',height: 420 }}
      >
        {
          selectedNodes?.[0] &&
          <div className='mb10'>{t('sdo_save_document_path')}{selectedNodePath}</div>
        }
        <Input.Search 
          className='mb10' 
          placeholder={t('sdo_save_document_search_plac')}
          onChange={onChange}
        />
        <div style={{ height: '20vh', overflow: 'auto' }}>
          <Tree.DirectoryTree
            autoExpandParent={autoExpandParent}
            className={styles.overrideTreeNode}
            treeData={searchDirName ? treeData : localNode.concat(treeData as any) as any}
            expandedKeys={expandedKeys}
            onExpand={(expandedKeys) => setExpandedKeys(expandedKeys)}
            expandAction="doubleClick"
            onSelect={(_selectedKeys, { selectedNodes }) =>
              setSelectedNodes(selectedNodes as any[])
            }
            selectedKeys={selectedNodes?.map((selectedNode) => selectedNode?.key)}
            titleRender={(node) => {
              return (
                <div
                  style={{ display: 'inline', width: '100%' }}
                >
                  {node.title}
                </div>
              )
            }}
          />
        </div>
        <div style={{ margin: 24 }}>
        {
          (selectedNodes?.[0]?.isShareSpaceDir || selectedNodes?.[0]?.isShareSpaceDirChild) &&
            getIsShareItemExtra(true)
        }
        {
            (selectedNodes?.[0]?.isHomeUserDir || selectedNodes?.[0]?.isHomeUserDirChild) &&
            getIsShareItemExtra(false)
        }
        </div>
        <Form
          form={saveDocForm}
          {...FormLayout}
          size="small"
          initialValues={{ fileName: `${t('sdo_untitled_doc')}.sql`, encode: encoding }}
        >
          <Form.Item
            label={t('sdo_store_as')}
            name="fileName"
            rules={[
              { required: true, message: t('sdo_file_rule_req_txt') },
              { validator: fileNameValidator },
              { max: 25, message: t('sdo_file_len_rule_txt') },
            ]}
          >
            <Input ref={focusRef}></Input>
          </Form.Item>
          <Form.Item
            label={t('sdo_encoding')}
            name="encode"
            rules={[{ required: true, message: t('sdo_encoding_rule_req_txt') }]}
          >
            <Select
              showSearch
              options={ENCODINGS.map((encoding) => ({
                label: encoding,
                value: encoding,
              }))}
            ></Select>
          </Form.Item>
        </Form>
      </UIModal>
    </span>
  )
}
