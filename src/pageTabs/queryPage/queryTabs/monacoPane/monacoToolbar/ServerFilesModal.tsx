import { message, Tree, Input } from 'antd'
import { DataNode } from 'antd/lib/tree'
import React, { useEffect, useState, useContext } from 'react'
import { getFileContent, handleSearchDirName, UserFile } from 'src/api'
import { UIModal, EditorContext } from 'src/components'
import { useEditorInstance,  } from 'src/components/BaseEditor/useEditorInstance'
import { useDispatch, useSelector, useRequest } from 'src/hook'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { setFilePath } from 'src/store/extraSlice/textImportSlice'
import {
  setMonacoServerFile,
  updateMonacoTabEncoding,
  updateMonacoTabName,
} from '../../queryTabsSlice'
import { useTranslation } from 'react-i18next'
import { activePaneInfoSelector } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice';
import { getParentKeysAboutContainSearchValue } from 'src/util'
import styles from './index.module.scss';
export interface FileDataNode extends DataNode {
  type?: string
  key: string
}
export const updateTreeData = (
  list: DataNode[],
  key: React.Key,
  children: DataNode[],
): DataNode[] => {
  if (!key) return children;
  return list.map((node) => {
  
    if (node.key === key) {
      return {
        ...node,
        isLeaf: !children?.length,
        ...(children?.length ?  {children} : {})
        ,
      }
    } else if (node?.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children)
      }
    }
    return node
  })
}

const formatTreeValue = (files: UserFile[] ) => {

  const newArr = files?.filter((item: any, index: number) => {
    const { path, name, type } = item;
      item.key = path
      item.title = name
      item.selectable = type !== 'directory';
      item.isLeaf =  type !== 'directory';
      if (item?.children) {
        item.children = formatTreeValue(item?.children)
        return item
      } else {
        item.children = []
      }
      return item
    
  })
  return newArr
}

const ServerFilesModal = ({
  ModalOpenServerFile,
}:{
  ModalOpenServerFile?:boolean;
}) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()

  const { fileInputRef } = useContext(EditorContext)
  const [editorInstance] = useEditorInstance()

  //本地文档
  const localNode = [{title: t('sdo_open_document_to_local'), key: 'base'}];
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(false)
  const [searchDirName, setSearchDirName] = useState<string>('')
  const [treeData, setTreeData] = useState<any[]>([])

  const activePaneInfo = useSelector(activePaneInfoSelector);
  const encode = activePaneInfo?.encoding || 'UTF-8'
  const ModalTextImportWizardVisible = useSelector((state) => state.modal.ModalTextImportWizard?.visible)
  const { isShowLocalFile  = false } = useSelector((state) => state.modal.ModalOpenServerFile?.extraProps) || {}
  const { fileType } = useSelector(
    (state) => state.textImport,
  )

  //搜索文件夹名称直接所有匹配数据 不需要loadData
  const {run: runHandleSearchDirName } = useRequest(handleSearchDirName, {
    manual: true,
    throttleInterval: 300,
    onSuccess: (data = []) =>{

      setTreeData(formatTreeValue(data))
      
    }
  })

  useEffect(() => {
    if (!ModalOpenServerFile) return
    runHandleSearchDirName(searchDirName);
  }, [ModalOpenServerFile, searchDirName])

  useEffect(() => {
    if (searchDirName) {
      let keys = getParentKeysAboutContainSearchValue(treeData, searchDirName)
      setExpandedKeys(keys);
      setAutoExpandParent(true);
    }else {
      setExpandedKeys([]);
      setAutoExpandParent(false);
    }
  },[treeData, searchDirName])

  useEffect(() => {

    return () => {
      setSelectedNodes([]);
      setSearchDirName('');
    }
  },[])

  // 去掉默认选中
  const setPosition = (model: any) => {
    const lastLine = model.getLineCount()
    const lastColumn = model.getLineMaxColumn(lastLine)
    editorInstance?.setPosition({ lineNumber: lastLine, column: lastColumn })
  }

  const tryOpenFile = async (userFile: any) => {
    const { path, name } = userFile
    const model = editorInstance?.getModel()
    if (!model) return
    return getFileContent(path, encode).then(({ content, encoding }) => {
      dispatch(setMonacoServerFile(userFile))
      dispatch(updateMonacoTabName(name))
      dispatch(updateMonacoTabEncoding({ encoding }))
      model.pushEditOperations(
        null,
        [{ range: model.getFullModelRange(), text: content }],
        () => null,
      )
      editorInstance?.focus()
      setPosition(model)
    })
  }

  const handleOpenFile =(file?: FileDataNode) => {
    file = file || selectedNodes[0];
    //如果选择本地文件
    if(file?.key === 'base') {
      dispatch(hideModal('ModalOpenServerFile'))
      setTimeout(() => {
        fileInputRef?.current?.click();
      },200)
      
      return 
    }
    if (!file) return message.error(t('sdo_select_file'))
    if (file?.type === 'directory') return
    if(ModalTextImportWizardVisible) {
      const title = file.title?.toString()!
      const suffix = title?.split('.')?.pop()!;

      // 校验是否为选择的文件类型
      if(fileType && suffix !== fileType){
        return message.error(`${t('sdo_select')} ${fileType} ${t('sdo_type')} ${t('sdo_file')}`)
      }

      const isTr = ['txt', 'csv', 'xls', 'xlsx', 'json'].includes(suffix)
      if(!isTr){
        return message.error(`${t('sdo_only_supports')} .txt/.csv/.xls/.xlsx/.json ${t('sdo_type')} ${t('sdo_file')}`)
      }
      dispatch(setFilePath(file.key as string))
    } else {
      tryOpenFile(file)
    }
    // todo: 加入 loading 交互
    dispatch(hideModal('ModalOpenServerFile'))
  }

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])
  const [selectedNodes, setSelectedNodes] = useState<FileDataNode[]>([])

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    //后端搜索
    setSearchDirName(value);
  }

  return (
    <UIModal
      title={t('sdo_open_file')}
      visible={ModalOpenServerFile}
      onCancel={() => dispatch(hideModal('ModalOpenServerFile'))}
      onOk={() => handleOpenFile()}
      width={520}
      bodyStyle={{ height: '50vh', overflow: 'hidden' }}
      afterClose={() => {
        setExpandedKeys([])
        setTreeData([])
        setSelectedNodes([])
      }}
      okText={t('sdo_open_upper')}
      className={styles.serverFiledsModal}
    >
      {
        isShowLocalFile &&
        <Input.Search className='mb10'   placeholder={t('sdo_open_document_search_plac')} onChange={onChange} />
      }
      <Tree.DirectoryTree
        autoExpandParent={autoExpandParent}
        treeData={searchDirName ? treeData : localNode.concat(treeData as any) as any}
        expandedKeys={expandedKeys}
        onExpand={(expandedKeys) => setExpandedKeys(expandedKeys)}
        expandAction="doubleClick"
        onSelect={(_selectedKeys, { selectedNodes }) =>
          setSelectedNodes(selectedNodes as FileDataNode[])
        }
        titleRender={(node) => {
          return (
            <span
              onDoubleClick={() => {
                handleOpenFile(node as FileDataNode)
              }}
            >
              {node.title}
            </span>
          )
        }}
        className={styles.tree}
      />
    </UIModal>
  )
}
export default ServerFilesModal
