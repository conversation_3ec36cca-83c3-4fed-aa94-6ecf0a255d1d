@import 'src/styles/variables';

.monacoToolbarWrapper {
  min-height: 48px;
  padding: 12px 16px 0;
  border-bottom: 1px solid $border-color;
  [data-theme='dark'] & {
    border-bottom: 1px solid $blue-26;
  }
}

.ml5 {
  margin-left: 5px;
}

.monacoToolbarIcon {
  font-size: 16px;
  line-height: 16px;
  display: flex;
  padding: 4px;
  color: rgba(0, 0, 0, 0.7);
  border-radius: 2px;
  cursor: pointer;
  [data-theme='dark'] & {
    color: rgba(255, 255, 255, 0.7);
  }
  &.iconExecute {
    color: #499c54;
  }
  &.iconCancel {
    color: #ff4d4f;
  }

  &:hover {
    background-color: rgba(0, 0, 0, 0.125);
    [data-theme='dark'] & {
      background-color: rgba(255, 255, 255, 0.125);
    }
  }

  &.iconDisabled {
    pointer-events: none;
    color: rgba(0, 0, 0, 0.3);
    [data-theme='dark'] & {
      color: rgba(255, 255, 255, 0.3);
    }
  }
  &.runIconDisbaled {
    color: rgba(0, 0, 0, 0.3);
    [data-theme='dark'] & {
      color: rgba(255, 255, 255, 0.3);
    }
  }
  &.iconDisabledWithEvent {
  
    color: rgba(0, 0, 0, 0.3);
    [data-theme='dark'] & {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  &.multiIcon {
    padding: 0;

    .multiIconMain,
    .multiIconSub {
      padding: 4px;
      &:hover {
        background-color: rgba(0, 0, 0, 0.25);
        [data-theme='dark'] & {
          background-color: rgba(255, 255, 255, 0.15);
        }
      }
    }
    .multiIconSub {
      padding: 4px 2px;
      display: inline-flex;
      align-items: center;
      font-size: 10px;
    }
  }

  &.iconActive {
    background-color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
    // pointer-events: none;
    [data-theme='dark'] & {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }
}

.commitAndRollbackGroup {
  display: flex;
}
.commitDisabled {
  pointer-events: none;
}

.txDropdownTrigger {
  align-items: center;
}

.divider {
  background-color: #bbb;
  margin: 0 12px;
  [data-theme='dark'] & {
    background-color: #999;
  }
}

.recentlyOpenedFiles,
.saveDocument {
  display: flex;
}
.recentFilesMenu {
  max-width: 540px;
}
.recentFile {
  overflow: hidden;
  text-overflow: ellipsis;
}

// !: override TreeNode ellipsis
.overrideTreeNode {
  :global .ant-tree-node-content-wrapper {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
  }
}

// 收藏下拉选择
.favoriteMenu {
  width: 504px;
  .favoriteMenuItem {
    display: flex;
    justify-content: space-between;
    .itemContent {
      width: 95%;
      display: flex;
      align-items: center;
      .itemSql {
        display: inline-block;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #434C5B;
      }
      .labeled {
        margin-left: 6px;
       }
      .itemTag {
        color: #434C5B;
      } 
    }
    .closeIcon {
      margin: auto 4px;
      color: rgba(0, 0, 0, 0.25);
      &:hover {
        color: rgba(0, 0, 0, 0.55);
      }
    }
  }
  :global {
    // ? 不这样处理，suffix 搜索按钮左侧一小块区域如果被点击，Dropdown 会意外收起
    .ant-input-suffix {
      margin-left: 0;
    }
    .ant-input-search-icon {
      margin-left: 0;
    }
  }
}

.sqlSharedIcon {
  border-radius: 2px;
  background: #E6F8FF;
  font-size: 10px;
  color: #00B7FF;
  font-family: "PingFang SC";
  margin-right: 8px;
  margin-left: 6px;
  padding: 1px 2px;
  display: flex;
  align-items: center;
  word-break: keep-all;
  .icon {
    font-size: 14px;
  }
}

.ml0 {
  margin-left: 0px;
}
.sqlManagementHeader {
  margin: 16px 0;
  .sqlTypeSelect {
    width: 150px;
  }
  // 收藏更多表格
  .searchInput {
    width: 260px;
  
    float: right;
  }
}

.helpMenu {
  width: 400px;
  padding:5px;
  :global {
    .ant-descriptions-header{
      margin-bottom:0;
      text-indent: 20px;
    }
  }
}
.favoriteAliasCellRender {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .favoritesTableTag {
    margin: -6px 6px -6px 0;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .editAliasIcon {
    margin-left: 6px;
  }
}
//快捷键
.shortcutKeyManagement {
  width: 681px;
  padding: 12px 24px 30px 24px;
  border-radius: 4px;
  background: #FFF;
  box-shadow: 0px 0px 10px 0px rgba(193, 198, 204, 0.10);

  .alertMessage {
    color: #434C5B;
  }
  .hidden-cursor {
    caret-color: transparent; /* 隐藏光标 */
  
    outline: none; /* 隐藏轮廓 */
  }
  
}

.shortcutRow {
 >td:first-child {
  background-color: #FAFAFA;
 }
}

.serverFiledsModal {
  .tree {
    height: calc(100% - 40px);
    overflow-y: auto;
  }
}

.pendingStatement {
  max-width: 480px;
}

.moreCommitModal {
  .commitList {
    :global {
      .ant-list-items {
        max-height: 450px;
        overflow-y: auto;
        &::-webkit-scrollbar-thumb {
          border-radius: 4px;
          background-color: #ccc !important;
          &:hover {
            background-color: #666 !important;
          }
        }
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
          border-radius: 4px;
        }
      }
    }
  }
}