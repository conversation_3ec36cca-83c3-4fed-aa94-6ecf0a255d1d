import React, { useContext, useEffect, useRef, useState } from 'react'
import { Input, message, Table , Select, Tooltip } from 'antd'
import { EditorContext, LinkButton, UIModal, EllipsisWithTooltip } from 'src/components'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { deleteFavorite, searchFavorites, updateFavoriteStatus } from 'src/api'
import { ColumnsType } from 'antd/lib/table'
import { Range } from 'monaco-editor'
import debounce from 'lodash/debounce'
import styles from './index.module.scss'

import { SQLSharedIdentifier } from './MonacoToolbarFavorite'
import { useTranslation } from 'react-i18next'
import { SQL_FAVORITE_TYPE, sqlFavoriteType, sqlFavoritesLabelMap } from './constants';

interface ISearchParams {
  scope: sqlFavoriteType;
  keyword?: string;
}
export const FavoriteManagementModal = () => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { ModalFavoriteManagement } = useSelector((state) => state.modal)
  const visible = ModalFavoriteManagement?.visible || false
  const { handleEditFavoriteItem, needRefreshManagement } = ModalFavoriteManagement?.extraProps || {}

  const sqlFavoriteTypeOptions = SQL_FAVORITE_TYPE.map((type: sqlFavoriteType) => ({ label: sqlFavoritesLabelMap[type], value: type}));
  
  const defaultSearchParams: ISearchParams = {
    scope: 'ALL'
  }

  const [searchParams, setSearchParams] = useState<ISearchParams>(defaultSearchParams);

  const {
    run: tryGetFavorites,
    data: favorites = [],
    refresh: reGetFavorites,
    loading: loadingGetFavorites,
    mutate: mutateGetFavorites,
  } = useRequest(searchFavorites, { manual: true, debounceInterval: 350 })

  useEffect(() => {
    if (!visible) return
    mutateGetFavorites([])
    tryGetFavorites(searchParams)
    focusRef.current?.focus()
  }, [visible, tryGetFavorites, JSON.stringify(searchParams), mutateGetFavorites, needRefreshManagement])

  const { run: tryDeleteFavorite } = useRequest(deleteFavorite, {
    manual: true,
    onSuccess: () => {
      message.success(t('sdo_favorite_del_suc'))
      reGetFavorites()
    },
  })

  const { run: silentlyUpdateFavoriteStatus } = useRequest(
    updateFavoriteStatus,
    { manual: true },
  )

  const { editorInstance } = useContext(EditorContext)

  const columnsWithAction: ColumnsType<any> = [
    { 
      dataIndex: 'statement', 
      title: t('sdo_statement'), 
      render: (val, record) => {
        return (
          <div className='flexAlignCenter'>
            {record?.canShare && <SQLSharedIdentifier className={styles.ml0}/>}
            <EllipsisWithTooltip text={val} width={300}/>
          </div>
        )
      }
     },
    {
      dataIndex: 'statementName',
      title: t('sdo_alias'),
      render: (val: string) => (
        <Tooltip title={val}>
           {`【${val?.replace(/^(.{10}).+$/, (match: any, p1: string) => p1 + '...')}】`}
        </Tooltip>
      ) ,
    },
    {
      dataIndex: 'userIdAndName',
      title: t('sdo_creator'),
    },
    {
      dataIndex: 'updateTime',
      title: t('sdo_update_time'),
      render: (updateTime) => (updateTime || '').slice(5),
      width: '136px',
    },
    { dataIndex: 'selectedNum', title: t('sdo_usage_count'), width: '96px' },
    {
      dataIndex: t('sdo_operation'),
      fixed: 'right',
      render: (_, record) => {
        const { statement, id } = record
        return (
          <>
           <LinkButton
              disabled={!record?.canAlter}
              onClick={() => handleEditFavoriteItem?.(record)}
            >
              {t('common.btn.edit')}
            </LinkButton>
            <LinkButton
              onClick={() => {
                silentlyUpdateFavoriteStatus(id)
                dispatch(hideModal('ModalFavoriteManagement'))
                // todo: 提出公共方法，获取 editor content
                // todo: 提取公共方法，处理「使用收藏」
                var position = editorInstance?.getPosition()
                if (!position) return
                // 插入到光标下一行，聚焦编辑器，移动光标到插入内容前
                const { lineNumber } = position
                editorInstance?.executeEdits('', [
                  {
                    range: new Range(lineNumber + 1, 1, lineNumber + 1, 1),
                    text: statement + '\n',
                    forceMoveMarkers: true,
                  },
                ])
                editorInstance?.focus()
                editorInstance?.setPosition({
                  lineNumber: lineNumber + 1,
                  column: 0,
                })
              }}
            >
              {t('sdo_use')}
            </LinkButton>
            <LinkButton
              danger
              disabled={!record?.canDelete}
              onClick={debounce(() => tryDeleteFavorite(id), 500)}
            >
              {t('sdo_del')}
            </LinkButton>
          </>
        )
      },
      width: '160px',
    },
  ]

  const focusRef = useRef<Input>(null)

  return (
    <UIModal
      title={t('sdo_favorite_management')}
      visible={visible}
      onCancel={() => dispatch(hideModal('ModalFavoriteManagement'))}
      width={'80vw'}
      okButtonProps={{ style: { display: 'none' } }}
      cancelText={t('sdo_return')}
      zIndex={400}
      bodyStyle={{ height: 576, padding: '0 16px'  }}
    >
      <div className={styles.sqlManagementHeader}>
        <label>{t('sdo_fav_search_type_label')}</label>
        <Select 
          className={styles.sqlTypeSelect} 
          options={sqlFavoriteTypeOptions} 
          defaultValue={'ALL'}
          onChange={(val) => setSearchParams({...searchParams, scope: val as sqlFavoriteType})}
        />
        <Input.Search
          className={styles.searchInput}
          placeholder={t('sdo_favorite_management_search')}
          size="small"
          onSearch={(keyword) => setSearchParams({ ...searchParams, keyword })}
          onChange={(e) => {
            const keyword = e.target.value || ''
            setSearchParams({ ...searchParams, keyword })
          }}
          ref={focusRef}
          allowClear
        />
      </div>
      
      <Table
        dataSource={favorites}
        columns={columnsWithAction}
        size="small"
        loading={loadingGetFavorites}
        rowKey="id"
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          total: favorites.length,
          showTotal: (total) => t('common.table.pagination.total', {total}),
        }}
        scroll={{ x: 'max-content', y: 420 }}
      />
    </UIModal>
  )
}

