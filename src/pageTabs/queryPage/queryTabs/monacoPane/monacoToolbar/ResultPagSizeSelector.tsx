import React, { memo, useMemo } from "react";
import { Select } from "antd";
import { useDispatch, useSelector } from 'src/hook'
import {
  updateTabsInfo
} from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { useTranslation } from "react-i18next";

const ResultPagSizeSelector = memo(({
  resultPageSize,
  activeTabKey,
  settingPageSize
}: {
  resultPageSize: number;
  activeTabKey: string;
  settingPageSize: number;
}) => {

  let DEFAULT_PAGESIZE_OPTIONS = [100, 200, 500, 1000];

  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { tabInfoMap } = useSelector((state) => state.queryTabs);

  const pageSizeOptions: number[] = useMemo(() => {
    // 根据 settingPageSize 过滤预设选项，保留不超过 settingPageSize 的选项
    if (settingPageSize) {
      const filteredOptions = DEFAULT_PAGESIZE_OPTIONS.filter(option => option <= settingPageSize)
      // 如果 settingPageSize 小于 1000 且不在预设选项中，添加为选项
      if (settingPageSize < 1000 && !DEFAULT_PAGESIZE_OPTIONS.includes(settingPageSize)) {
        return [...filteredOptions, settingPageSize].sort((a, b) => a - b)
      }
      return filteredOptions
    }
    return DEFAULT_PAGESIZE_OPTIONS
  }, [settingPageSize, DEFAULT_PAGESIZE_OPTIONS])

  return (
    <div>
      {t('sdo_res_page_nums')}：
      <Select
        size="small"
        value={resultPageSize}
        options={pageSizeOptions?.map(n => ({ label: n, value: n }))}
        onChange={(val: number) => {
          dispatch(updateTabsInfo({ ...tabInfoMap[activeTabKey], resultPageSize: val }))
        }}
      />
    </div>
  )
})

export default ResultPagSizeSelector;