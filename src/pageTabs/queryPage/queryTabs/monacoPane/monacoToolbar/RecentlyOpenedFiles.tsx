import React, { PropsWithChildren, useContext, useEffect } from 'react'
import { Dropdown, Menu, message, Space, Tooltip } from 'antd'
import styles from './index.module.scss'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { EditorContext, Iconfont } from 'src/components'
import {
  setMonacoServerFile,
  updateMonaco,
  updateMonacoTabEncoding,
  updateMonacoTabName,
  pushMonacoValue,
} from '../../queryTabsSlice'
import { getFileContent, getRecentFiles, getUserSysFile } from 'src/api'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { Encoding } from 'src/constants'
// import { CaretDownOutlined } from '@ant-design/icons'
import { sliceReadFile } from 'src/util'
import { useTranslation } from 'react-i18next'
import { keyStringSplitWithMultiKey } from 'src/util/hotKeys'
import { activePaneInfoSelector } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice';

export const RecentlyOpenedFiles = ({
  encoding,
}: PropsWithChildren<{ encoding?: Encoding }>) => {
  const { activeTabKey } = useSelector((state) => state.queryTabs)
  const { editorInstance, fileInputRef } = useContext(EditorContext)
  const dispatch = useDispatch()
  const { t } = useTranslation()

  const activePaneInfo = useSelector(activePaneInfoSelector);
  const encode = activePaneInfo?.encoding || 'UTF-8'
   //'Ctrl+Alt+S' 冲突
  const { OpenDocument = 'Ctrl+Alt+S' } = useSelector((state) => state.setting.hotKeys)

  const { data: recentFiles = [], refresh } = useRequest(getRecentFiles, {
    formatResult: (recentFiles) => recentFiles.slice(0, 11),
  })

  useEffect(() => {
   
    if (!editorInstance) return
   
    const keyCode =  keyStringSplitWithMultiKey(OpenDocument as string) || undefined
    if (!keyCode) return

    const disposable = editorInstance.addCommand(keyCode[0], (e) => {
      dispatch(showModal('ModalOpenServerFile', { isShowLocalFile: true }))
     
    })

    return () => {
      // disposable?.dispose()
    }
  }, [editorInstance, OpenDocument])

  // 去掉默认选中
  const setPosition = (model: any) => {
    const lastLine = model.getLineCount()
    const lastColumn = model.getLineMaxColumn(lastLine)
    editorInstance?.setPosition({ lineNumber: lastLine, column: lastColumn })
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return
    return new Promise(async(resolve, reject) => {
      try {
        const userUpload = await getUserSysFile()
        // 后端让前端加默认值100M,理由是网络波动有可能拿不到设置的最大文件大小
        const uploadSize = userUpload?.uploadSize || 100
        const fileMbSize = file.size / 1024 / 1024
        const isLessThanSysFileMaxSetting =
        uploadSize && fileMbSize < uploadSize
        if (!isLessThanSysFileMaxSetting) {
          message.warning(`${t('sdo_file_size_limit')} ${uploadSize}M`)
          return false
        }
        dispatch(updateMonaco({ key: activeTabKey, value: '' }))
  
        sliceReadFile(file, async (chunk: Blob, i: any) => {
          return new Promise((resolve, reject) => {
            const fileReader = new FileReader()
            fileReader.onload = function (this: any) {
              const text = this?.result
              dispatch(
                pushMonacoValue({
                  key: activeTabKey,
                  tailText: text || '',
                }),
              )
              resolve(true)
            }
            fileReader.onerror = () => reject(false)
            fileReader.readAsText(chunk, encoding)
          })
        })
        reject()
      } catch (error) {
        console.error(error)
        reject()
      }
    })
  }

  return (
    <Space className={styles.recentlyOpenedFiles} size={1}>
      <Dropdown
        overlay={
          <Menu className={styles.recentFilesMenu}>
            {recentFiles.map((recentFile) => {
              const { name, path } = recentFile
              return (
                <Menu.Item
                  onClick={() => {
                    const model = editorInstance?.getModel()
                    getFileContent(path, encode).then(({ content, encoding }) => {
                      dispatch(setMonacoServerFile(recentFile))
                      dispatch(updateMonacoTabName(name))
                      dispatch(updateMonacoTabEncoding({ encoding }))
                      model?.pushEditOperations(
                        null,
                        [
                          {
                            range: model?.getFullModelRange(),
                            text: content,
                          },
                        ],
                        () => null,
                      )
                      editorInstance?.focus()
                      setPosition(model)
                    })
                  }}
                  key={path}
                >
                  <div className={styles.recentFile}>{name}</div>
                </Menu.Item>
              )
            })}
            {!recentFiles[0] && <Menu.Item disabled>{t('sdo_no_recent_doc')}</Menu.Item>}
            <Menu.Divider />
            <Menu.Item
              onClick={() => dispatch(showModal('ModalOpenServerFile', { isShowLocalFile: true }))}
            >
              {t('sdo_more')}...
            </Menu.Item>
          </Menu>
        }
        placement="bottomCenter"
        trigger={['hover']}
        mouseLeaveDelay={0.3}
        onVisibleChange={(visible) => {
          if (!visible) return
          refresh()
        }}
      >
        <Tooltip title={t('sdo_open_local_doc', {val: OpenDocument})}>
          <Iconfont
            type="icon-yunpanwenjianjia"
            className={styles.monacoToolbarIcon}
          />
        </Tooltip>
      </Dropdown>
      {/* 打开文档-本地上传 */}
      <input type="file" ref={fileInputRef} onChange={handleFileChange} style={{ display: 'none' }} />
    </Space>
  )
}
