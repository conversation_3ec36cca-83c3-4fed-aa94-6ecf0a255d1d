import React, { memo, useEffect, useRef } from 'react'
import * as _ from 'lodash';
import { Form, Input, message, Radio } from 'antd'
import { UIModal } from 'src/components'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { hideModal, showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { saveFavorite } from 'src/api'
import { useTranslation } from 'react-i18next'
import { getIsShareItemExtra } from '../common';

export const FavoriteEditModal = memo(() => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { ModalFavoriteAdd, ModalFavoriteManagement } = useSelector((state) => state.modal)

  const { visible = false,} = ModalFavoriteAdd;

  const { lastSavedFavorite } = useSelector((state) => state.queryTabs)
  const { id, statement, statementName, canShare } = lastSavedFavorite || {}
  const [favoriteAddForm] = Form.useForm()
  const favoriteManagementModalExtraProps = ModalFavoriteManagement?.extraProps || {}

  //是否共享
  const [shareFieldValue, setShareFieldValue] = React.useState<boolean | undefined>(undefined);

  const { run: trySaveFavorite, loading: loadingSubmit } = useRequest(
    saveFavorite,
    {
      manual: true,
      onSuccess: () => {
        message.success(t('sdo_save_suc'))
        handleHideModal()

        //收藏管理界面编辑 需要刷新列表数据
        if (!_.isEmpty(favoriteManagementModalExtraProps)) {
          dispatch(showModal('ModalFavoriteManagement', {...favoriteManagementModalExtraProps, needRefreshManagement: new Date().getTime() }))
        }
      },
    },
  )

  useEffect(() => {
    if (visible) {
      setShareFieldValue(canShare)
    }
    
  }, [visible, canShare])

  const handleSubmitAddApp = () => {
    favoriteAddForm.validateFields().then((values) => {
      const { statementName, statement, share } = values
      let paramsName: any = statementName
      if (!statementName) {
        paramsName = null
      }
      trySaveFavorite({ id, statementName: paramsName, statement, share })
    })
  }

  const handleHideModal = () => {
    dispatch(hideModal('ModalFavoriteAdd'))
  }

  const focusRef = useRef<Input>(null)
  useEffect(() => {
    if (!visible) return
    focusRef.current?.focus();
  }, [visible])

  return (
    <UIModal
      title={t('sdo_favorite_statement')}
      visible={visible}
      width={640}
      onCancel={handleHideModal}
      zIndex={500}
      onOk={() => favoriteAddForm.submit()}
      confirmLoading={loadingSubmit}
      afterClose={() => {
        favoriteAddForm.resetFields();
        setShareFieldValue(undefined)
      }}
      destroyOnClose={true}
    >
      <Form
        form={favoriteAddForm}
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 17 }}
        onFinish={handleSubmitAddApp}
      >
        <Form.Item 
          label={t('sdo_alias')} 
          name="statementName" 
          initialValue={statementName}
          rules={[{
            pattern: /^(?!\d+$)([a-zA-Z\u4E00-\u9FA5\uF900-\uFA2D0-9_]){1,64}$/, 
            message: t('sdo_alias_hint')}
          ]}
        >
          <Input ref={focusRef} placeholder={t('sdo_alias_input_placeholder')}></Input>
        </Form.Item>
        <Form.Item
          label={t('sdo_statement')}
          name="statement"
          initialValue={statement}
          rules={[{ required: true, message: t('sdo_statement_rule_txt') }]}
        >
          <Input.TextArea
            minLength={5}
            autoSize={{ maxRows: 20 }}
          ></Input.TextArea>
        </Form.Item>
        <Form.Item
          label={t('sdo_fav_isShare_label')}
          name="share"
          {...(id ? { initialValue: canShare } : {})}
          extra={
            <>
              {
               shareFieldValue === true  &&
                getIsShareItemExtra(true)
              }
              {
                shareFieldValue === false &&
                getIsShareItemExtra(false)
              }
            </>
          }
          rules={[{required: true, message: t('sdo_fav_isShare_plac')}]}
        >
          <Radio.Group onChange={(e: any) => setShareFieldValue(e?.target?.value)}>
            <Radio value={true}>{t('common.btn.yes')}</Radio>
            <Radio value={false}>{t('common.btn.no')}</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </UIModal>
  )
})
