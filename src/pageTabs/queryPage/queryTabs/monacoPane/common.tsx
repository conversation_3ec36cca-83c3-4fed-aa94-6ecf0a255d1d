import React from 'react';
import i18n from 'i18next';
import { Iconfont } from 'src/components';

// 构建tree结构 -- 不同的sdt展示类型，tree结构内容不一样
export function setTreeNodeKey(node: any, groupByType: boolean) {
  if (!node) return
  node.key = node.nodePath
  node.title = <div
    title={node?.alias || node.nodeName}
    style={{ display: 'inline-block', width: 'max-content', marginBottom: '-6px', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}
  >
    {node?.alias || node.nodeName}
  </div>
  node.selectable = !['datasource', 'connectionGroup'].includes(node.nodeType)
  node.value = node.nodePath
  // 获取icon类型
  const getIconType = () => { 
    switch (node.nodeType) {
      case 'connectionGroup':
        return 'icon-folder-fill'
      case 'datasource':
        return `icon-connection-${node.nodePath}`
      // 连接的图标在不同sdt展示类型下不一样
      default:
        return groupByType? `icon-${node.connectionType}` :`icon-connection-${node.connectionType}`
    }
  }
  node.icon = <Iconfont
    style={node.nodeType === 'connectionGroup' ? { color: '#3f84e9' } : {}}
    type={getIconType()}
  />
  if (node.children) {
    node.children.forEach((child: any) => setTreeNodeKey(child, groupByType))
  }
}

// 格式化数据按数据库类型分组
export const formatToolbarConnectionsWithGroupByType = (data: any[]) => {
  const dataSourceTypes = data.map(item => item.connectionType)
  const uniqueDataSourceTypes = [...new Set(dataSourceTypes)]
  const newTreeData = uniqueDataSourceTypes.map(dataSourceType => {
    const children = data.filter(node => node.connectionType === dataSourceType)
    const childrenCount = children.length
    return {
      nodePath: dataSourceType,
      nodeType:"datasource",
      nodeName: dataSourceType + '(' + childrenCount + ')',
      children
    }
  })
  return newTreeData
}

//保存sql是否共享提示信息

export const getIsShareItemExtra = (isShare: boolean) => {
  if (isShare) {
    return <>{i18n.t('sdo_fav_isShare_tip1_pre')}<span className='color3357ff'>{i18n.t('sdo_fav_isShare_tip1_postfix')}</span> </>
  }
  return  <>{i18n.t('sdo_fav_isShare_tip2_pre')}<span className='color3357ff'>{i18n.t('sdo_fav_isShare_tip2_postfix')}</span> </>
}
