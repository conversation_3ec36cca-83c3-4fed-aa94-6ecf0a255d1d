// sql审核结果弹框内容
import React, { useEffect, useMemo, useRef, useState } from "react"
import classNames from "classnames";
import { Modal, Pagination, Tooltip, Button } from "antd"
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { copyTextToClipboard } from 'src/util'
import { useDispatch, useSelector } from 'src/hook'
import { setExamineReviewResult } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { useTranslation } from "react-i18next";
import styles from './index.module.scss';
import { Iconfont } from "src/components";

interface IProps {
  activePaneKey: string
  execSegment: (params: any) => void,
}
const SqlExecuteAuditeModal = ({
  execSegment,
  activePaneKey
}: IProps) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { executeActiveTabParams, tabInfoMap, activeTabKey, connectionsAndUserSettingInfo } = useSelector((state) => state?.queryTabs);
  const curConnectionsAndUserSettingInfo = (connectionsAndUserSettingInfo && Object.values(connectionsAndUserSettingInfo)?.[0]) || {}
  //绑定当前语句元素的引用，以便于滚动到顶部
  const curStatementRef = useRef<HTMLDivElement>(null)
  const [reviewCurrent, setReviewCurrent] = useState<number>(1)
  const examineReviewResult = useSelector((state) => state.queryTabs.examineReviewResult)
  //queryTabkey 防止出现多个弹框
  const {queryTabKey, reviewResult = {}  } = examineReviewResult || {};
  const {reviewItems = [] } = reviewResult;
  
  const [examineModalVisible, setExamineModalVisible] = useState(false)

  useEffect(()=>{
    return ()=>{
      dispatch(setExamineReviewResult(null))  // 重置审核结果
      setReviewCurrent(1)
    }
  },[dispatch])

  useEffect(()=>{
      if (queryTabKey === activePaneKey) {
        setExamineModalVisible(!!reviewItems?.length)
      }
     
  }, [reviewItems, reviewItems?.length])

  const handlePageChange = (page: number) =>{
    setReviewCurrent(page);
    if (curStatementRef?.current) {
      curStatementRef.current.scrollTop = 0;
    }
  }

  const handleExamineModalClose = (execute?: boolean) => {
    setExamineModalVisible(false)
    dispatch(setExamineReviewResult(null))  // 重置审核结果
    setReviewCurrent(1)
    if(execute && activePaneKey){
      const memoParams = executeActiveTabParams[activePaneKey] || {}
      const params: any = {...memoParams, reviewResultId: examineReviewResult?.reviewResult?.reviewResultId}
      const curTabInfo = tabInfoMap?.[activeTabKey] || {};
      let pageSize = curTabInfo?.resultPageSize;

      if (tabInfoMap[activeTabKey].isScrollLoading === false) {
        if (typeof params.rowCount === 'number' && pageSize) {
          params.rowCount = pageSize + 1;
        }
      } else {
        params.rowCount = curConnectionsAndUserSettingInfo?.connectionExecSqlRowNum || 100;
      }
      delete params?.statements
      execSegment(params)
    }
  }

  const renderMessageInfo = (data: any[], type: string, color: string) => data?.map(
    (item: any, index: number)=>{
      const {brief, description, params} = item
      const paramParseArr = params && JSON.parse(params)
      return (
        <div key={index} className='flexAlignCenter mb10'>
          <span style={{color}} className='mr10 tc lh18' >
            <ExclamationCircleOutlined />
            <div>{type}</div>
          </span>
          <Tooltip 
            title={
              <>
                <div>{description}</div>
                {
                  paramParseArr?.length &&
                  <div>{t('sdo_param')}：{paramParseArr?.map((i: any)=>(`${i?.desc}:${i?.value}`))?.join(',')}</div>
                }
              </>
            }
          >
            <span>{brief}</span>
          </Tooltip> 
        </div>
      )
    }
  )

  const numberOfRuleMatched = useMemo(() => {
    const {error = [], warn = [], notice = []} = reviewItems?.[reviewCurrent-1] || {};

    return error.length + warn.length + notice.length;

  }, [reviewCurrent,reviewItems ])

  if(!examineModalVisible){
    return null
  }

  return (
    <Modal
      maskClosable={false}
      visible={examineModalVisible}
      onCancel={()=>handleExamineModalClose()}
      title={t('sdo_sql_audit_result')}
      width={680}
      bodyStyle={{padding: '16px 24px'}}
      footer={
        [3,4,6].includes(examineReviewResult?.reviewResult?.flag) 
        ?
          [
            <Button onClick={()=>handleExamineModalClose(true)}>{t('sdo_continue_execution')}</Button>,
            <Button onClick={()=>handleExamineModalClose()} className='ml10' type="primary">{t('sdo_return_modifications')}</Button>
          ]
        :
          [
            <Button  onClick={()=>handleExamineModalClose()} className='ml10' type="primary">{t('sdo_return_modifications')}</Button>
          ]
      }
    >
      {/* title */}
      <div className={styles.auditResultContent}>
      <div className={classNames(styles.statement,'fwb')}>
        <div className={styles.executeStatement}>
          <span>{`${reviewCurrent}/${reviewItems?.length}` }
             &nbsp;{t('sdo_sql_execute_audit_title')}
             <Tooltip title={t('sdo_sql_execute_audit_copy_statement')}>
               <Iconfont type='icon-copy' onClick={() => {copyTextToClipboard(reviewItems[reviewCurrent-1]?.statement)}}/>
             </Tooltip>
          </span>
          <Pagination 
            size='small'
            total={reviewItems?.length} 
            pageSize={1} 
            showLessItems={true}
            current={reviewCurrent} 
            onChange={handlePageChange}
            style={{textAlign: 'center'}}
          /> 
          </div>
        <div className={styles.statementDetail}  ref={curStatementRef}>
          {reviewItems[reviewCurrent-1]?.statement}
        </div>
      </div>
      <div className={styles.ruleCount} >{t('sdo_total')}<span className="color3357ff">{numberOfRuleMatched}</span>{t('sdo_records')}</div>
      {/* content */}
      <div className={styles.matchingRules}>
          {/* error */}
          {
            !!reviewItems[reviewCurrent-1]?.error?.length &&
            renderMessageInfo(reviewItems[reviewCurrent-1]?.error, 'error', '#FF3232')
          }
          {/* warn */}
          {
          !!reviewItems[reviewCurrent-1]?.warn?.length &&
          renderMessageInfo(reviewItems[reviewCurrent-1]?.warn, 'warn', '#F3833D')
          }
          {/* notice */}
          {
          !!reviewItems[reviewCurrent-1]?.notice?.length &&
          renderMessageInfo(reviewItems[reviewCurrent-1]?.notice, 'notice', '#3262ff80')
          }
        </div>
      </div>
    </Modal>
  )
}

export default SqlExecuteAuditeModal