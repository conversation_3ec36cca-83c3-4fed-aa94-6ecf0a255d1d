import React, { useRef, useState } from 'react'
import { But<PERSON>} from 'antd'
import {
  GridReadyEvent,
  GridApi,
  ColumnApi,
  ColDef,
  ColGroupDef,
} from '@ag-grid-community/core'
import { AgGridReact } from '@ag-grid-community/react'
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model'
import type { DataSourceType } from 'src/api'
import { isSupport, getEnumOptions } from 'src/util'

import { GridConfigBase } from '../../../resultTabs/resultContentGrid'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'src/hook'

interface ITabColumnPane {
  connectionId: string | number | undefined
  connectionType: DataSourceType
  theme: string
  onPreviewSQL: (params: any) => void
}

export const TabColumnPane = ({
  connectionType,
  theme,
  onPreviewSQL,
}: ITabColumnPane) => {
  const mockData: object[] = []
  const defaultColDef: ColDef = { editable: true }

  const gridColumnApiRef = useRef<ColumnApi>()
  const [rowData, setRowData] = useState<any[]>([])
  const gridApiRef = useRef<GridApi>()
  const cellBooleanEditor: ColDef | ColGroupDef = {
    cellEditor: 'agSelectCellEditor',
    cellEditorParams: { values: ['true', 'false'] },
  }
  const { t } = useTranslation()
  const { locales } = useSelector((state) => state.login)
  const gridConfig = GridConfigBase(locales);
  const getColumnTypeCellEditor = (connectionType: DataSourceType) => {
    switch (connectionType) {
      default:
        return 'agSelectCellEditor'
    }
  }

  const getColumnDefs = (
    connectionType: DataSourceType,
  ): (ColDef | ColGroupDef)[] => {
    const baseCol = [
      {
        field: 'columnName',
        headerName: t('sdo_field_name'),
      },
      {
        field: 'dataType',
        headerName: t('sdo_type'),
        cellEditor: getColumnTypeCellEditor(connectionType),
        cellEditorParams: {
          values: getEnumOptions('dataTypeEnum'),
        },
      },
      {
        field: 'length',
        headerName: t('sdo_length'),
        cellEditor: 'numericEditor',
        cellEditorParams: {
          min: 0,
          maxLength: 10,
        },
      },
      {
        field: 'scale',
        headerName: t('sdo_decimal_point'),
        cellEditor: 'numericEditor',
        cellEditorParams: {
          min: 0,
          maxLength: 10,
        },
      },
      {
        field: 'defaultValue',
        headerName: t('sdo_default_value'),
        cellRenderer: 'simpleTextRenderer',
      },
      {
        field: 'comment',
        headerName: t('sdo_comment'),
        cellEditor: 'agLargeTextCellEditor',
      },
      {
        field: 'virtual',
        headerName: t('sdo_virtual'),
        hide: !isSupport('supportedColumnProperty.virtualColumn'),
        ...cellBooleanEditor,
      },
      { field: 'nullable', headerName: t('sdo_allow_null'), ...cellBooleanEditor },
      {
        field: 'autoIncrease',
        headerName: t('sdo_auto_increment'),
        hide: !isSupport('supportedColumnProperty.autoIncrease'),
        ...cellBooleanEditor,
      },
      { field: 'isPrimary', headerName: t('sdo_primary_key'), ...cellBooleanEditor },
    ]

    return baseCol;
  }

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params.api
    gridColumnApiRef.current = params.columnApi
    setRowData(mockData)
    gridApiRef.current.sizeColumnsToFit()
  }

  const deleteColumn = () => {
    const selectedRows = gridApiRef.current?.getSelectedRows()
    gridApiRef.current?.applyTransactionAsync(
      { remove: selectedRows },
      () => {},
    )
  }

  const addColumn = () => {
    gridApiRef.current?.applyTransactionAsync(
      { add: [{ }] },
      (res) => {
        gridApiRef.current?.startEditingCell({
          rowIndex: Number(res.add[0]?.rowIndex),
          colKey: 'columnName',
        })
      },
    )
  }

  const handleSave = async() => {
    onPreviewSQL(gridApiRef.current)
  }

  return (
    <>
      <div style={{ margin: '0 0 16px 0' }}>
        <Button size="small" className="mr8" onClick={addColumn}>
          {t('sdo_add_column')}
        </Button>
        <Button size="small" className="mr16" onClick={deleteColumn}>
          {t('sdo_del_column')}
        </Button>
      </div>
      <div
        id="myGrid"
        style={{
          height: '60vh',
          width: '100%',
        }}
        // todo: 更好地统一处理主题样式
        className={
          theme === 'dark' ? 'ag-theme-balham-dark' : 'ag-theme-balham'
        }
      >
        <AgGridReact
          {...gridConfig}
          modules={[ClientSideRowModelModule]}
          columnDefs={getColumnDefs(connectionType)}
          defaultColDef={defaultColDef}
          rowDragManaged={true}
          animateRows={true}
          onGridReady={onGridReady}
          rowData={rowData}
          rowSelection="single"
          stopEditingWhenCellsLoseFocus
        />
      </div>
      <Button 
        type='primary'
        style={{position:'fixed', bottom: 66, right: 42}}
        onClick={handleSave}
      >
        {t('sdo_save')}
      </Button>  
    </>
  )
}
