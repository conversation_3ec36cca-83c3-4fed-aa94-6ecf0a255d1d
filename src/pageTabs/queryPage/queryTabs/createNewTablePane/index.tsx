import React, { useRef, useState, useCallback, useEffect } from 'react'
import * as _ from 'lodash'
import {  Descriptions, Input, message, Tabs, Modal } from 'antd'
import {
  executeMenuActionSql,
  newGenerateCreateSql,
  GenerateSqlParams,
  getSqlCheck
} from 'src/api'

import { getSchemaFormConnectionType, correctComment } from 'src/util'
import { INCLUDE_SCHEMA_CONNECTIONS } from 'src/constants'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import SqlExecuteAuditeModal from '../monacoPane/SqlExecuteAuditeModal';
import { DatabaseInfo, setExamineReviewResult } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { refreshOnRoot } from '../../sdt'
import { TabColumnPane } from './components'
import styles from './index.module.scss'
import { useTranslation } from 'react-i18next'

const { TabPane } = Tabs
interface CreateTablePaneProps {
  databaseInfo: Partial<DatabaseInfo>
  queryTabKey: string
}

export const CreateNewTablePane = ({
  databaseInfo,
  queryTabKey,
}: CreateTablePaneProps) => {
  const { theme } = useSelector((state) => state.appearance)
  const { t } = useTranslation()

  const {
    connectionId,
    connectionType = 'MySQL',
    databaseName,
    nodePath,
    nodePathWithType
  } = databaseInfo

  const { toolbarConnections } = useSelector((state) => state.editor)

  const connection = toolbarConnections?.find(
    (t) => t.connectionId === connectionId,
  )
  const connectionName = connection?.alias
    ? connection.alias
    : connection?.nodeName

    const schemaName = (() => {
      if (!nodePath) return ;
      let transformedSchemaName = getSchemaFormConnectionType(connectionType, nodePath);
       return transformedSchemaName;
    })()

  //创建表OracleCDB operatingObject 特殊处理
  const operatingObject = ['OracleCDB'].includes(connectionType)
    ? schemaName
    : databaseName
 // 缓存所有待提交的参数
 const generateParamsRef = useRef<Partial<GenerateSqlParams>>({})

  const [generatedSql, setGeneratedSql] = useState('')
  const [tableName, setTableName] = useState<string>('')
  const [tableComment, setTableComment] = useState<string>('')
  const [activeTabKey, setActiveTabKey] = useState<string>('columns')
  const [previewModalVisible, setPreviewModalVisible] = useState(false)
  const [previewSql, setPreviewSql] = useState<string>('')
  const dispatch = useDispatch()

  useEffect(()=>{
    setPreviewSql(generatedSql)
  },[generatedSql, previewModalVisible])

  //sql审核
  const {run: runCheckSqlStatement } = useRequest(getSqlCheck,{
    manual: true,
    onSuccess: (res) => {

      if (res) {
        dispatch(setExamineReviewResult({queryTabKey, reviewResult: res }))
      }else {
        onCreateTable()
      }
      
    }
});

  const { run: createTable } = useRequest(executeMenuActionSql, {
    manual: true,
    onSuccess: (result) => {
      const [{ executeError }] = result
      if (executeError) {
        return message.error(executeError.message)
      }
      message.success(t('sdo_table_creation_suc'))
      dispatch(refreshOnRoot())
    },
  })

  const getBaseFieldInfo = () => {
    let baseFiles: {schemaName?: string; databaseName?: string}= {schemaName};
     // todo: 从后端或数据源模块获取配置，自动处理数据源差异

    if (!['DamengDB', 'DB2'].includes(connectionType)) {
      baseFiles.databaseName = databaseName
    }

    generateParamsRef.current={
      connectionType,
      ...generateParamsRef.current,
      ...baseFiles
    }
  }

  const tryGenerateSql = (rowData: any) => {
    const columns: any[] = []
    const primaryKeys: any[] = []
    //@ts-ignore
    rowData?.forEachNode((rowNode, index) => {
      const { data } = rowNode
      const { columnName, isPrimary, comment } = data
      // 修正备注中带特殊符号的
      columns.push({
        ...data,
        comment: correctComment(connectionType, comment),
      })
      isPrimary ==='true' && primaryKeys.push(columnName)
    })

    let sqlParams: GenerateSqlParams = {
      ...generateParamsRef.current,
      connectionType,
      tableName,
      primaryKeys,
      columns,
    }

    if (tableComment) {
      sqlParams.tableInfo = {
        comment: correctComment(connectionType, tableComment),
      }
    }
    //方式tableName comment 修改重新执行这里
    generateParamsRef.current = sqlParams;
    newGenerateCreateSql(sqlParams)
      .then(({ sql }) => setGeneratedSql(sql))
      .catch(() => setGeneratedSql(''))
      .finally(()=>{
        setPreviewModalVisible(true)
      })
  }

  useEffect(() => {
    getBaseFieldInfo()
  },[connectionType, connectionId])

  const onCreateTable = () => {
    if (!connectionId || !generatedSql) return
    if (!tableName) return message.error(t('sdo_table_name_required'))
    
    createTable({
      connectionId,
      dataSourceType: connectionType,
      databaseName,
      operatingDatabase: databaseName,
      operatingObject: operatingObject,
      // plSql,
      tabKey: queryTabKey,
      statements: [generatedSql],
    })
  }

  const handleSubmit = () => {
    if (!previewSql || !connectionId || !nodePathWithType) return;
    runCheckSqlStatement({
      connectionId,
      dataSourceType: connectionType,
      nodePathWithType,
      scenario: "GUI",
      statements: [previewSql]
    })
    setPreviewModalVisible(false)
  }

  // 表 或 表注释 生成sql,避免重复操作
  const onGenreateBaseInfoSql = () => {
    if (generateParamsRef.current) {
    //@ts-ignore
      newGenerateCreateSql({...generateParamsRef.current})
      .then(({ sql }) => setGeneratedSql(sql))
      .catch(() => setGeneratedSql(''))
    }
  }
  //change 太过频繁
  const onChangeBaseInfo = useCallback(
    _.throttle(onGenreateBaseInfoSql, 1000)
    , [])

  const handlePreviewSql = (rowData: GenerateSqlParams) => {
    if (!tableName) return message.error(t('sdo_table_name_required'))
    tryGenerateSql(rowData)
  }

  const handlePreviewCancle = () => {
    setPreviewModalVisible(false)
  }

  const handleSqlChange = (e: any) => {
    const value = e.target.value?.trim()
    setPreviewSql(value)
  }

  return (
    <div className={styles.createNewTablePane}>
      <Descriptions column={2}>
        <Descriptions.Item label={t('sdo_connection_name')}>{connectionName}</Descriptions.Item>
        {!['DamengDB', 'DB2'].includes(connectionType) && (
          <Descriptions.Item label={t('sdo_database')}>{databaseName}</Descriptions.Item>
        )}
        {INCLUDE_SCHEMA_CONNECTIONS.includes(connectionType) && (
          <Descriptions.Item label="Schema">{schemaName}</Descriptions.Item>
        )}
        <Descriptions.Item label={t('sdo_table_name')}>
          <Input.TextArea
            value={tableName}
            onChange={(e) => {
  
              setTableName(e.target.value);
              generateParamsRef.current = {
                ...generateParamsRef.current,
                tableName: e.target.value
              }
              onChangeBaseInfo()
            }}
            style={{ width: 300 }}
            rows={2}
            className="mr16"
          />
        </Descriptions.Item>
        <Descriptions.Item label={t('sdo_table_comment')}>
          <Input.TextArea
            value={tableComment}
            onChange={async(e) => {
              setTableComment(e.target.value)
              if (_.isEmpty(e.target.value)) {
                delete generateParamsRef.current?.tableInfo
              }else {
                generateParamsRef.current = {
                  ...generateParamsRef.current,
                  tableInfo:{
                    comment: correctComment(connectionType, e.target.value)
                  } 
                }
              }
              onChangeBaseInfo()
            }}
            style={{ width: 300 }}
            rows={2}
          />
        </Descriptions.Item>
      </Descriptions>
      <Tabs
        tabPosition="top"
        activeKey={activeTabKey}
        className={styles.tab}
        onChange={(key) => setActiveTabKey(key)}
      >
        <TabPane tab={t('sdo_field')} key="columns" tabKey="columns">
          <TabColumnPane
            connectionId={connectionId}
            connectionType={connectionType}
            theme={theme}
            onPreviewSQL={handlePreviewSql}
          />
        </TabPane>
      </Tabs>
        {/* sql审核结果 */}
        <SqlExecuteAuditeModal
          activePaneKey={queryTabKey}
          execSegment={onCreateTable}
        />
        {
          previewModalVisible &&
          <Modal 
            width={532}
            title={t('sdo_sql_preview')}
            okText={t('sdo_submit')}
            visible={true}
            onOk={handleSubmit}
            onCancel={handlePreviewCancle}
            okButtonProps={{disabled: !previewSql}}
            maskClosable={false}
          >
            <Input.TextArea
              placeholder={t('sdo_sql_preview_placeholder')}
              autoSize={{ minRows: 6, maxRows: 28 }}
              value={previewSql}
              onChange={handleSqlChange}
            />
          </Modal>
        } 
    </div>
  )
}
