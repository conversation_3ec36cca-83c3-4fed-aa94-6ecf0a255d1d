import React, {useEffect, useMemo, useRef, useState} from 'react'
import {AgGridReact} from '@ag-grid-community/react'
// ChangeDetectionStrategyType 在 ag-grid 29 版本中已弃用，现在总是使用 IdentityCheck
import {ClientSideRowModelModule} from '@ag-grid-community/client-side-row-model'
import {ColumnApi, GridApi, GridReadyEvent} from '@ag-grid-community/core'
import {Button, message, Spin, Modal, Input } from 'antd'
import {isEmpty, isEqual, cloneDeepWith, find, omit} from 'lodash'
import {useRequest, useSelector, useDispatch} from 'src/hook'
import {GridConfigBase} from 'src/pageTabs/queryPage/resultTabs/resultContentGrid'
import {
    TableConstraintResponse,
    TableForeignKeyResponse,
    TableColumnResponse, 
    QueryTableInfoParams,
    getTableConstraints_api,
     getTableForeignKey_api, 
     getTableIndexes_api, 
     getTableColumn_api,
     getRefDatabaseNames,
     getRefSchemaNames,
     getRefTableNames,
     getRefColumnNames,
     getTableTriggers_api
} from 'src/api'
import { updateDesignInfo } from '../../designTableSlice'
import {DataSourceType, IIndex} from 'src/types'
import {correctComment, getEnumOptions, isSupport} from 'src/util'
import { TriggerExtraContent } from './TriggerExtraContent'
import { useTranslation } from 'react-i18next'

// 根据数据源类型判断 mysql是被引用数据库
const DataBaseTypesForHasSchema = ['Oracle', 'PostgreSQL', 'SQLServer', 'DamengDB', 'OceanBase', 'PolarDB','HighGo',
'DB2','OpenGauss','GaussDB','GaussDB_DWS','Greenplum','KingBaseOracle' ,'KingBasePG','MogDB','TDSQLPG','OracleCDB', 
'GBase8c','HANA', 'Oscar']
// 触发器的触发事件可多选的数据源
const DataBaseTypesForHasTriggerMultiEvent = ['MySQL','Oracle', 'PostgreSQL', 'SQLServer','DamengDB','OceanBase',
'PolarDB','HighGo', 'TeleDB','DB2','GaussDB','GaussDB_DWS','TDSQLPG','Greenplum','KingBaseOracle','KingBasePG','MogDB',
'OpenGauss','HANA','OracleCDB']

//索引 字段 单选
const INDEX_DATASOURCE_WITH_SINGLE_NAME = ['StarRocks'];

export interface IDesignIndexProps {
    connectionId?: string | number
    connectionType: DataSourceType
    nodeType?: string;
    databaseName?: string
    schemaName: string | undefined
    tableName: string
    tableKey: string
    originData?: (TableConstraintResponse & { key?: string })[] | (TableForeignKeyResponse & { key?: string })[] | (TableColumnResponse & { key?: string })[] | undefined
    queryTabKey: string
    nodePath?: string
    nodePathWithType?: string
    modeType?: 'designTable' | 'viewTableStructure'
    successfulCallback: () => void
    onSave: (data: any) => void
    sql?: string
    disabledEdit: boolean
    onSubmit?: (p:string) => void
}

const ColumnAPIMapping: {[key: string]: any} = {
    constraints: getTableConstraints_api,
    fkConstraints: getTableForeignKey_api,
    indexes: getTableIndexes_api,
    columns: getTableColumn_api,
    triggers:getTableTriggers_api
}

export const DesignPane = (props: IDesignIndexProps) => {
    const {
        sql,
        connectionId,
        connectionType,
        nodeType,
        databaseName,
        schemaName,
        tableName,
        tableKey,
        queryTabKey,
        nodePath,
        nodePathWithType,
        modeType,
        onSave,
        disabledEdit,
        onSubmit
    } = props
    const { t } = useTranslation()
    const referencedFieldAction = DataBaseTypesForHasSchema.includes(connectionType)
      ? getRefSchemaNames : getRefDatabaseNames
    const gridApiRef = useRef<GridApi>()
    const gridColumnApiRef = useRef<ColumnApi>()
    const {tableDataCollection} = useSelector(state => state.designTable)

    const dispatch = useDispatch()
    const {theme} = useSelector((state) => state.appearance)
    const { locales } = useSelector((state) => state.login)
    const gridConfig = GridConfigBase(locales);

    // 表格数据
    const [rowData, setRowData] = useState<any>([])
    //行选中数据 置灰数据无法focus
    const [selectedRowData, setSelectedRowData] = useState<any>(null)
    //触发器 body
    const [focusRowData,setFocusRowData] = useState<any>(null)
    const [previewModalVisible, setPreviewModalVisible] = useState(false)
    const [previewSql, setPreviewSql] = useState<string>('')
    //保存按钮位置
    const [saveBtnDistanceToRight, setSaveBtnDistanceToRight] = useState<number>(50)

    useEffect(()=>{
        setPreviewSql(sql ?? '')
    },[sql, previewModalVisible])

    // 获取表格信息
    let {
        data: originData,
        loading:getOriginDataLoading,
        refresh
    } = useRequest(
        () => {
            if (!(connectionId && nodePath && tableName)) return
            setFocusRowData(null);
            let params = {
                connectionId,
                connectionType,
                databaseName,
                schemaName,
                tableName,
                nodePath,
                nodePathWithType,
            }
           
           const request = ColumnAPIMapping[tableKey]
            return request(params)
        },
        {
            onSuccess: (data) => {
                const tipMapping: any = {
                    columns: 'columnName',
                    indexes: 'indexName',
                    constraints: 'constraintName',
                    fkConstraints: 'constraintName',
                    triggers: 'triggerName'
                }

               

               const originData =  data && cloneDeepWith(data.map((i: any) => {
            
                    // 赋值标示
                    const key = tipMapping[tableKey] || ''
                    
                        // @ts-ignore
                        i.key = i[key];
                    // if (tableKey === 'columns') {
                    //     // @ts-ignore
                    //     i.key = i.columnName;
                    // }
                    return i
                }))
                setRowData(originData)
            },
            refreshDeps:[tableName]
        }
    )

    // 外键 额外调用接口
    const {data: refSchameOrDatabaseData, run: getRefSchameOrDatabaseList} = useRequest(referencedFieldAction,{
        manual: true,
        formatResult: (res) => res?.names || []
    })
    
    const { run: getRefTableList} = useRequest(getRefTableNames,{
          manual: true,
          formatResult: (res) => res?.names || []
      })

      const {run: getRefColumnList} = useRequest(getRefColumnNames,{
          manual: true,
          formatResult: (res) => res?.names || []
      })

      useEffect(() => {
       if (tableKey === 'fkConstraints' && !disabledEdit) {
        getRefSchameOrDatabaseList({connectionId,connectionType})
       }
      },[tableKey,disabledEdit])

    //存储字段数据 其他tab有使用到
    useEffect(() => {
        if (tableKey === 'columns') {
            dispatch(updateDesignInfo({tabKey:queryTabKey, rowData: rowData}))  
        }
    },[rowData])

    const onGridReady = (params: GridReadyEvent) => {
        gridApiRef.current = params.api
        gridColumnApiRef.current = params.columnApi
        gridApiRef.current.sizeColumnsToFit()
    }
    //根据宽度调整表格宽度
    useEffect(() => {
        const element = document.getElementById('myGrid');
        const handleResize = () => {
            gridApiRef?.current?.sizeColumnsToFit();
            const rect = element?.getBoundingClientRect();
            if (rect?.right) {
                const distanceToRight = window.innerWidth - rect?.right; // 计算右侧距离
                setSaveBtnDistanceToRight(distanceToRight + 10)
            }
        };

        const resizeObserver = new ResizeObserver(handleResize);
        if (element) {
          resizeObserver.observe(element);
        }
    
        return () => {
          if (element) {
            resizeObserver.unobserve(element);
          }
        };
      }, []);

    // 保存后，父组件会置空sql，标示保存操作，需要刷新当前页面请求
    useEffect(() => {
        if (sql === '') {
            refresh()
        }
    }, [sql])

    const getColumnSelect = () => {
        const columnFieldList = tableDataCollection[queryTabKey] || []
        if (isEmpty(columnFieldList) ){
            return []
        }
        return columnFieldList.map((item: any) => item.columnName)
    }

    //联动 字段onchange事件
    const onResetSelectValue = async(values: any) => {

        const { field, rowIndex,oldValue, columns} = values
    
        const rowNode = gridApiRef.current?.getRowNode(rowIndex)

        if ((field === 'refSchemaName' ||field === 'refDatabaseName') && columns[field] !== oldValue){
            rowNode?.setDataValue('refColumnNames','')
            rowNode?.setDataValue('refTableName','')
        }
        if (field === 'refTableName') {
  
            rowNode?.setDataValue('refColumnNames','')
        }
    }
    // 基础列信息
    const getBaseColumn = (tabKey: string, connectionType: string) => {
  
        //约束
        if (tabKey === 'constraints') {
            return [
                {
                    headerName: t('sdo_constraint_name'),
                    field: 'constraintName',
                    editable: false,
                },
                {
                    headerName: t('sdo_field'),
                    field: 'columnNames',
                    editable: false,
                    cellEditor: 'columnMultiSelectEditor',
                    cellEditorParams: {
                        columnsOptions: getColumnSelect(),
                    }
                },
                {
                    headerName: t('sdo_constraint_type'),
                    minWidth: 120,
                    field: 'constraintType',
                    editable: false,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: getEnumOptions('constraintTypeEnum'),
                    }
                },
                {
                    headerName: t('sdo_comment'),
                    field: 'comment',
                    hide: !isSupport('supportedConstraintProperty.comment'),
                    editable: false,
                },
                {
                    headerName: t('sdo_condition'),
                    field: 'condition',
                    hide: !isSupport('supportedConstraintProperty.condition'),
                    editable: false,
                },
                {
                    headerName: t('sdo_enable'),
                    field: 'enabled',
                    hide: !isSupport('supportedConstraintProperty.enabled'),
                    editable: false,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: ['true', 'false'],
                    },
                }
            ]
        } else if (tabKey === 'fkConstraints') {//外键
            return [
                {
                    headerName: t('sdo_constraint_name'),
                    field: 'constraintName',
                    editable: false,
                },
                {
                    headerName: t('sdo_field'),
                    field: 'columnNames',
                    editable: false,
                    cellEditor: 'columnMultiSelectEditor',
                    cellEditorParams: {
                        columnsOptions: getColumnSelect(),
                    }
                },
                {
                    headerName: t('sdo_comment'),
                    field: 'comment',
                    hide: !isSupport('supportedFKConstraintProperty.comment'),
                    editable: false,
                },
                {
                    headerName: DataBaseTypesForHasSchema.includes(connectionType) ? t('sdo_referenced_schema') : t('sdo_referenced_database'),
                    field: DataBaseTypesForHasSchema.includes(connectionType) ? 'refSchemaName' : 'refDatabaseName',
                    editable: false,
                    cellEditor: 'antdSelectEditor',
                    cellEditorParams: {
                        columnsOptions:refSchameOrDatabaseData,
                        onValueChange :async(values: any) => {
                        
                            await onResetSelectValue(values)
                        
                        }
                    }   
                },
                {
                    headerName: t('sdo_referenced_table'),
                    field: 'refTableName',
                    editable: false,
                    cellEditor: 'antdSelectEditor',
                    cellEditorParams: {
                        //传过去async方法试试
                        async: true,
                        onValueChange :async(values: any) => {
                            await onResetSelectValue(values)
                        },
                        //@ts-ignore
                        columnsOptions: async(params: any) => {
                            const {data = {}} = params
                        let tableParams:QueryTableInfoParams  = {
                            connectionId,
                            connectionType
                        }
                        if (DataBaseTypesForHasSchema.includes(connectionType)) {
                            tableParams['schemaName']  = data?.refSchemaName
                        }else {
                            tableParams['databaseName'] = data?.refDatabaseName
                        }
                        if ( (data?.refSchemaName || data?.refDatabaseName) && !disabledEdit) {
                            return await getRefTableList({...tableParams})
                        }else {
                            return []
                        }
                    },
                    }
                },
                {
                    headerName: t('sdo_referenced_field'),
                    field: 'refColumnNames',
                    editable: false,
                    cellEditor: 'columnMultiSelectEditor',
                    cellEditorParams: {
                        //传过去async方法试试
                        async: true,
                        onValueChange :async(values: any) => {
                            await onResetSelectValue(values)
                        },
                        columnsOptions: async(params: any) => {
                            const {data={}} = params
                        let tableParams:QueryTableInfoParams  = {
                            connectionId,
                            connectionType
                        }
                        if (DataBaseTypesForHasSchema.includes(connectionType)) {
                            tableParams['schemaName']  = data?.refSchemaName
                        }else {
                            tableParams['databaseName'] = data?.refDatabaseName
                        }
                
                        if (data?.refTableName && !disabledEdit &&  (data?.refSchemaName || data?.refDatabaseName)) {
                            return await getRefColumnList({...tableParams,tableName:data?.refTableName})
                
                        }else {
                            return []
                        }
                        }
                },
            },
                {
                    headerName: t('sdo_update_rule'),
                    minWidth: 100,
                    hide: !isSupport('supportedFKConstraintProperty.updateRule'),
                    field: 'updateRule',
                    editable: false,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: getEnumOptions('updateRuleEnum'),
                    }
                },
                {
                    headerName: t('sdo_delete_rule'),
                    minWidth: 100,
                    hide: !isSupport('supportedFKConstraintProperty.deleteRule'),
                    field: 'deleteRule',
                    editable: false,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: getEnumOptions('deleteRuleEnum')
                    }
                },
                {
                    headerName: t('sdo_enable'),
                    field: 'enabled',
                    hide: !isSupport('supportedFKConstraintProperty.enabled'),
                    editable: false,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: ['true', 'false'],
                    },
                },
            ]
        } else if (tabKey === 'indexes') {
            return [
                {
                    headerName: t('sdo_index_name'),
                    field: 'indexName',
                    editable: false,
                },
                {
                    headerName: t('sdo_field'),
                    field: 'columnNames',
                    editable: true,
                    hide: ['HANA'].includes(connectionType),
                    cellEditor: INDEX_DATASOURCE_WITH_SINGLE_NAME.includes(connectionType)
                    ? 'columnSpecialMultiSelector': 'columnMultiSelectEditor' ,
                  cellEditorParams: {
                    columnsOptions: getColumnSelect()
                  }
                },
                {
                    headerName: t('sdo_type'),
                    minWidth: 120,
                    field: 'indexType',
                    editable: false,
                    cellEditor: 'selectEditor',
                    cellEditorParams: {
                        values: getEnumOptions('indexTypeEnum'),
                    }
                },
                {
                    headerName: t('sdo_comment'),
                    field: 'comment',
                    hide: !isSupport('supportedIndexProperty.comment'),
                    editable: false,
                },
                {
                    headerName: t('sdo_unique'),
                    field: 'unique',
                    editable: false,
                    cellEditor: 'agSelectCellEditor',
                    hide: !isSupport('supportedIndexProperty.unique'),
                    cellEditorParams: {
                        cellHeight: 50,
                        values: ['true', 'false'],
                    },
                }
            ]
        } else if (tabKey === 'columns') {
            return [
                {
                    headerName: t('sdo_field_name'),
                    field: 'columnName',
                    editable: true,
                },
                {
                    headerName: t('sdo_type'),
                    minWidth: 120,
                    field: 'dataType',
                    editable: true,
                    cellEditor: 'selectEditor',
                    cellEditorParams: {
                        values: getEnumOptions('dataTypeEnum')
                    }
                },
                {
                    headerName: t('sdo_length'),
                    field: 'length',
                    editable: true,
                    cellEditor: 'numericEditor',
                    cellEditorParams: {
                        min: 0,
                        maxLength: 10
                    }
                },
                {
                    headerName: t('sdo_decimal_point'),
                    field: 'scale',
                    hide: !isSupport('supportedColumnProperty.decimal'),
                    editable: true,
                    cellEditor: 'numericEditor',
                    cellEditorParams: {
                        min: 0,
                        maxLength: 10
                    }
                },
                {
                    headerName: t('sdo_default_value'),
                    field: 'defaultValue',
                    editable: true,
                    hide: (modeType === 'designTable' && ['Trino', 'Presto'].includes(connectionType))||
                    ['Hive','Impala','Inceptor', 'Phoenix', 'PolarDB'].includes(connectionType),
                },
                {
                    headerName: t('sdo_comment'),
                    field: 'comment',
                    editable: true,
                    hide: (modeType === 'designTable' && ['Trino', 'Presto'].includes(connectionType)) || ['Phoenix'].includes(connectionType),
                },
                {
                    headerName: t('sdo_virtual'),
                    field: 'virtual',
                    hide: !isSupport('supportedColumnProperty.virtualColumn'),
                    editable: true,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: ['true', 'false'],
                    }
                },
                {
                    headerName: t('sdo_allow_null'),
                    field: 'nullable',
                    editable: true,
                    cellEditor: 'agSelectCellEditor',
                    hide: ['Hive','Impala', 'Inceptor'].includes(connectionType),
                    cellEditorParams: {
                        values: ['true', 'false'],
                    }
                },
                {
                    headerName: t('sdo_auto_increment'),
                    field: 'autoIncrease',
                    hide: !isSupport('supportedColumnProperty.autoIncrease'),
                    editable: true,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: ['true', 'false'],
                    }
                },
                {
                    headerName: t('sdo_primary_key'),
                    field: 'primary',
                    editable: true,
                    cellEditor: 'agSelectCellEditor',
                    //gbase的设计表 主键需要去掉 其他按照后端返回控制
                    hide: (modeType==='designTable' && ['GBase'].includes(connectionType))  || !isSupport('supportedColumnProperty.primary'),
                    cellEditorParams: {
                        values: ['true', 'false'],
                    }
                },
                {
                    headerName: t('sdo_sort_key'),
                    field: 'sortKey',
                    editable: true,
                    hide: !isSupport('supportedColumnProperty.sortKey') || (connectionType === 'StarRocks' && nodeType === 'foreignTable') ,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: ['true', 'false'],
                    }
                }
            ]
        }else if (tabKey === 'triggers') {
            return [
                {
                    headerName: t('sdo_trigger_name'),
                    field: 'triggerName',
                    editable: true,
                },
                {
                    headerName: t('sdo_trigger_timing'),
                    minWidth: 120,
                    hide: !isSupport('supportedTriggerProperty.timing'),
                    field: 'timing',
                    editable: true,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: getEnumOptions('triggerTimingEnum')
                    }
                },
                {
                    headerName: t('sdo_trigger_event'),
                    field: 'events',
                    editable: true,
                    cellEditor: DataBaseTypesForHasTriggerMultiEvent.includes(connectionType)
                      ? 'columnMultiSelectEditor' : 'columnSpecialMultiSelector',
                    cellEditorParams: {
                        columnsOptions: getEnumOptions('triggerEventEnum')
                    }
                },
                {
                    headerName: t('sdo_level'),
                    field: 'level',
                    editable: true,
                    cellEditor: 'agSelectCellEditor',
                    hide: !isSupport('supportedTriggerProperty.level'),
                    cellEditorParams: {
                        values: getEnumOptions('triggerLevelEnum')
                    }
                },
                {
                    headerName: t('sdo_refd_old_value'),
                    field: 'referenceOld',
                    hide: !isSupport('supportedTriggerProperty.referenceOld'),
                    editable: true,
                },
                {
                    headerName: t('sdo_refd_new_value'),
                    field: 'referenceNew',
                    hide: !isSupport('supportedTriggerProperty.referenceNew'),
                    editable: true,
                },
                {
                    headerName: t('sdo_updated_field'),
                    field: 'updateColumns',
                    hide: !isSupport('supportedTriggerProperty.updateColumns'),
                    cellEditor: 'columnMultiSelectEditor',
                    cellEditorParams: {
                        columnsOptions: getColumnSelect(),
                    }
                },
                {
                    headerName: t('sdo_when_clause'),
                    field: 'when',
                    hide: !isSupport('supportedTriggerProperty.when'),
                    editable: true,
                },
                {
                    headerName: t('sdo_comment'),
                    field: 'comment',
                    hide: !isSupport('supportedTriggerProperty.comment'),
                    editable: true,
                },
                {
                    headerName: t('sdo_enable'),
                    field: 'enabled',
                    hide: !isSupport('supportedTriggerProperty.enabled'),
                    editable: true,
                    cellEditor: 'agSelectCellEditor',
                    cellEditorParams: {
                        values: ['true', 'false'],
                    }
                }
            ]
        }
    }

    // 新增约束
    const add = () => {
        gridApiRef.current?.applyTransactionAsync(
            {add: [{operationType: 'ADD'}]},
            (res) => {
                gridApiRef.current?.startEditingCell({
                    rowIndex: Number(res.add[0]?.rowIndex),
                    colKey: 'indexName',
                })
                //滚动条自动定位新增数据位置
                gridApiRef?.current?.ensureIndexVisible(Number(res.add[0]?.rowIndex))
            },
        )
    }

    // 删除约束
    const del = () => {
        const selectedRows = gridApiRef.current?.getSelectedRows()
        const selectedNodes = gridApiRef.current?.getSelectedNodes()
        if (!selectedRows || !selectedRows[0] || !selectedNodes) return
        gridApiRef.current?.applyTransactionAsync({remove: selectedRows})
        const {rowIndex} = selectedNodes[0] as any
        setRowData((prevData: any) => {
            prevData?.splice(rowIndex, 1)
            return prevData
        })
    }

    // 获取表格数据
    const getGridData = () => {
        const data: IIndex[] = []
        gridApiRef.current?.forEachNode((node) => {
            data.push(node.data)
        })
        return data
    }

    // 失焦时获取整体表格数据
    const updateRowData = () => { 
        const rowData = getGridData()
        // @ts-ignore
        setRowData(rowData)
        if(tableKey === 'column') {
            dispatch(updateDesignInfo({tabKey: queryTabKey,rowData} ))
        }
    }

    // 保存
    const handleSave = async () => {
      try {   
        // 找出差异值
        let payload = formatData(getDiffPayload()) || {}
        await onSave({[tableKey]: payload, primaryKey: getDiffExtraPayload()})
        setPreviewModalVisible(true)
      } catch (error) {
        message.success(t('sdo_save_fail'))
      }
    }

    // 计算用户填写和原数据的 diff 结果
    const getDiffPayload = () => {
        let result:any[] = []
        // 1. 过滤出 operationType 是 add 的为新增
        rowData?.forEach((item:any) => {
            if(item.operationType === 'ADD'){
                result.push({
                    "operationType": "ADD",
                    newData:omit(item,'operationType'),
                    oldData:null
                })
            }
            
        })
        // 2. 遍历之前的数据 originData，如果 key （由 columnName 生成） 有在 rowData 时，表示 修改 alter
        originData?.forEach((origin: any) => {
            let newData = find(rowData, {key: origin.key})
            if (!newData) {
                // 3. 如果不在 rowData，则表示删除
                result.push({
                    "operationType": "DELETE",
                    newData:null, 
                    oldData:{...origin}
                })
            } else if (!isEqual(origin, newData)) {
                // 3.1 如果字段，则把新的字段给出
                result.push({
                    "operationType": "ALTER",
                    newData:omit(newData,'operationType'),
                    oldData:{...origin}
                })
            }
        })
        return result
    }

    // 计算特殊字段的 diff 结果
    const getDiffExtraPayload = () => {
        const primaryKeys: string[] = []
        const newPrimaryKeys: string[] = []
        originData?.forEach((item: any) => {
            if (item.primary === true || item.primary === 'true') {
                primaryKeys.push(item.columnName)
            }
        })
        rowData?.map((item: any) => {
            if (item.primary === true || item.primary === 'true') {
                newPrimaryKeys.push(item.columnName)
            }
        })
        if (!isEqual(primaryKeys, newPrimaryKeys)) {
            return {primaryKeys, newPrimaryKeys}
        }
    }

    const formatData = (dataArr: any[]) => {
        if (!dataArr) return []
        return dataArr.map(item => {
            const {newData = {}} = item
            return {
                ...item,
                newData: isEmpty(newData) ? null: {
                    ...newData,
                    comment: newData?.comment && correctComment(connectionType,newData.comment)
                }
            }
        })
    }

    //设置触发器body字段
    const onResetTriggerBody = (params={}) => {
        const {rowIndex, data = {}} = focusRowData || {};
        setFocusRowData({
            rowIndex,
            data: {
                ...data,
                ...params
            }
        })
        let changedRowData = rowData && rowData.map((item: any) => {
         if (item?.triggerName === data?.triggerName) {
          return ({
            ...item, 
            ...params
          })
         }
         return item
        })
         // @ts-ignore
         setRowData(changedRowData)
    }

  const delBtnDisableStatus = useMemo(() => {
      //gbase 索引禁止删除 ,触发器 可以删除已有数据
      if (['GBase'].includes(connectionType)) {
          if (tableKey === 'indexes') {
              return true;
          }else if (['triggers'].includes(tableKey)) {
              return false;
          }  
      }

      //部分特殊处理
      if (!selectedRowData) return false;

        //hive 新增数据可删 已有数据禁止删除
      if (['Hive','Impala', 'Inceptor'].includes(connectionType) && selectedRowData?.data?.operationType !=='ADD') {
        return true;
      }  
      return false;

  },[selectedRowData?.rowIndex]);
   
  // 渲染操作表格按钮
  const renderOperation = () => {
    return <div style={{marginBottom: '16px'}}>
      <Button 
        size="small" 
        onClick={add} 
        className="mr8" 
        disabled={(['HANA'].includes(connectionType) && tableKey === 'indexes') ||
        (['GBase'].includes(connectionType) && ['triggers', 'indexes'].includes(tableKey))
        }
      >
        {t('sdo_add_1')}
      </Button>
      <Button 
        size="small"  
        onClick={del} 
        className="mr8"
        disabled={delBtnDisableStatus}
      >
        {t('sdo_del')}
      </Button>
    </div>
  }

  const handleSqlChange = (e: any) => {
    const value = e.target.value?.trim()
    setPreviewSql(value)
  }

  const handlePreviewSubmit = () => {
    if (previewSql) {
      onSubmit && onSubmit(previewSql)
      setPreviewModalVisible(false)
    }
  }

  const handlePreviewCancle = () => {
    setPreviewModalVisible(false)
  }

  return (
    <Spin spinning={getOriginDataLoading}>
      {!disabledEdit && renderOperation()}
      <div
        id="myGrid"
        style={{
          height: `calc(60vh - 40px)`,
          width: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
        className={
          theme === 'dark' ? 'ag-theme-balham-dark' : 'ag-theme-balham'
        }  
      >
        <div style={{flex: 1}}>
          <AgGridReact
            {...gridConfig}
            modules={[ClientSideRowModelModule]}
            columnDefs={getBaseColumn(tableKey, connectionType)?.map(i => ({...i, editable:!disabledEdit }))}
            defaultColDef={{editable: true, resizable: true}}
            animateRows={true}
            onGridReady={onGridReady}
            rowData={rowData}
            rowSelection="single"
            onCellEditingStopped={updateRowData}
            rowClassRules={{
                'ag-disabled-edit': function (params: any) {
                //HANA 索引禁止编辑
                if (connectionType === 'HANA' && ['indexes'].includes(tableKey)) {
                    return true;
                }
                //gbase 索引和触发器禁止已有数据编辑
                if(connectionType === 'GBase' && ['indexes','triggers'].includes(tableKey)) {
                    return true;
                }
                //gbase8a 索引禁止已有数据编辑, 新增数据可正常操作
                if(connectionType === 'GBase8a' && ['indexes'].includes(tableKey) && params.node.data?.operationType !== 'ADD') {
                    return true;
                }
                //Inceptor Phoenix 字段禁止已有数据编辑, 新增数据可正常操作
                if( ['Phoenix'].includes(connectionType) && ['columns'].includes(tableKey) && params.node.data?.operationType !== 'ADD') {
                    return true;
                }
                //禁止StarRocks已有数据编辑
                let result = ['StarRocks'].includes(connectionType) && ['indexes'].includes(tableKey) && params.node.data?.operationType !== 'ADD';
                return result;
            }}}
            
            onRowClicked={(event) => {
                if (event.rowIndex != null) {
                    //置灰数据无法focus所以增加selectedRowData
                    setSelectedRowData({rowIndex: event.rowIndex,data: event?.data || {}})
                }
                
            }}
            onCellFocused={(event) => {
            
                if (event.rowIndex === focusRowData?.rowIndex) return;
                if (event.rowIndex != null) {
                const rowNode = event.api.getDisplayedRowAtIndex(
                    event.rowIndex,
                )
                const { rowIndex=0, data ={}} = rowNode as any
                setFocusRowData({rowIndex,data})
                }
                
            }}
            stopEditingWhenCellsLoseFocus
            // rowDataChangeDetectionStrategy 在 ag-grid 29 版本中已弃用，现在总是使用 IdentityCheck
          />
        </div>
        {
          tableKey === 'triggers' && 
          <TriggerExtraContent 
            disabledEdit={disabledEdit}
            connectionId={connectionId}
            databaseName={databaseName}
            schemaName={schemaName}
            connectionType={connectionType}
            focusRowData={focusRowData}
            onResetTriggerBody={onResetTriggerBody}
          />
        }
      </div>
      {/* 设计表模式下 保存按钮 */}
      {
          modeType === 'designTable' && <Button
              type='primary'
              style={{ position: 'fixed', bottom: 66, right: saveBtnDistanceToRight }}
              onClick={handleSave}
          >
              {t('sdo_save')}
          </Button>
      }
      {
        previewModalVisible &&
        <Modal 
          width={532}
          title={t('sdo_sql_preview')}
          okText={t('sdo_submit')}
          visible={true}
          onOk={handlePreviewSubmit}
          onCancel={handlePreviewCancle}
          okButtonProps={{disabled: !previewSql}}
          maskClosable={false}
        >
          <Input.TextArea
            placeholder={t('sdo_sql_preview_placeholder')}
            autoSize={{ minRows: 6, maxRows: 28 }}
            value={previewSql}
            onChange={handleSqlChange}
          />
        </Modal>
      } 
    </Spin>
  )
}
