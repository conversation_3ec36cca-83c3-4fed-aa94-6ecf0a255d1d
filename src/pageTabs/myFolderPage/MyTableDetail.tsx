import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { checkFileExists, deleteFiles, UserFile } from 'src/api'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { Menu, message, Table, Tooltip } from 'antd'
import { clickFile, setClickTreeOrlist, setIsNotClickFile, setPopupVisible, setPwd, setTreeTag } from './myFolderSlice'
import { Iconfont } from 'src/components'
import { handleDownload, showConfirmModal } from 'src/util'
import { ColumnsType } from 'antd/lib/table'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import copy from 'copy-to-clipboard'
import { SpaceType } from './constants';
import styles from './myTableDetail.module.scss'


interface MyTableDetailProps {
  files: any
  refresh: () => Promise<any>
  curSpaceType: SpaceType
}

export const MyTableDetailPage: React.FC<MyTableDetailProps> = (props) => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { files, refresh, curSpaceType } = props
  const { treeTag, pwd, PopupVisible, isNotClickFile } = useSelector((state) => state.myFolder)
  const [col, setCol] = useState<any>([])
  const [rowInfo, setRowInfo] = useState<any>({})
  const [location, setLocation] = useState<any>({
    x: 0,
    y: 0
  })

  const newArr = files?.map((item: any) => {
    delete item.children
    return item
  })



  const columns: ColumnsType<any> = [
    {
      title: t('personal:nickName'),
      dataIndex: 'connectionName',
      ellipsis: true,
      width: 300,
      render: (connectionName, _record, index) => (
        <Tooltip title={<span style={{wordBreak: 'break-all'}}>{connectionName}</span>}>
          <Iconfont type={_record?.isDir ? 'icon-wenjianjia' : 'icon-wenjian'} style={{ width: '20px' }} /> {connectionName}
        </Tooltip>
      )
    },
    {
      title: t('personal:updateTime'),
      dataIndex: 'updateTime',
      width: 200
    },
    {
      ...(curSpaceType === 'share' ? 
      {
        title: t('personal:creator'),
        dataIndex: 'userId',
        width: 200
      }
      : {}
      )
    }
    ,
    {
      title: t('personal:type'),
      dataIndex: 'fileType',
      width: 100
    },
    {
      title: t('personal:size'),
      dataIndex: 'size',
      width: 100
    },
  ]

 

  const newColumns = useMemo(() => {
    const aa = pwd?.path?.split('/').includes('systemSpace')
    if (aa) {
      columns.push(
        {
          title: t('creator'),
          dataIndex: 'userId',
          width: 160
        }
      )
    }
    return columns
  }, [pwd?.path, curSpaceType])

  useEffect(() => {
    setCol(newColumns)
  },[newColumns, curSpaceType])

  const { run: tryDeleteFiles } = useRequest(deleteFiles, {
    manual: true,
    onSuccess: () => {
      message.success(t('common.message.delete_success'))
      refresh()
      dispatch(setPwd(pwd?.path))
    }
  })

  const tryDownloadFile = useCallback((path: string) => {
    checkFileExists(path).then(() => {
      handleDownload({ href: `/export/download?path=${path}` })
    }).catch((err: any) => {console.error(err)})
  }, [])

  const onRowFun = (record: any) => {
    return {
      onClick: (event: any) => handleClick(event, record),
      onDoubleClick: (event: any) => handleDoubleClick(event, record),
      onContextMenu: (event: any) => handleContextMenu(event, record)
    }
  }

  const handleClick = (event: any, record: UserFile) => {

    const { path } = record
    dispatch(clickFile(path))
    dispatch(setTreeTag(path))
    dispatch(setIsNotClickFile(false))
    event.stopPropagation()
  }

  const handleDoubleClick = (event: any, record: UserFile) => {
    const { type, path } = record
    if (type === 'file') {
      tryDownloadFile(path)
      return
    }
    if (type === 'directory') {
      dispatch(setPwd(path))
      refresh()
      return
    }
  }

  const handleContextMenu = (event: any, record: UserFile) => {
    event.preventDefault()
    const { type, path } = record
    setRowInfo(record)
    dispatch(setTreeTag(path))
    dispatch(setIsNotClickFile(false))
    document.addEventListener(`click`, function onClickOutside() {
      dispatch(setPopupVisible(false))
      document.removeEventListener(`click`, onClickOutside)
    })
    dispatch(setPopupVisible(true))
    setLocation({
      x: event?.clientX,
      y: event?.clientY
    })
  }

  const handleDeleteConfirmModal = (path: string[]) => {
    showConfirmModal({
      onOk: () => {
        tryDeleteFiles(path)
      }
    });
  }

  const Popup = () => {
    const { isSystemSpaceDirChild, path, type, isDir, canDelete = false, canAlter = false } = rowInfo
  
    let MenuItem = []
    if (isSystemSpaceDirChild) {
      MenuItem = [<Menu.Item key="copyPath" onClick={() => {
        copy(path)
        dispatch(setIsNotClickFile(true))  // 选择复制选项，对应的文件夹取消选中状态
        message.success(t('personal:copySuccess'))
      }}>{t('personal:copyFileAddress')}</Menu.Item>]
    } else {
      MenuItem = [
        <Menu.Item 
          key="dele" 
          onClick={() => { handleDeleteConfirmModal([path]) }}
          //分享空间仅允许创建人自己删除
          disabled={curSpaceType === 'share' && !canDelete}
        >
          {t('common.btn.delete')}
        </Menu.Item>,
        <Menu.Item 
          key="rename" 
          className="chooseFile" 
          //分享空间仅允许创建人自己编辑
          disabled={curSpaceType === 'share' && !canAlter}
          onClick={() => {
            dispatch(showModal('ReNameModal'))
            dispatch(setClickTreeOrlist('list'))
            dispatch(setIsNotClickFile(false))
          }}
        >
          {t('personal:rename')}
        </Menu.Item>,
        <Menu.Item 
          key="move" 
          className="chooseFile" 
          //分享空间仅允许创建人自己编辑
          disabled={curSpaceType === 'share' && !canAlter}
          onClick={() => {
            dispatch(showModal('MoveModal'))
            dispatch(setIsNotClickFile(false))
          }}
        >
          {t('personal:move')}
        </Menu.Item>,
        <Menu.Item key="copyPath" onClick={() => {
          copy(path)
          dispatch(setIsNotClickFile(true))  // 选择复制选项，对应的文件夹取消选中状态
          message.success('复制成功')
        }}>{t('personal:copyFileAddress')}</Menu.Item>
      ]
      if (type !== 'directory' && !isDir) {
        MenuItem.unshift(<Menu.Item key="download" className="chooseFile" onClick={() => {
          tryDownloadFile(path)
          dispatch(setIsNotClickFile(false))
        }}>{t('common.btn.download')}</Menu.Item>)
      } else {
        MenuItem.push(<Menu.Item key="newFolder" className="chooseFile" onClick={() => {
          dispatch(showModal('ModalNewFolder'))
          dispatch(setIsNotClickFile(false))
        }}>{t('personal:newFolder')}</Menu.Item>)
      }
    }
    return <Menu className={styles.popup} style={{ left: `${location?.x}px`, top: `${location?.y}px` }}>
      {
        MenuItem
      }
    </Menu>
  }

  return (
    <div className={styles.myTableWrap}>
      <Table
        className={styles.myTable}
        dataSource={newArr}
        columns={col}
        rowClassName={(re) => {
          return (!isNotClickFile && (treeTag === re?.path)) ? styles.bgcolor : ''
        }}
        size='small'
        pagination={false}
        onRow={onRowFun}
        scroll={{ y: `calc(100vh - 300px)` }}
      />
      {
        PopupVisible && <Popup />
      }
    </div>
  )
}
