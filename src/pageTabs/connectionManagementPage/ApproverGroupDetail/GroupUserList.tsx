
import React, { useEffect, useState, useMemo } from "react";
import { useLocation } from 'react-router-dom';
import { useTranslation } from "react-i18next";
import { Button, Popconfirm, Table, Tag, Tooltip, message } from 'antd';
import Service from 'src/service';
import { useRequest } from 'src/hook';
import {
  UserEntity,
  unbindApproverGroupUser,
  IApproverGroupItem,
  getApproverGroupUserList,
  IApproverGroupUserParams
} from 'src/api';
import { Iconfont } from 'src/components';
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import styles from './index.module.scss';
const GroupUserList = ({
  isRefresh,
  disbaled,
  roles,
  selectedApproverGroupNode,
}: {
  isRefresh: boolean;
  disbaled: boolean;
  roles: string;
  selectedApproverGroupNode: IApproverGroupItem;
}) => {

  const defaultTableParams = { pageSize: 10, pageNum: 1 };
  const location = useLocation();
  const { t } = useTranslation();
  const { state = {} } = location as { state: GloablSearchLocationState }

  const [tableParams, setTableParams] = useState<IApproverGroupUserParams>();

  const { data: userDataSource, run, loading: dataLoading } = useRequest((params) => params?.groupId && getApproverGroupUserList(params), { manual: true })
  //解绑 最后一个无法解绑
  const { run: runUnbindApproverGroupUser, loading: unbindLoading } = useRequest(unbindApproverGroupUser, {
    manual: true,
    onSuccess: () => {
      message.success(t('common.message.removeSuccess'));
      setTableParams({ ...defaultTableParams, groupId: selectedApproverGroupNode?.id });
    }
  });

  useEffect(() => {
    if (selectedApproverGroupNode?.id) {
      setTableParams({ ...defaultTableParams, groupId: selectedApproverGroupNode?.id })
    }
  }, [selectedApproverGroupNode?.id])

  //全局搜索
  useEffect(() => {

    if (state?.globalSearchRecordPosition && tableParams?.pageNum && tableParams?.pageSize) {
      const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / tableParams.pageSize);
      if (tableParams.pageNum !== pageNum) {
        setTableParams({
          ...tableParams,
          pageNum: pageNum
        })
      }
    }
  }, [state?.globalSearchRecordPosition, tableParams?.pageNum, tableParams?.pageSize])

  useEffect(() => {

    if (tableParams) {
      run(tableParams)
    }
  }, [tableParams, isRefresh])

  const isSelectedRowIndex = useMemo(() => {

    if (!state?.globalSearchRecordPosition || !tableParams?.pageNum || !tableParams?.pageSize) {
      return null;
    }

    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / tableParams.pageSize);
    //@ts-ignore
    if (pageNum === tableParams.pageNum && selectedApproverGroupNode?.id === Number(state?.object?.groupId)) {
      const itemIndexInPage = state.globalSearchRecordPosition % tableParams.pageSize;
      return itemIndexInPage === 0 ? tableParams.pageSize - 1 : itemIndexInPage - 1;
    }
  },[tableParams?.pageNum,tableParams?.pageSize, state?.globalSearchRecordPosition, selectedApproverGroupNode?.id])

  const renderAuditRange = (_: any, record: UserEntity) => {
    // 角色里有审计员，展示审计范围，审计员id为2
    const { auditUser, rolesId } = record || {}
    let auditInfo = '';
    if (auditUser) {
      auditInfo = Object.keys(auditUser).map(item => {
        return `${auditUser[item]}(${item})`
      }).join('、')
    }
    if (rolesId?.includes(2)) {
      if (auditInfo?.length > 30) {
        return (
          <Tooltip title={auditInfo}>
            <div style={{ wordWrap: 'break-word' }}>
              {auditInfo.slice(0, 30)}...
            </div>
          </Tooltip>
        )
      } else if (auditInfo?.length > 0) {
        return auditInfo
      } else {
        return t('systemManagement.personManagement.alluUsers')
      }
    } else {
      return '-'
    }
  }

  const columns: any = [
    {
      title: '',
      render: (val: string, record: UserEntity) => {
        return (
          <span>
            {record?.onlineStatus ?
              <span style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#1AD42D', display: "inline-block" }}></span>
              : <span style={{ width: '8px', height: '8px', borderRadius: '50%', background: 'gray', display: "inline-block" }}></span>
            }
          </span>
        )
      },
      width: 50
    },
    {
      title: t('db.connection.conn.userName'),
      dataIndex: 'userName',
      width: 100,
      render: (val: string, record: UserEntity) => {
        const { systemAuthCode, userName } = record
        const isAdmin = Service.permService.checkAuthWithCode(
          'SystemSettings',
          systemAuthCode,
        )

        if (!isAdmin) return userName
        return (
          <span>
            <span>{userName}</span>
            <Tag className={styles.tag} color="geekblue">
              Admin
            </Tag>
          </span>
        )
      },
      ellipsis: true,
    },
    {
      title: t('systemManagement.personManagement.table.column.userId'),
      dataIndex: 'userId',
      width: 150,
    },
    {
      title: t('systemManagement.personManagement.table.column.departmentName'),
      dataIndex: 'departmentName',
      width: 100,
      render: (val: string, record: UserEntity) => {
        return (
          <span>
            {record?.principalFlag ? <Iconfont type='icon-departmentHead' /> : null}
            {record?.departmentName}
          </span>
        )
      }
    },
    {
      title:  t('systemManagement.personManagement.table.column.systemRoles'),
      dataIndex: 'systemRoles',
      render: (_: any, record: UserEntity) => {
        const { systemRoles } = record
        const roleString = systemRoles?.join(', ')
        return (
          <Tooltip title={roleString}>
            <span>{roleString}</span>
          </Tooltip>
        )
      },
    },
    {
      title: t('systemManagement.personManagement.table.column.telephone'),
      dataIndex: 'telephone',
      width: 100
    },
    {
      title: t('systemManagement.personManagement.table.column.createFrom'),
      dataIndex: 'createFrom',
      ellipsis: true,
      render: (val: string) => {
        let text
        switch (val) {
          case null:
            text = t('systemManagement.personManagement.createFrom.cqUser')
            break
          case 'adLogin':
            text = t('systemManagement.personManagement.createFrom.ad')
            break
          case 'casLogin':
            text = 'CAS'
            break
          case 'openLdapLogin':
            text = 'OpenLdap'
            break
          case 'OauthLogin':
            text = 'Oauth2.0'
            break
        }
        return text
      },
    },
    {
      title: t('systemManagement.personManagement.table.column.auditRange'),
      dataIndex: 'auditRange',
      width: '200px',
      render: renderAuditRange,
    },
    {
      title: t('systemManagement.personManagement.table.column.userStatus'),
      dataIndex: 'userStatus',
      fixed: 'right',
      render: (val: string) => val === 'NORMAL' ? t('common.btn.normal') : t('common.btn.lock'),
      width: '90px',
    },
    {
      title: t('common.text.action'),
      dataIndex: 'action',
      fixed: 'right',
      render: (val: string, record: any) => (
        <Popconfirm disabled={disbaled} title={t('db.connection.approver.removeUser.tip')} onConfirm={() => {
          runUnbindApproverGroupUser({
            groupId: selectedApproverGroupNode.id,
            userIds: [record.userId]
          })
        }}>
          <Tooltip placement="topRight" title={disbaled ? t('db.connection.noPerm', {roleNameList: roles}) : null}>
            <Button disabled={disbaled} type="link" loading={unbindLoading}>{t('common.btn.remove')}</Button>
          </Tooltip>
        </Popconfirm>
      ),
      width: '90px',
    }
  ]

  return (
    <div>
      <Table
        loading={unbindLoading || dataLoading}
        rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected': ''}
        dataSource={userDataSource?.content || []}
        columns={columns}
        pagination={{
          current: tableParams?.pageNum || 1,
          pageSize: tableParams?.pageSize || 10,
          total: userDataSource?.totalElements || 0,
          showTotal: () => t('common.table.pagination.total', {total: userDataSource?.totalElements || 0}),
          showSizeChanger: true,
          showQuickJumper: true,
          onChange: (page, pageSize) => {
            setTableParams({ ...tableParams, pageNum: page, pageSize: pageSize || 10 } as IApproverGroupUserParams)
          },
        }}
        scroll={{ x: 'max-content', y: `calc(100vh - 370px)` }}
      />
    </div>
  )
}

export default GroupUserList;