@import 'src/styles/variables';

.ml10 {
	margin-left: 10px;
}
.ml12 {
	margin-left: 12px;
}
.ml32 {
	margin-left: 32px;
}
.mr4 {
	margin-right: 4px;
}
.mr10 {
	margin-right: 10px;
}
.mt10 {
	margin-top: 10px;
}
.mt20 {
	margin-top: 20px;
}
.mb10 {
	margin-bottom: 10px;
}
.mb20 {
	margin-bottom: 20px;
}
.pd20 {
	padding: 20px;
}
.ptb10 {
	padding: 10px 0;
}
.flex1 {
	flex: 1
}
.flex {
	display: flex;
	align-items: center;
}
.flexRight {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
.color2B58BA{
	color: #2B58BA;
}
.color008dff {
	color: #3357ff;
}
.colorf00 {
	color: #f00;
}
.colorgreen {
	color: green;
}
.colorb8b8b8 {
  color: #b8b8b8 !important;
}
.bgcolorfff {
	background-color: #fff;
}
.bgcolord38787 {
	background-color: #d38787;
}
.bgcolor3357FF {
	background-color: #3357FF;
}
.bgcolor23B899 {
	background-color: #23B899;
}
.bgcolorebefff {
	background-color: #ebefff;
}
.fs18 {
	font-size: 18px;
}
.fs30 {
	font-size: 30px;
}
.circle48 {
	color: #fff;
	width: 48px;
	height: 48px;
	border-radius: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.circle64 {
	color: #fff;
	width: 64px;
	height: 64px;
	border-radius: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.options {
	cursor: pointer;
	color: var(--primary-color);
	:global {
		.ant-dropdown-menu-item {
			color:var(--primary-color);
		}
	}
}
.padding0 {
  padding: 0 !important;
}
.optionTxt {
	cursor: pointer;
	&:hover {
		color: var(--primary-color);
	}
	:global {
		.ant-dropdown-menu-item {
			&:hover {
				color: var(--primary-color);
			}
		}
	}
}
.optionLine {
	display: flex;
	align-items: center;
}
.returnLine {
	display: flex;
	align-items: center;
	margin: 20px 0;
	.backIcon {
		margin-right: 10px;
		margin-top: 0;
	}
}
.resizableBox {
	height: 100% !important;
}
.resizeHandle {
	position: absolute;
	right: -3px;
	top: calc(50% - 24px);
	font-size: 16px;
	cursor: col-resize;
	color: rgba(0,0,0, 0.85);
}
.searchBtn {
	width: 100%;
	:global {
		.ant-input-search-icon {
			margin-left: 4px;
			padding: 0 9px 0 0;
		}
		.ant-input-clear-icon {
			padding: 4px;
			color: rgba(0,0,0,0.52);
		}
	}
}
.checkAllBox {
	display: block;
	margin: 10px 0;
	width: 60px;
}
.checkBoxGroup {
	width: 100%;
	max-height: 300px;
	overflow-y: auto;
}
.connectionManagementPageWrap {
	height: calc(100vh - 30px - 48px);
	padding: 0 10px 10px;
	.breadcrumbLine {
		padding: 0 10px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.contentWrap {
		font-size: 14px;
		min-height: 300px;
		display: flex;
		.leftWrap,
		.rightWrap {
			border-radius: 4px;
			height: calc(100vh - 140px);
			background-color: #F7F9FC;
		}
		.leftWrap {
			padding: 16px 14px;
			margin-right: 10px;
			.addBtn {
				width: 100%;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.treeWrap {
				background-color: unset;
				:global {
					.ant-tree-treenode {
						width: 100%;
						padding: 0;
						border-bottom: 4px solid #F7F9FC;
						background-color: #fff;
						height: 36px;
						align-items: center;
						border-radius: 4px;
						&:last-child {
							height: 32px;
							border-bottom: 0;
						}
					}
					.ant-tree-node-content-wrapper {
						flex: 1;
					}

					.ant-tree-node-content-wrapper:hover,
					.ant-tree-treenode:hover {
						background-color: #d6e5ff;
					}
					.ant-tree-node-content-wrapper {
						transition: none;
					}
					.ant-tree-treenode-selected {
						background-color: #d6e5ff;
					}
					.ant-tree-treenode-selected .ant-tree-switcher,
					.ant-tree-treenode
						.ant-tree-node-content-wrapper.ant-tree-node-selected {
						color: initial;
						background-color: #d6e5ff;
					}
				}
			}
			.treeTitleItem {
				display: flex;
				align-items: center;
				.titleTxtWrap {
          display: flex;
					flex: 1;
					text-overflow: ellipsis;
          white-space: nowrap;
					overflow: hidden;
          .titleTxt {
            overflow: hidden;
            text-overflow: ellipsis;
          }
				}
			}
		}
		.rightWrap {
			padding: 16px;
			flex: 1;
			.contentStyle {
				padding: 0 20px 20px;
				background-color: #fff;
				height: calc(100vh - 172px);
				overflow-y: auto;
        .contentTabs {
          :global {
            .ant-tabs-nav::before {
              display: none !important;
            }

            .ant-tabs-tab {
              border-radius: 4px;
            }
        
            .ant-tabs-tab-active {
              background-color: #165dfe !important;
              border-radius: 4px !important;
            }

            .ant-tabs-tab.ant-tabs-tab-active > .ant-tabs-tab-btn {
              color: #ffffff;
              background-color: #165dfe;
            }
        
            .ant-tabs-tab:hover {
              color: #ffffff !important;
              background-color: #165dfe;
            }

            .ant-tabs-tab-btn:hover {
              color: #ffffff !important;
            }
        
            .ant-tabs-tab-btn:active {
              color: #ffffff !important;
              background-color: #165dfe;
            }
          }
        }
			}
			.headerLine {
        display: flex;
        align-items: center;
        justify-content: space-between;
      
        .namePart {
          padding: 20px 0;
          line-height: 32px;
          margin-right: 50px;
          display: flex;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
        }
      
        .desc {
          display: flex;
          align-items: center;
          color: #666;
        }
      }
		}
	} 
	:global{
		.ant-tabs-tab .anticon {
			margin-right: 4px;
		}
	}
	.addConnectionBtn {
		color: #3262FF;
		line-height: 32px;
		cursor: pointer;
		position: relative;
	}
}

.createConnectionOverlayStyle {
	:global {
		.ant-dropdown-menu {
			max-height: 600px;
			overflow-y: auto;
			&::-webkit-scrollbar {
				width: 6px;
			}
			&::-webkit-scrollbar-thumb {
				border-radius: 8px;
				background-color: #888;
			}
		}
	}
}

.backIcon {
	display: block;
	margin-top: 20px;
	cursor: pointer;
	font-size: 18px;
	text-align: left;
	width: 18px;
	&:hover {
		color: #3357ff;
	}
}

.connectionTabsWrap {
	.topLine {
		display: flex;
		align-items: center;
		padding: 20px 0;
		.titleIcon {
			color: #2B58BA;
			width: 48px;
			height: 48px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8px;
			background-color: #EBEFFF;
			font-size: 30px;
		}
		.info {
			.parentName{
				margin-bottom: 4px;
				font-size: 18px;
				font-weight: bold;
				.version, .isArchiveResMark {
					margin-left: 6px;
					font-size: 14px;
					color: #666;
					background-color: #e5e5e5;
					padding: 2px 6px;
					border-radius: 4px;
				}
			}
			.selfName {
				font-size: 16px;
				.remark {
					font-size: 14px;
					color: #999;
				}
			}
		}
	}
  .connectionTabs {
    :global {
      .ant-tabs-nav-wrap {
        z-index: 800;
      }
    }
  }
}
.connectionOverviewTabs {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	.cardWrap {
		margin-right: 10px;
		&:last-child {
			margin-right: 0px;
		}
		width: 32%;
		min-width: 324px;
		.header{
			display: flex;
			align-items: center;
			padding-bottom: 20px;
			margin-bottom: 20px;
			border-bottom: 1px solid #e5e5e5;
		}
		.title {
			font-size: 16px;
			font-weight: bold;
		}
		.desc {
			font-size: 16px;
		}
		.nums {
			font-size: 24px;
		}
		.col3357ff {
			color: #3357ff;
		}
		.items {
			padding-right: 20px;
			margin-bottom: 10px;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
	}
	.noCursor {
		&:hover {
			cursor: auto;
		}
	}
}
.addOrEditConnectionWrap {
	.formWrap{
		margin-top: 30px;
	}
}

.connectSettingWrap {
	height: calc(100vh - 366px);
	overflow-y: auto;
	color: #0F244C;
	.settingTitle {
		margin-bottom: 10px;
		background: #F7F9FC;
		border-radius: 4px;
		padding: 6px 12px;
		color: #667084;
	}
}

.connectionManageWrap {
	padding: 20px;
}
.explainWrap {
	.title {
		color: #333;
		font-weight: bold;
		word-break: break-all;
	}
}
.selectedItem {
	td {
		background-color: #E2E9F5 !important;
	}
	&:hover {
		:global {
			td {
				background-color: #E2E9F5 !important;
			}
		}
	}
}
.loading {
	display: block;
	padding: 80px;
}
.connnectionList {
  :global {
    .ant-btn {
      padding: 0;
    }
    .ResizeTable_tableHeader__iDZn- {
      cursor: pointer;
    }
    .ant-table-thead > tr > th, .ant-table-small .ant-table-thead > tr > th {
      background-color: #ffffff;
    }
  }
  .connectionNameWrap{
    display: flex;
    overflow: hidden;
    .connectionNameText {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.createOrEditConnectionWrap {
  .createOrEditConnectionForm {
    height: 500px;
    overflow-y: scroll;
  }
  :global {
    .ant-collapse-borderless .ant-collapse-item {
      border-width: 0px;
    }
    .ant-collapse-content {
      background-color: white !important;
    }
  }
}

.mask {
  position: fixed; /* 使用fixed定位可以让蒙版覆盖整个页面 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
  z-index: 800; /* 确保蒙版位于其他内容之上 */
}
.guidePopconfirm {
  :global {
    .ant-popover-inner-content {
      width: 259px;
    }
  }
}
.guideMark {
  z-index: 2;
  :global {
    .ant-modal-mask {
      background-color: rgba(0, 0, 0, 0.55);
    }
    .ant-modal-body {
      padding: 20px;
      padding-bottom: 0px;
    }
    .ant-modal-footer {
      border-width: 0px;
    }
  }
}

.zIndex999 {
  position: relative;
  z-index: 1200 !important;
  background-color: white;
  padding: 6px;
}

.colorRed {
  color: red !important;
}

.confirmIcon {
  font-size: medium;
  cursor: pointer;
  position: absolute;
  top: 12px;
  right: 16px;
  border: none;
  background-color: white;
}

.handleRemoveConfirm {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	font-size: 16px;
	color: #22223f;

	.clearSwitch {
		margin-left: 6px;
	}
}

.hostConnectionSettingWrap {
  margin: 12px;
  font-size: 15px;
  .content {
    display: inline-grid;
    padding-left: 20px;
    .keyAuthTip {
      font-size: 13px;
      color: #686868;
      .btn {
        margin-left: 6px;
      }
    }
    .pwdTxt {
      margin-top: 20px;
    }
  }
}