import React from 'react'
import { useTranslation } from 'react-i18next'
import { Form, Select, Input, Checkbox, Radio, Tooltip, Space } from 'antd'
import { WizardItem } from 'src/api'
import { useSelector } from 'src/hook'
import { FormItemProps } from 'antd/lib/form'
import { FormTailLayoutTwo } from 'src/constants'
import { looseNameValidator } from 'src/util'
import { ConnectionMembersTable } from 'src/features/wizards/wizardFormItem/ConnectionMembersTable'
import { QuestionCircleOutlined } from '@ant-design/icons'
import classnames from 'classnames'

interface IWizardFormProps {
  spec: Partial<WizardItem>
  labelWithTooltip?: boolean
  operationType?: any
}

const GenerateFormItem = ({
  spec,
  labelWithTooltip,
  operationType,
}: IWizardFormProps) => {

  const { t } = useTranslation()
  const isLangEn = useSelector((state) => state.login.locales) === 'en'

  const { type, label, field, required, options, suffix, value, hide, disabled, onlyRead } = spec
  const labelWithTips = (
    <Space size="small">
      {label}
      {suffix && (
        <Tooltip title={suffix}>
          <QuestionCircleOutlined />
        </Tooltip>
      )}
    </Space>
  )
  const formProps: FormItemProps = {
    label: labelWithTooltip ? labelWithTips : label,
    name: field,
    initialValue: value,
    rules: [{ required, message: t('db.connection.conn.formItem.required', {label})}],
  }

  const getFormItemContent = () => {
    switch (type) {
      case 'checkbox':
        formProps.valuePropName = 'checked'
        return <Checkbox disabled={disabled}>{suffix}</Checkbox>
      case 'select':
        const SelectOptions = options?.map(({ key, title }: any) => ({
          label: title,
          value: key,
        }))
        return (
          <Select
            disabled={disabled}
            placeholder={t('db.connection.conn.formItem.plac', {label})}
            showSearch
            optionFilterProp="label"
            options={SelectOptions}
            allowClear
          ></Select>
        )
      case 'radio':
        return (
          <Radio.Group disabled={disabled}>
            {options?.map(({ key, title }: { key: string; title: string }) => (
              <Radio key={key} value={key}>
                {title}
              </Radio>
            ))}
          </Radio.Group>
        )
      case 'password':
        return (
          <Input.Password
            disabled={disabled}
            visibilityToggle={false}
            placeholder={t('common.formItem.input.plac', {label})}
            autoComplete="off"
          ></Input.Password>
        )
      case 'tag':
        return (
          <Select placeholder={t('db.connection.conn.formItem.plac2', {label})} mode="tags" disabled={disabled}>
            {options?.map(({ key, title }: any) => (
              <Select.Option key={key} value={key}>
                {title}
              </Select.Option>
            ))}
          </Select>
        )
      case 'textarea':
        return <Input.TextArea placeholder={t('common.formItem.input.plac', {label})} disabled={disabled}></Input.TextArea>
      case 'table':
        // todo: 响应值格式统一之后去掉
        if (!(value instanceof Array)) {
          formProps.initialValue = []
        }
        return <ConnectionMembersTable />
      // connectionUrlView是自动生成的，所以不需要这个输入文案提示
      default:
        return <Input
          autoComplete="off" 
          placeholder={field === 'connectionUrlView' ? '' : t('common.formItem.input.plac', {label})} 
          disabled={disabled} 
          readOnly={onlyRead}
          className={classnames({'customReadOnlyInput': onlyRead})}
        />
    }
  }

  const Content = getFormItemContent()
  const style = hide ? { display: 'none' } : {}

  // ! anti pattern. 暂时处理，需要和后端约定配置化表单的 schema
  if (field === 'connectionName') {
    formProps.rules?.push({ validator: looseNameValidator })
  }

  // 自动回滚时间不能大于1200,小于60且只能是正整数
  const validateImplicitCommitTimeout = (_rule: any, value: any) => {
    const reg = /^[1-9]\d*$/
    if (!value) {
      return Promise.reject('') // 上面有共用的提示,不用重复处理
    }
    if (!reg.test(value)) {
      return Promise.reject(t('db.connection.conn.implicitCommitTimeout.hint')) // 正整数校验
    }
    if (value > 86400 || value < 60 || value % 1 !== 0) {
      return Promise.reject(t('db.connection.conn.implicitCommitTimeout.hint2'))
    }
    return Promise.resolve()
  }

  //校验不能输入空格
  const validateNoIncludesSpace = (_rule: any, value: any) => {
    const reg = /^\S*$/;
    if (!reg.test(value)) {
      return Promise.reject(t('db.connection.conn.space.hint'))
    }
    return Promise.resolve()
  }
  // 自动回滚添加校验
  if(field === 'implicitCommitTimeout') {
    formProps.rules?.push({ validator: validateImplicitCommitTimeout })
  }

  if(field && ['connectionUrl', 'userName', 'password'].includes(field)) {
    formProps.rules?.push({ validator: validateNoIncludesSpace })
  }

  const layoutTmp = { 
    labelCol: { span: isLangEn ? 8 : 5 },
    wrapperCol: { span: isLangEn ? 7 : 7 }
  }
  const formLayout = operationType === "connectionConfig" ? {...layoutTmp}  : 
                     operationType === "connectionInfo" ? {...FormTailLayoutTwo, labelCol: { span: isLangEn ? 10 : 6 } }: {}

  return label ? (
    (!!operationType && ["connectionConfig", "connectionInfo"].includes(operationType)) ? (
      <Form.Item {...formProps} key={String(formProps.name)} {...formLayout} style={style}>
        {Content}
      </Form.Item>
    ) : (
      <Form.Item {...formProps} key={String(formProps.name)} style={style}>
        {Content}
      </Form.Item>
    )
  ) : (
    <Form.Item
      {...formProps}
      key={String(formProps.name)}
      {...formLayout}
      style={style}
    >
      {Content}
    </Form.Item>
  )
}
export default GenerateFormItem