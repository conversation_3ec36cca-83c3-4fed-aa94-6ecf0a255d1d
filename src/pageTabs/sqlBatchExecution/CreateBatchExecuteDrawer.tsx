/**
 * 新建批量执行任务
 */
import React, { useEffect, useState } from 'react'
import { But<PERSON>, Divider, Drawer, Form, Radio, Input, message, Spin, Space, Modal, Tooltip, Row, Col } from 'antd'
import { UploadOutlined, PlusCircleOutlined, PaperClipOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import UploadFolderModal from './UploadFolderModal'
import { CustomDragableUploadList } from 'src/components'
import { useDispatch } from 'src/hook'
import { getErrorHandleWayEnums, getExecuteWayEnums } from './constant'
import { parseBatchSqlFile, createBatchSql, deleteSqlFile, getUserSysFile, checkBatchSqlCron, downloadSqlFile, getBatchSqlPreviewFile } from 'src/api'
import styles from './index.module.scss'
import { debounce } from 'lodash'
import { BatchExecuteStep2 } from './BatchExecuteStep2/index';
import { ElementTreeSelect } from './ElementTreeSelect'
import { useHistory } from 'react-router-dom'
import { getCronExpressionTooltipTitle } from 'src/constants'
import useRequest from '@ahooksjs/use-request'
import CronGenerator from "src/components/CronGenerator";
import { useTranslation } from 'react-i18next';
import CustomUploadFilesRender from 'src/components/CustomUploadFilesRender'
import { setTabsKey } from 'src/pageTabs/taskCenter/taskCenterSlice'

interface IProps {
  visible: boolean
  onClose: () => void
  [p: string]: any
}

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const CreateBatchExecuteDrawer = ({
  visible,
  onClose,
  refreshData
}: IProps) => {
  const [form] = Form.useForm();
  const history = useHistory();
  const dispatch = useDispatch();
  const [uploadFolderModalVisible, setUploadFolderModalVisible] = useState(false)
  const [folderName, setFolderName] = useState<string>('')
  const [step, setStep] = useState<number>(1)
  const [parseResult, setParseResult] = useState<any>({})
  const [mergedScriptInfos, setMergedScriptInfos] = useState<any[]>([])  // 渲染使用
  const [loading, setLoading] = useState(false)
  const [uploadSize, setUploadSize] = useState<number>(0)
  //周期校验状态
  const [cronExpressionStatus, setCronExpressionStatus] = useState<"" | "success" | "warning" | "error" | "validating" | undefined>('');
  const [isScheduled, setIsScheduled] = useState<boolean>(false);
  const [submitBtnLoading, setSubmitBtnLoading] = useState(false);
  const [cronGeneratorVisible, setCronGeneratorVisible] = useState<boolean>(false);
  const [step2SqlResult, setStep2SqlResult] = useState<{ nextBtnState: boolean; filePath: string; } | null>(null)
  const { t } = useTranslation();
  const [uploadFilePreviewMap, setUploadFilePreviewMap] = useState<Map<string, string>>(new Map())

  useEffect(() => {
    getUploadFileSize()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    const { scriptInfos = [{ fileOrder: 1 }] } = parseResult || {}
    if (!scriptInfos?.length) {
      return
    }
    // 构造渲染数据
    let resultArr: any[] = []
    const obj = handleDataFormat(scriptInfos)
    Object.keys(obj)?.forEach((key: string) => {
      const { connectionId, nodePathWithType, realConnectionName, dataSourceType } = obj[key]?.[0] || {};
      resultArr.push({
        connectionId,
        nodePathWithType,
        connectionName: realConnectionName,
        connectionType: dataSourceType,
        id: key
      })
    })
    setMergedScriptInfos(resultArr)
  }, [parseResult])

  //校验
  const { run: runCheckCron } = useRequest(checkBatchSqlCron, {
    manual: true,
    onSuccess: (res) => {
      if (res) {
        setCronExpressionStatus("success");
      } else {
        setCronExpressionStatus("error");
      }
    },
  });

  // 获取上传文件大小限制
  const getUploadFileSize = () => {
    getUserSysFile().then((res: any) => {
      if (res) {
        const { uploadSize = 0 } = res
        setUploadSize(Number(uploadSize))
      }
    }).catch((err: any) => {
      console.error(t("personal:failedToGetUploadFileSizeLimit"), err)
    })
  }

  // add item
  const handleAddTask = () => {
    form.validateFields().then(() => {
      setMergedScriptInfos([...mergedScriptInfos, { fileOrder: mergedScriptInfos?.length + 1 }])
    }).catch((error: any) => {
      console.error('form error:', error)
    })
  }

  // delete item
  const handleDeleteTask = (index: number) => {
    setMergedScriptInfos((m: any[]) => {
      m?.map((r: any, i: number) => {
        if (index === i) {
          r.delete = true
        }
        return { ...r }
      })
      return [...m]
    })
  }

  const handleNextStep = () => {
    form.validateFields().then((values) => {
      setStep(step + 1)
    })
  }

  const getScriptInfos = () => {
    let scriptInfosValue: any[] = [], initLength = 0
    const values: any = form.getFieldsValue()
    const keys = Object.keys(values)?.filter(i => i?.startsWith('uploadFile'))?.sort((a, b) => {
      const numberA = a?.split('uploadFile')?.[1] ? Number(a?.split('uploadFile')?.[1]) : 0
      const numberB = b?.split('uploadFile')?.[1] ? Number(b?.split('uploadFile')?.[1]) : 0
      return numberA - numberB
    })
    for (let i = 0; i < keys?.length; i++) {
      const curIndex = keys[i]?.split('uploadFile')?.[1] ? Number(keys[i]?.split('uploadFile')?.[1]) : 0
      const fileEncodeList = values?.[`fileEncode${curIndex}`] || []
      // eslint-disable-next-line no-loop-func
      scriptInfosValue[i] = values[keys[i]]?.map((r: any, index: number) => ({
        ...r,
        fileOrder: (index + 1 + initLength),
        fileEncodeName: (fileEncodeList?.[index] ?? r?.fileEncodeName)
      }));
      initLength += values[keys[i]]?.length || 0
    }
    return scriptInfosValue?.flat(1)?.filter(i => i)
  }

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      const params = {
        description: values?.description,
        executeMode: values?.executeMode,
        errorHandleMode: values?.errorHandleMode,
        indexFile: parseResult?.indexFileName,
        indexFilePath: parseResult?.indexFilePath,
        scheduledCron: values?.scheduledCron,
        scriptInfos: getScriptInfos(),
        sqlCheckFilePath: step2SqlResult?.filePath
      }
      setSubmitBtnLoading(true)
      createBatchSql(params).then((res: any) => {
        onClose()
        message.success(t("personal:createBatchExecutionTaskSuccessful"))
        history.push('/download');
        dispatch(setTabsKey('batchExecute'))
        refreshData && refreshData()
      }).catch((err: any) => {
        console.error(t("personal:createBatchExecutionTaskFailed"), err)
      }).finally(() => {
        setSubmitBtnLoading(false)
      })
    })
  }

  const handleConnectionChange = (value: any, index: number) => {
    const { connectionId, nodePathWithType, connectionType, connectionName, templateName } = value || {};
    setMergedScriptInfos((m: any[]) => {
      m?.map((r: any, i: number) => {
        if (index === i) {
          r.connectionId = Number(connectionId);
          r.nodePathWithType = nodePathWithType;
          r.connectionType = connectionType;
          r.connectionName = connectionName;
          r.templateName = templateName;
        }
        return { ...r }
      })
      return [...m]
    })
    // 改变连接后重置附件信息
    // form.setFieldsValue({[`uploadFile${index}`]: []})
  }

  // 数据处理 
  const handleDataFormat = (scriptInfos: any[]) => {
    let res: any = {}
    scriptInfos?.sort((a: any, b: any) => a.fileOrder - b.fileOrder)?.forEach((item: any) => {
      let resKeysLength = Object.keys(res)?.length || 0
      const preItem: any = resKeysLength ? res[resKeysLength - 1]?.[res[resKeysLength - 1]?.length - 1] : null
      if (preItem && (preItem?.nodePathWithType === item?.nodePathWithType) && (preItem?.fileOrder + 1 === item?.fileOrder)) {
        res[resKeysLength - 1] = [...res[resKeysLength - 1], item]
      } else {
        res[resKeysLength] = [{ ...item, key: resKeysLength }]
      }
    })
    return res
  }

  // 上传文件夹
  const handleFieldUploadCallback = (value: any[], folderName: string) => {
    setLoading(true)
    parseBatchSqlFile({ files: value?.map(i => i?.originFileObj) }).then((res: any) => {
      if (res) {
        const { scriptInfos = [], warningMessage } = res
        if (warningMessage) {
          message.warning(warningMessage.split("\n").map((str: string, index: number) => (
            <div key={str} style={{ display: !!index ? 'block' : 'inline-block' }}>{str}</div>
          )))
        }
        // 回显form表单 999 
        const resultObj = handleDataFormat(scriptInfos)
        Object.keys(resultObj)?.forEach((key: string, index: number) => {
          const { connectionId, nodePathWithType, dataSourceType: connectionType, realConnectionName } = resultObj[key]?.[0] || {};

          form.setFieldsValue({
            [`connection${index}`]: {
              connectionId: Number(connectionId),
              nodePathWithType,
              connectionType,
              connectionName: realConnectionName
            },
            [`uploadFile${index}`]: resultObj[key]?.map((i: any) => ({ ...i, name: i?.fileName }))
          })
        })
        // 回显form表单end
        setParseResult(res)
        setFolderName(folderName || value?.[0]?.name)
        form.setFieldsValue({ uploadFolder: value })
      }
    }).catch((err: any) => {
      console.error(t("personal:fileParsingFailed"), err)
    }).finally(() => {
      setLoading(false)
    })
  }

  // 手动上传附件
  const handleManualUpload = (value: any[], preValue: any[], index: number) => {
    !loading && setLoading(true)
    debouncedFunction(value, preValue, index);
  }

  const debouncedFunction = debounce((value, preValue, index) => {
    manualUploadFn(value, preValue, index);
  }, 300);

  const manualUploadFn = (value: any[], preValue: any[], index: number) => {
    const connectionInfo = form.getFieldValue([`connection${index}`])
    const fileEncodePreValue = form.getFieldValue(`fileEncode${index}`) || []
    const { connectionId, nodePathWithType } = connectionInfo || {};
    parseBatchSqlFile({ files: value?.map(i => i?.originFileObj), connectionId, nodePathWithType, sqlFile: true }).then((res: any) => {
      if (res) {
        const { scriptInfos = [], warningMessage } = res
        if (warningMessage) {
          message.warning(warningMessage.split("\n").map((str: string, index: number) => (
            <div key={str} style={{ display: !!index ? 'block' : 'inline-block' }}>{str}</div>
          )))
        }
        form.setFieldsValue({
          [`uploadFile${index}`]: [...preValue, ...scriptInfos?.map((i: any) => ({ ...i, nodePathWithType, name: i?.fileName }))],
          [`fileEncode${index}`]: [...fileEncodePreValue, ...scriptInfos?.map((i: any) => i?.fileEncodeName || 'UTF-8')]
        })
        const newUploadFilePreviewMap = new Map(uploadFilePreviewMap)
        // 获取文件预览
        Promise.all(scriptInfos.map(async (file: any) => {
          const res = await getBatchSqlPreviewFile({
            fileName: file?.filePath,
            encodeName: file?.fileEncodeName || 'UTF-8'
          })
          newUploadFilePreviewMap.set(`uploadFile${index}-${file?.filePath}`, res)
        })).finally(() => {
          setUploadFilePreviewMap(newUploadFilePreviewMap)
          setLoading(false)
        })
      } else {
        form.setFieldsValue({ [`uploadFile${index}`]: [...preValue] }) // 手动重置form表单上传列表
        setLoading(false)
      }
    }).catch((err: any) => {
      console.error(t("personal:singleUploadParsingFailed"), err)
      form.setFieldsValue({ [`uploadFile${index}`]: [...preValue] }) // 手动重置form表单上传列表
      setLoading(false)
    })
  }

  // 模板文件删除
  const handleTemplateRemove = () => {
    Modal.confirm({
      title: t("personal:confirmDeleteTemplateFile"),
      onOk: () => {
        form.resetFields()
        setFolderName('')
        setParseResult({})
      }
    })
  }

  // 删除上传sql
  const handleDeleteUploadSql = (params: any) => {
    deleteSqlFile(params).then().catch((err: any) => {
      console.error(t("personal:deleteUploadedSqlFileFailed"), err)
    })
  }
  const handleClose = () => {
    const scriptInfos = getScriptInfos()
    if (scriptInfos?.length) {
      const params = {
        scriptInfos
      }
      handleDeleteUploadSql(params)
    }
    onClose()
  }

  const validateDescription = (_: any, value: any) => {
    if (value && value?.trim()?.length > 20) {
      return Promise.reject(t("personal:maxLength20Characters"))
    }
    return Promise.resolve()
  }

  // 下载审核结果
  const handleSqlExecuteResultDownload = () => {
    if (step2SqlResult?.filePath) {
      downloadSqlFile({ attachPath: step2SqlResult?.filePath })
        .then(() => message.success(t("personal:downloadSuccessful")))
    }
  };

  const renderNextBtn = () => {
    if (step === 1) {
      return <Button type='primary' onClick={handleNextStep}>{t("personal:sqlAudit")}</Button>
    }
    if (step === 2) {
      return (
        <Tooltip title={ !step2SqlResult?.nextBtnState && t("personal:sqlAuditResultContainsError")}>
          <Button type='primary' disabled={ !step2SqlResult?.nextBtnState ? true : false} onClick={handleNextStep}>
            {t("personal:next")}
          </Button>
        </Tooltip>
      )
    }
    if (cronExpressionStatus !== 'success' && isScheduled) {
      return (
        <Tooltip title={t("personal:taskCycleExpressionInvalid")} placement='topRight' >
          <Button
            type='primary'
            disabled={true}
          >
            {t("personal:confirmDing")}
          </Button>
        </Tooltip>
      )
    }
    return <Button type='primary' onClick={handleSubmit} loading={submitBtnLoading}> {t("personal:confirmDing")}</Button>
  }

  const handleCronGenerator = () => {
    setCronGeneratorVisible(true)
  }

  const handleCronGeneratorOk = (cron: string) => {
    form.setFieldsValue({ scheduledCron: cron });
    setCronGeneratorVisible(false)
  }

  const handleCronGeneratorCancel = () => {
    setCronGeneratorVisible(false)
  }

  return (
    <>
      <Drawer
        title={t("personal:newBatchExecutionTask")}
        width={1100}
        visible={visible}
        onClose={handleClose}
        maskClosable={false}
        className={styles.createBatchExecuteDrawer}
        footer={
          <Space>
            {step === 2 && <Button onClick={handleSqlExecuteResultDownload}>{t("personal:downloadAuditResult")}</Button>}
            {step === 1 ? <Button onClick={handleClose} className='mr10'>{t("personal:cancel")}</Button>
              : <Button onClick={() => setStep(step-1)} className='mr10' disabled={submitBtnLoading}>{t("personal:previous")}</Button>
            } 
            {renderNextBtn()}
          </Space>
        }
        footerStyle={{ display: 'flex', justifyContent: 'flex-end' }}
      >
        <Spin spinning={loading}>
          <Form
            form={form}
            {...layout}
          >
            {
              <span style={{ display: step === 1 ? 'block' : 'none' }}>
                <Form.Item
                  label={t("personal:uploadFolder")}
                  name="uploadFolder"
                  extra={t("personal:supportUploadFolderAndCompressedFiles")}
                >
                  <Tooltip title={t("personal:batchExecutionSupportsPlainTextOnly")}>
                    <Button
                      className={styles.uploadButtonStyle}
                      onClick={() => setUploadFolderModalVisible(true)}
                    >
                      {t("personal:clickUploadFolder")}<UploadOutlined className='ml4' />
                    </Button>
                  </Tooltip>
                </Form.Item>
                {
                  folderName &&
                  <Form.Item wrapperCol={{ span: 18, offset: 6 }}>
                    <div className='flexAlignCenterBetween' style={{ width: 470 }}>
                      <span>
                        <PaperClipOutlined className='mr4' />
                        {folderName}
                      </span>
                      <DeleteOutlined className='options' onClick={() => handleTemplateRemove()} />
                    </div>
                  </Form.Item>
                }
                <Divider style={{ margin: '24px 0' }} />
                <div className={styles.dataSourceWrap}>
                  <span className={styles.addBtn} style={{ top: folderName ? 228 : 182 }} onClick={handleAddTask}>
                    <PlusCircleOutlined />
                  </span>
                  {
                    mergedScriptInfos?.map((item: any, index: number) => {
                      if (item?.delete) {
                        return null
                      }
                      return (
                        <span key={index} style={{ position: 'relative' }}>
                          <Form.Item
                            label={t("personal:database")}
                            name={`connection${index}`}
                            rules={[{ required: true, message: t("personal:pleaseSelect")}]}
                          >
                            <ElementTreeSelect
                              // value={item}
                              isBatchExecute={true}
                              onChange={(value: any) => { handleConnectionChange(value, index) }}
                            />
                          </Form.Item>
                          <Form.Item
                            label={t("personal:attachmentUpload")}
                            name={`uploadFile${index}`}
                            rules={[{ required: true, message: t("personal:pleaseSelectAttachmentUpload") }]}
                          >
                            <CustomDragableUploadList
                              multiple={true}
                              directory={false}
                              notAllowCompresPack={true}
                              uploadSize={uploadSize}
                              buttonProps={{
                                disabled: !item?.nodePathWithType,
                                style: { width: "470px", border: "1px dashed #DFE1E6", background: "#FBFBFB", marginBottom: 10 },
                                buttonText: t("personal:uploadAttachment"),
                                tooltipTxt: t("personal:batchExecutionSupportsPlainTextOnly")
                              }}
                              fileListWidth={470}
                              onChange={(value: any, preValue: any[]) => { handleManualUpload(value, preValue, index) }}
                              fileListRender={(file: any, fileRenderList: any[]) =>
                                <Form.Item name={`fileEncode${index}`} noStyle>
                                  <CustomUploadFilesRender
                                    file={file}
                                    fileRenderList={fileRenderList}
                                    form={form}
                                    width={"90%"}
                                    field="filePath"
                                    uploadFormName={`uploadFile${index}`}
                                    uploadFileEncodeName={`fileEncode${index}`}
                                    handleDelParams={()=>{
                                      const uploadFileValue = form.getFieldValue(`uploadFile${index}`)
                                      const fileIndex = fileRenderList.indexOf(file)
                                      const needDeleleItem = uploadFileValue?.find((_: any, i: number) => fileIndex === i)
                                      const params = { scriptInfos: [needDeleleItem] }
                                      return params
                                    }}
                                    deleteApi={deleteSqlFile}
                                    previewApi={getBatchSqlPreviewFile}
                                    uploadFilePreviewMap={uploadFilePreviewMap}
                                    modifyUploadFilePreviewMap={setUploadFilePreviewMap}
                                  />
                                </Form.Item>
                              }
                            />
                          </Form.Item>
                          {
                            mergedScriptInfos?.filter(i => !i?.delete)?.length > 1 &&
                            <span 
                              className='options mb10' 
                              style={{ position: 'absolute', right: 260, top: '2px' }} 
                              onClick={() => { handleDeleteTask(index) }}
                              >
                                {t("personal:delete")}
                            </span>
                          }
                        </span>
                      )
                    })
                  }
                </div>
              </span>
            }
            {
              step === 2 &&
              <span style={{ display: step === 2 ? 'block' : 'none' }}>
                <BatchExecuteStep2
                  getExecuteResult={(res: { nextBtnState: boolean; filePath: string; }) => setStep2SqlResult(res)}
                  scriptInfos={getScriptInfos()}
                />
              </span>
            }
            {
              step === 3 &&
              <span style={{ display: step === 3 ? 'block' : 'none' }}>
                <Form.Item
                  label={t("personal:executionMethod")}
                  name="executeMode"
                  rules={[{ required: true, message: t("personal:pleaseSelectExecutionMethod")}]}
                >
                  <Radio.Group onChange={(e) => setIsScheduled(e?.target?.value === "SCHEDULED")}>
                    {
                      getExecuteWayEnums()?.map((i: any) => {
                        return (
                          <Radio value={i?.value} key={i?.value}>{t(i?.label) || '-'}</Radio>
                        )
                      })
                    }
                  </Radio.Group>
                </Form.Item>
                {
                  isScheduled &&
                  <Form.Item
                    label={
                      <Tooltip title={getCronExpressionTooltipTitle()}>
                        {t("personal:taskCycle")}<QuestionCircleOutlined />
                      </Tooltip>
                    }
                    required
                  >
                    <Row>
                      <Col>
                        <Form.Item
                          name="scheduledCron"
                          required
                          hasFeedback
                          validateStatus={cronExpressionStatus}
                          rules={[
                            { required: true, message: t("personal:pleaseEnterTaskCycle")  },
                            {
                              validator(_: any, value: string) {
                                if (!value) {
                                  setCronExpressionStatus('error')
                                  return Promise.reject(new Error(t("personal:pleaseEnterTaskCycle")))
                                }
                                return Promise.resolve()
                              }
                            }
                          ]}
                        >
                          <Input
                            placeholder={t("personal:pleaseEnterTaskCycle")}
                            style={{ width: 260 }}
                            onChange={(e: any) =>
                              setCronExpressionStatus(e?.target?.value ? '' : 'error')
                            }
                          />
                        </Form.Item>
                      </Col>
                      <Col push={1}>
                        <Button
                          type="primary"
                          onClick={() => {
                            runCheckCron({
                              cron: form.getFieldValue("scheduledCron") || '',
                            })
                          }}
                        >
                          {t("personal:validation")}
                        </Button>
                        <span className="options ml4" onClick={handleCronGenerator}>{t("personal:cronGenerator")}</span>
                      </Col>
                      {
                        cronGeneratorVisible && 
                        <CronGenerator
                          defaultValue={form.getFieldValue("scheduledCron")}
                          onOk={handleCronGeneratorOk}
                          onCancel={handleCronGeneratorCancel}
                        />
                      }
                    </Row>
                  </Form.Item>
                }
                <Form.Item
                  label={t("personal:executionErrorHandler")}
                  name="errorHandleMode"
                  rules={[{ required: true, message:  t("personal:pleaseSelectExecutionErrorHandler")}]}
                >
                  <Radio.Group>
                    {
                      getErrorHandleWayEnums()?.map((i: any) => {
                        return (
                          <Radio value={i?.value} key={i?.value}>{t(i?.label) || '-'}</Radio>
                        )
                      })
                    }
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  label= {t("personal:description")}
                  name='description'
                  rules={[{ validator: validateDescription }]}
                >
                  <Input.TextArea
                    rows={4}
                    placeholder={t("personal:inputDescriptionContent")}
                    style={{ width: 470 }}
                    maxLength={20}
                  />
                </Form.Item>
              </span>
            }
          </Form>
        </Spin>
      </Drawer>
      {/* 上传文件夹 */}
      {
        uploadFolderModalVisible &&
        <UploadFolderModal
          visible={true}
          uploadSize={uploadSize}
          onClose={() => {
            setUploadFolderModalVisible(false)
          }}
          callback={handleFieldUploadCallback}
        />
      }
    </>
  )
}

export default CreateBatchExecuteDrawer