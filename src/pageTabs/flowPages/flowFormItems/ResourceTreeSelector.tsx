import React, { useEffect, useRef, useState, useMemo } from 'react'
import { TreeSelect } from 'antd'
import * as _ from 'lodash';
import type { DataNode } from 'antd/es/tree'
import { LegacyDataNode } from 'rc-tree-select/lib/interface'

import {
  queryAllGroupNodes,
  queryTreeNodeAll,
  TreeNode,
} from 'src/api'

import { Iconfont } from 'src/components'
import { useRequest } from 'src/hook'
import { isEmpty } from 'lodash';
import { generateNPWTList } from '../utils';
import styles from './index.module.scss'
import { FormInstance } from 'antd/lib/form';
import { useTranslation } from 'react-i18next';

interface IProps {
  onChange?: (value: LegacyDataNode[]) => void
  defaultValue?: LegacyDataNode[]
  endSchema: boolean,
  form: FormInstance<any>,
  callbackConnections: any
}
const getTargetNode = (treeData: TreeNode[], nodePath: string,) => {
  const queue = [...treeData]
  while (queue.length) {
    const currentNode = queue.shift()
    if (!currentNode) return
    if (currentNode.nodePath === nodePath) {
      return currentNode
    }
    if (currentNode.children) {
      queue.push(...currentNode.children)
    }
  }
}

export function findNodeByKey(nodes: DataNode[], key: string): DataNode | undefined {
  if (!key || !nodes || !nodes.length) return;

  for (const item of nodes) {
    // find target node
    //@ts-ignore
    if (item?.key === key) {
      return item;
    }

    if (item?.children && item?.children?.length > 0) {
      const res = findNodeByKey(item.children, key);
      if (res) return res;
    }
  }
  return;
}

// 构造树结构  
export const generateTree = (data: any[], treeMapRef: any) => {
  const allId = data
    ?.map((i) => i.id)
    ?.filter((i, index, arr) => arr.indexOf(i) === index)
  const allParentId = data
    ?.map((i) => i.parentId)
    ?.filter((i, index, arr) => arr.indexOf(i) === index)

  const validateParentId = (item: any) => {
    return !!data?.filter((i: any) => {
      return i?.parentId === item?.id && item?.nodeType !== i?.nodeType
    })?.length
  }

  const filterData = data.filter((item, _, arr) => {
    // 自身是父级（且id和parentId不能重复）
    if (allParentId.includes(item.id) && validateParentId(item)) {
      item.children = arr.filter((i) => i.parentId === item.id)
    }
    // 没有父级
    if (!allId.includes(item.parentId) || !item.parentId) {
      return true
    }
    return false
  })

  const formatTree = (data: any[]): any[] =>
    data.map((item: any) => {
      const { nodeType, nodeName, id, connection = {}, nodePath } = item;
      const realValue = nodeType === 'datasource' ? nodeName : nodeType === 'group' ? id : item.nodePathWithType;
      item.connectionType = item?.nodeType !== 'datasource' ? connection?.connectionType : nodeName;
      item.key = nodePath; // 唯一key
      item.title = item.nodeName
      item.value = nodePath;
      item.realValue = realValue;
      let icon = `icon-${nodeType}`;

      if (nodeType === "connection") {
        icon = `icon-${connection?.connectionType}`;
      } else if (nodeType === 'datasource') {
        icon = `icon-connection-${nodeName}`;
      } else if (nodeType === 'group') {
        icon = "icon-shujukuwenjianjia"
      }
      item.icon = <Iconfont type={icon} />

      if (item.children) {
        item.children = formatTree(item.children);
      }

      treeMapRef?.current?.set(nodePath, item)
      return { ...item }
    })
  return formatTree(filterData)
}

const ResourceTreeSelector = ({
  onChange,
  defaultValue,
  endSchema = false,
  form,
  callbackConnections
}: IProps) => {
  const { t } = useTranslation();
  const { SHOW_PARENT } = TreeSelect
  // tree map, 记录元素树节点的引用
  const treeMapRef = useRef(new Map<string, LegacyDataNode>())


  const [treeData, setTreeData] = useState<any[]>([])
  const [treeLoadedKeys, setTreeLoadedKeys] = useState<React.Key[]>([]) // 已加载的节点
  const [treeExpandedKeys, setTreeExpandedKeys] = useState<React.Key[]>([])
  const [checkedNodeItems, setCheckedNodeItems] = useState<LegacyDataNode[]>([]);
  const [selectLoading, setSelectLoading] = useState<boolean>(true);

  const flagRef = useRef<boolean>(false)

  // 递归获取默认值的节点数据
  const recursionLoadData = async (node: any, nodePathList: string[], nodePath: string, expandKeys: any[]) => {
    const beforeValues = form.getFieldValue("elements") || []
    if (node && nodePathList) {
      if (nodePathList.length === 0) {
        const newNode = {
          ...node,
          schemaFlag: defaultValue?.[0]?.schemaFlag
        }
        setCheckedNodeItems([...beforeValues, newNode])
        onChange?.([...beforeValues, newNode]);
        const expandedList = generateNPWTList(nodePath);
        expandedList.pop()
        setTreeExpandedKeys([...expandedList])
        flagRef.current = false
        setTimeout(() => {
          setSelectLoading(false)
        }, 0)
        return
      }
      if (node?.children) {
        const n = node.children?.filter((i: any) => i.nodePath === nodePathList?.[0])[0]
        if (n) {
          expandKeys.push(n?.nodePath)
          nodePathList.shift()
          recursionLoadData(n, nodePathList, nodePath, expandKeys)
        }
        node.children?.map((c: any) => {
          if (c.nodeType === 'group' && c.children) {
            const n = c.children?.filter((i: any) => i.nodePath === nodePathList?.[0])[0]
            if (n) {
              expandKeys.push(c?.nodePath)
              expandKeys.push(n?.nodePath)
              nodePathList.shift()
              recursionLoadData(n, nodePathList, nodePath, expandKeys)
            }
            else return
          }
          else return
        })
      }
      else {
        if (node?.newNodeType === "SCHEMA" && endSchema) {
          setCheckedNodeItems([...beforeValues, node])
          onChange?.([...beforeValues, node]);
          flagRef.current = false
          setTimeout(() => {
            setSelectLoading(false)
          }, 0)
          return
        }
        await loadData(node)
      }
    }
  }

  // 左侧treeData
  const { data: treeWrapData } = useRequest(queryAllGroupNodes, {
    formatResult(res) {
      const allConnections = res?.nodeList?.filter((node: any) => node.nodeType === 'connection')?.map((c: any) => ({ connectionId: c?.id, dataSourceType: c?.connection?.connectionType, nodeName: c?.nodeName }))
      callbackConnections?.(allConnections)
      return generateTree(res?.nodeList || [], treeMapRef).filter(node => node?.children?.length);
    },
    onSuccess: () => {
      setSelectLoading(true)
    }
  });

  useEffect(() => {
    if (!isEmpty(defaultValue)) {
      flagRef.current = true
      setSelectLoading(true)
    }
  }, [defaultValue])

  useEffect(() => {
    if (!isEmpty(defaultValue) && !isEmpty(treeData) && flagRef.current) {
      let expandKeys: string[] = []
      form.setFieldsValue({ elements: [] });
      defaultValue?.map((item: any) => {
        const initValue: any = item
        const { connectionType, nodePath } = initValue;
        const nodePathList = generateNPWTList(nodePath);
        nodePathList.shift()
        const firstData = treeData?.filter((i) => {
          return i?.connectionType === connectionType;
        })?.[0]
        expandKeys = [firstData?.nodePath]
        recursionLoadData(firstData, nodePathList, nodePath, expandKeys)
      });
      expandKeys.pop()
      setTreeExpandedKeys(expandKeys)
    }
    else {
      setSelectLoading(false)
    }
  }, [defaultValue, treeData])

  /**
* 获取子节点
*/
  const { run: loadChildren } = useRequest(queryTreeNodeAll, {
    manual: true,
    formatResult: res => {
      return res?.map((node: any) => {
        const { nodeName, nodePathWithType, nodeType, nodePath, connection = {}, sdt = {}, newNodeType } = node || {};

        let icon = `icon-${nodeType}`;
        if (node?.nodeType === 'datasource') {
          icon = `icon-connection-${node?.connectionType}`;
        } else if (nodeType === 'group') {
          icon = "icon-shujukuwenjianjia"
        }

        const treeNode = {
          ...node,
          key: nodePath,
          value: nodePath,
          realValue: nodePathWithType,
          title: nodeName,
          icon: <Iconfont type={icon} />,
          connectionType: connection?.connectionType,
          isLeaf: !sdt?.hasChild || nodeType === "table" || (endSchema && newNodeType === "SCHEMA"),
        };

        treeMapRef.current.set(nodePath, treeNode)
        return treeNode;
      });
    },
  });

  // 异步加载数据
  const loadData = async (node: any) => {
    const key: any = node?.key

    const {
      id,
      connectionId,
      connection,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType,
      newNodeType,
    } = node;

    if (newNodeType === 'SCHEMA' && endSchema) {
      return
    }

    if (nodeType === "datasource" || nodeType === "group") {
      setTreeLoadedKeys((keys) => [...keys, key])
      return;
    }

    const { connectionType } = connection || {}
    const params = {
      connectionId: nodeType === 'connection' ? id : connectionId,
      connectionType,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType: nodePathWithType,
    }
    try {
      //@ts-ignore
      const children = await loadChildren({
        ...params
      });
      const targetNode = treeMapRef.current.get(key)
      if (!targetNode) return
      targetNode.children = children;

      const cloneTreeData = _.cloneDeep(treeData);
      //@ts-ignore
      const target = getTargetNode(cloneTreeData, key);

      if (target) {
        target.children = children || [];
      }

      setTreeData(cloneTreeData);
      // 懒加载成功，加入 loadedKeys
      setTreeLoadedKeys((keys) => [...keys, key])
    } catch (error) {
      // 懒加载失败，自动收起节点
      setTreeExpandedKeys((keys) => keys.filter((el) => el !== key))
    }
  }

  useEffect(() => {
    if (treeWrapData) {
      setTreeData(treeWrapData || []);
    }
  }, [treeWrapData])

  const handleTreeExpand = (keys: any[]) => {
    setTreeExpandedKeys(keys)
  };

  const onChangeValue = (checkedValues: any) => {
    const targetNodes: any[] = []
    checkedValues.forEach((i: any) => {
      const targetNode = treeMapRef.current.get(i.value)
      if (targetNode) {
        targetNodes.push(targetNode)
      }
    })

    setCheckedNodeItems(targetNodes)

    onChange?.(targetNodes);
  }

  const getIcon = (node: any) => {
    const { nodeType, connection = {} } = node || {};
    let icon = `icon-${nodeType}`;
    if (nodeType === "connection") {
      icon = `icon-${connection?.connectionType}`;
    } else if (node?.nodeType === 'datasource') {
      icon = `icon-connection-${node?.connectionType}`;
    } else if (nodeType === 'group') {
      icon = "icon-shujukuwenjianjia"
    }
    return icon
  }

  const labeledValue = useMemo(() => {
    if (checkedNodeItems) {
      return checkedNodeItems.map(item => ({
        ...item,
        value: item.value,
        label: <div style={{ display: 'flex', alignItems: 'center' }}>
          <Iconfont
            type={getIcon(item)}
            style={{ marginRight: 4 }} />
          {item.nodeName}
        </div>
      }))
    }
    return null
  }, [checkedNodeItems])

  return (
    <TreeSelect
      key={"databaseTree"}
      treeData={treeData}
      value={labeledValue}
      loading={selectLoading}
      className={styles.resourceTreeSelector}
      allowClear
      labelInValue
      loadData={loadData}
      treeLoadedKeys={treeLoadedKeys}
      treeExpandedKeys={treeExpandedKeys}
      onTreeExpand={handleTreeExpand}
      onChange={onChangeValue}
      treeCheckable={true}
      showCheckedStrategy={SHOW_PARENT}
      showArrow
      placeholder={t('flow_select_db_ele')}
      treeIcon={true}
      maxTagCount={3}
      autoClearSearchValue={false}
      dropdownStyle={{ width: 'maxContent', maxHeight: "400px" }} // 设置下拉框宽度自适应于内容
      treeNodeFilterProp='title'
    />
  );
};

export default ResourceTreeSelector;