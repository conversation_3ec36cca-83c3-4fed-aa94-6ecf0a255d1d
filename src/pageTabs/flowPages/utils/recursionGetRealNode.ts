// 提取datasource和group的children
export const recursionGetRealNode = (ele: any): any[] => {
  if (['datasource', 'group'].includes(ele.nodeType)) {
    let list: any[] = []
    ele?.children?.map((e: any) => {
      list = list.concat(recursionGetRealNode(e))
    })
    return list
  }
  else return [ele]
}

// 分级拆解nodePath
export const generateNPWTList = (nodePath: string) => {
  const arr = nodePath.split('/');
  arr.shift();
  const nodePathList = arr.map((_, index) => {
    return '/' + arr.slice(0, index + 1).join('/')
  });
  return nodePathList;
}