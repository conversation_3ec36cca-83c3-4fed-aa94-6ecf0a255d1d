import React, { useState, useEffect, useMemo } from 'react'
import { useHistory, useLocation } from "react-router-dom";
import { useSelector, useDispatch, useRequest } from 'src/hook'
import {
  // Alert,
  Input,
  message,
  Button,
  Table,
  Popconfirm,
  Form,
  Tag,
  Modal,
  Typography,
  Descriptions,
  Switch,
  Tooltip,
  Dropdown,
  Space,
  Menu,
} from 'antd'
import {
  fetchDeptUserList,
  fetchOrgTreeData,
  fetchOrgTreeDataNoSelectNode,
  setDeptUsersManagerId,
  setOrganizationSearch
} from '../organizationSlice'
import { LinkButton, CreateButton, Iconfont } from 'src/components'
import {
  AddUserModal,
  UserManagementModal,
  UserPermDetaiModal,
} from '../orgModals'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { ColumnsType } from 'antd/lib/table'
import {
  deleteUser,
  editGroupUsers,
  UserEntity,
  resetUserPassword,
  changeUserStatus,
  resetOtpKey,
  getDeptUsersManagerId,
  updateUsersStatus,
  resetPwd,
  deleteUsers,
} from 'src/api'
import { getRandomUserPassword } from 'src/util'
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import { effectTimeTypeOptions } from 'src/constants'
import { PlusSquareOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons'
import Service from 'src/service'
import styles from './index.module.scss'
import { isEmpty, toInteger } from 'lodash'
import PermissionTooltip from 'src/components/PermissionTooltip/PermissionTooltip';
import { BatchUpdateEffectTime } from '../orgModals/BatchUpdateEffectTime';
import moment from 'moment';
import { useTranslation } from 'react-i18next';

const { useForm } = Form
const { Text } = Typography

interface IProps {
  [p: string]: any
}

interface RemoveButtonProps {
  operation: () => Promise<any>
}

const RemoveButton: React.FC<RemoveButtonProps> = ({ operation }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false)
  const dispatch = useDispatch()

  return (
    <LinkButton
      onClick={() => {
        setLoading(true)
        operation()
          .then(() => {
            dispatch(fetchOrgTreeDataNoSelectNode())
          })
          .finally(() => setLoading(false))
      }}
      loading={loading}
    >
      {t('common.btn.moveout')}
    </LinkButton>
  )
}

export const UserList = (props: IProps) => {

  const { permissionlist } = props
  const { isOnlyRead, roleNameList = []} = permissionlist || {}
  const { t } = useTranslation();
  const history = useHistory();
  const dispatch = useDispatch()
  const location = useLocation<GloablSearchLocationState>();
  const { state = {} } = location;
  
  const [mode, setMode] = useState<'add' | 'edit' | 'editAndAdd'>('add') // add（新增） edit（编辑） editAndAdd （先编辑当前数据后新增）
  const {
    userList,
    userListLoading,
    organizationPageNum,
    organizationPageSize,
    organizationTotalNum,
    organizationSearch,
  } = useSelector((state) => state.organization)
  const { userId: currentUserId } = useSelector((state) => state.login.userInfo)
  const { companyId, companyName } = useSelector((state) => state.companyInfo)
  const { isLoginCtrlByManager } = useSelector((state) => state.login)

  const dept = useSelector((state) => state.organization.selectedNode)
  const isGroup = dept?.orgType === 'GROUP'

  const [selectRecord, setSelectRecord] = useState({}) as any

  const [search, setSearch] = useState<string>('')
  const [searchOutput, setSearchOutput] = useState<string>('')
  const [createFrom, setCreateFrom] = useState<string>('')

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [disableds, setDisableds] = useState<boolean>(true);
  const [requestUserList, setRequestUserList] = useState<boolean>(false);
  const [effectTimeId, setEffectTimeId] = useState<number>()

  // ! HACK
  // 重构时要按标准实现
  useEffect(() => {
    dispatch(setOrganizationSearch(''))
    setSearchOutput('')
  }, [dept])

  useEffect(() => {
    if (!isEmpty(userList) && !isEmpty(selectedRowKeys)) {
      const newSelectRowKeys = selectedRowKeys.filter((key: any) => {
        return userList.map((item: any) => item.userId).includes(key)
      })
      setSelectedRowKeys(newSelectRowKeys)
    }
  }, [userList]);


  const [form] = useForm()
  useEffect(() => {
    if (!dept) return
    if (dept?.id) {
      getUsersManagerId(dept?.id)
    }
    const promise = dispatch(
      fetchDeptUserList({
        dept,
        search: searchOutput,
        pageNum: 0,
        pageSize: organizationPageSize,
      }),
    )
    return () => promise.abort()
  }, [dispatch, dept, searchOutput, requestUserList])

  const { run: resetPassword } = useRequest(resetUserPassword, {
    manual: true,
  })
  const { fetches, run: toggleStatus } = useRequest(changeUserStatus, {
    manual: true,
    fetchKey: ({ userId }) => userId?.toString() || '',
    onSuccess: () => {
      dept &&
        dispatch(
          fetchDeptUserList({
            dept,
            search: searchOutput,
            pageNum: 0,
            pageSize: organizationPageSize,
          }),
        )
    },
  })

  //全局搜索定位
  useEffect(() => {
    if (state?.globalSearchRecordPosition) {
      //处理分页 并选中
      const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
      dept &&
      dispatch(
        fetchDeptUserList({
          dept,
          search: searchOutput,
          pageNum: pageNum - 1, //从0开始
          pageSize: organizationPageSize,
        })
      );
    }
  }, [state?.globalSearchRecordPosition, dept, organizationPageSize])
  
  const isSelectedRowIndex = useMemo(() => {
    if (!state?.globalSearchRecordPosition) {
      return null;
    }
    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10) -1;
    if (pageNum === organizationPageNum) {
      const itemIndexInPage = state.globalSearchRecordPosition % organizationPageSize;
      return itemIndexInPage === 0 ? organizationPageSize - 1 : itemIndexInPage - 1;
    }
  },[organizationPageNum, state?.globalSearchRecordPosition])
  
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  useEffect(() => {
    if (selectedRowKeys.length > 0) {
      setDisableds(false)
    } else {
      setDisableds(true)
    }
  }, [selectedRowKeys])

  // 批量启用/禁用
  function bulkChangeStatus(status: boolean) {
    const userIds = selectedRowKeys;
    updateUsersStatus({ userIds, status }).then(res => {
      if (res) {
        setRequestUserList(!requestUserList);
        message.success(t('systemManagement.personManagement.setting.success'))
      } else {
        message.success(t('systemManagement.personManagement.setting.error'))
      }
    });
  }

  // 批量设置生效时间
  const batchSetEffectTime = () => {
    const selectedRowNodes = userList.filter((item: any) => selectedRowKeys.includes(item?.userId))
    const hasNoTwoFactorAuth = selectedRowNodes.some((item: any) => !item?.twoFactor || item?.twoFactor === t('common.text.null'))
    if (hasNoTwoFactorAuth) {
      message.warning(t('systemManagement.personManagement.twoFactor.no'))
    }
    else {
      const id = selectedRowNodes[0]?.parameterValues?.find((item:any)=>item.parameter.tagName === 'TwoFactorEffectTime')?.parameter?.id
      setEffectTimeId(id);
      dispatch(showModal("BatchUpdateEffectTime"));
    }
  }

  const menu = (
    <Menu>
      <Menu.Item onClick={() => bulkChangeStatus(false)}>{t('common.btn.batch.enable')}</Menu.Item>
      <Menu.Item onClick={() => bulkChangeStatus(true)}>{t('common.btn.batch.forbidden')}</Menu.Item>
      <Menu.Item>
        <Popconfirm
          title={t('systemManagement.personManagement.reset_password.tip')}
          onConfirm={() => {
            const randomPassword = getRandomUserPassword()
            resetPwd({
              userIds: selectedRowKeys,
              newP: randomPassword,
            }).then(({ code, message }) => {
              Modal.info({
                title: t('systemManagement.personManagement.reset_password.success'),
                content: (
                  <>
                    <Descriptions
                      className={styles.descriptions}
                      column={1}
                    >
                      <Descriptions.Item label={t('systemManagement.personManagement.table.column.user')}>
                        <div className={styles.selectRowDiv}>
                          <Text strong style={{ whiteSpace: 'nowrap' }}>
                            {`${selectedRowKeys}`}
                          </Text>
                        </div>
                      </Descriptions.Item>
                      <Descriptions.Item label={t('common.text.password')}>
                        {
                          <Text strong copyable>
                            {randomPassword}
                          </Text>
                        }
                      </Descriptions.Item>
                    </Descriptions>
                  </>
                ),
                centered: true,
              })
            })
          }}
          okButtonProps={{ danger: true }}
        >
          <span>{t('common.btn.batch.reset_password')}</span>
        </Popconfirm>
      </Menu.Item>
      <Menu.Item disabled={disableds}>
        <Popconfirm
          title={t('systemManagement.personManagement.delete.tip')}
          onConfirm={() => {
            const userIds: any = selectedRowKeys;
            deleteUsers({ userIds })
              .then(res => {
                message.success(t('systemManagement.personManagement.delete.success'))
                setSelectedRowKeys([])
                if (dept) {
                  dispatch(
                    fetchDeptUserList({
                      dept,
                      search: searchOutput,
                      pageNum: 0,
                      pageSize: organizationPageSize,
                    }),
                  )
                }
                dispatch(fetchOrgTreeData())
              })
              .catch(() => {
                message.success(t('systemManagement.personManagement.delete.error'))
              })
          }}
          okButtonProps={{ danger: true }}
        >
          <span>{t('systemManagement.personManagement.batch_delete')}</span>
        </Popconfirm>
      </Menu.Item>
      {isLoginCtrlByManager && <Menu.Item disabled={disableds} onClick={batchSetEffectTime}>
        {t('systemManagement.personManagement.batch_setting_time')}
      </Menu.Item>}
    </Menu>
  );

  const { run: getUsersManagerId } = useRequest(getDeptUsersManagerId, {
    manual: true,
    onSuccess(data) {
      !!data && dispatch(setDeptUsersManagerId(data?.userId))
    }
  })

  const renderAuditRange = (_: any, record: UserEntity) => {
    // 角色里有审计员，展示审计范围，审计员id为2
    const { auditUser, rolesId } = record || {}
    let auditInfo = '';
    if (auditUser) {
      auditInfo = Object.keys(auditUser).map(item => {
        return `${auditUser[item]}(${item})`
      }).join('、')
    }
    if (rolesId?.includes(2)) {
      if (auditInfo?.length > 30) {
        return <>
          <Tooltip title={auditInfo}>
            <div style={{ wordWrap: 'break-word' }}>
              {auditInfo.slice(0, 30)}...
            </div>
          </Tooltip>
        </>
      } else if (auditInfo?.length > 0) {
        return auditInfo
      } else {
        return t('systemManagement.personManagement.alluUsers')
      }
    } else {
      return '-'
    }
  }

  const columns: ColumnsType<UserEntity> = useMemo(() => {
    const baseColumns: ColumnsType<UserEntity> = [
      {
        title: '',
        render: (onlineStatus, record) => {
          return (
            <span>
              {record?.onlineStatus ?
                <span style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#1AD42D', display: "inline-block" }}></span>
                : <span style={{ width: '8px', height: '8px', borderRadius: '50%', background: 'gray', display: "inline-block" }}></span>
              }
            </span>
          )
        },
        width: 50
      },
      {
        title: t('systemManagement.personManagement.table.column.userName'),
        dataIndex: 'userName',
        render: (_text, record) => {
          const { systemAuthCode, userName } = record
          const isAdmin = Service.permService.checkAuthWithCode(
            'SystemSettings',
            systemAuthCode,
          )

          if (!isAdmin) return userName
          return (
            <span>
              <span>{userName}</span>
              <Tag className={styles.tag} color="geekblue">
                Admin
              </Tag>
            </span>
          )
        },
        ellipsis: true,
      },
      {
        title: t('systemManagement.personManagement.table.column.userId'),
        dataIndex: 'userId',
        ellipsis: true,
        render: (_text, record) => {
          const { userId } = record
          return <span>{userId}</span>
        },
      },
      {
        title: t('systemManagement.personManagement.table.column.departmentName'),
        dataIndex: 'departmentName',
        ellipsis: true,
        render: (val, record) => {
          return (
            <span>
              {record?.principalFlag ? <Iconfont type='icon-departmentHead' /> : null}
              {record?.departmentName}
            </span>
          )
        }
      },
      // {
      //   title: '性别',
      //   dataIndex: 'userGender',
      //   render: renderItemField('userGender'),
      //   width: '48px',
      // },
      {
        title: t('systemManagement.personManagement.table.column.systemRoles'),
        dataIndex: 'systemRoles',
        width: '140px',
        // ellipsis: true,
        render: (_, record) => {
          const { systemRoles } = record
          const roleString = systemRoles?.join(', ')
          return (
            <Tooltip title={roleString}>
              <span>{roleString}</span>
            </Tooltip>
          )
        },
      },
      {
        title: t('systemManagement.personManagement.table.column.telephone'),
        dataIndex: 'telephone',
        width: '128px',
        ellipsis: true,
        render: (_, record) => {
          const { telephone } = record
          return <span>{telephone}</span>
        },
      },
      {
        title: t('systemManagement.personManagement.table.column.createFrom'),
        dataIndex: 'createFrom',
        width: '100px',
        ellipsis: true,
        render: (_text) => {
          let text
          switch (_text) {
            case null:
              text = t('systemManagement.personManagement.createFrom.cqUser')
              break
            case 'adLogin':
              text = t('systemManagement.personManagement.createFrom.ad')
              break
            case 'casLogin':
              text = 'CAS'
              break
            case 'openLdapLogin':
              text = 'OpenLdap'
              break
            case 'OauthLogin':
              text = 'Oauth2.0'
              break
          }
          return text
        },
      },
      {
        title: t('systemManagement.personManagement.table.column.auditRange'),
        dataIndex: 'auditRange',
        width: '200px',
        render: renderAuditRange,
      },
      {
        title: t('systemManagement.personManagement.table.column.ip'),
        dataIndex: 'ip',
        width: '200px',
        render: (val: string) => 
        val ? (
          <Tooltip title={<div>{val?.split(',')?.map(v => <div key={v}>{v}</div>)}</div>}>
          {val?.replace(/^(.{20}).+$/, (match: any, p1: string) => p1 + '...')}
        </Tooltip>
        )
        : '-'
      },
      {
        title: t('systemManagement.personManagement.table.column.userStatus'),
        dataIndex: 'userStatus',
        fixed: 'right',
        render: (_text, record) => (
          <PermissionTooltip
            permissionlist={permissionlist}
            title={t('systemManagement.personManagement.title')}
          >
            <Switch
              checked={record.userStatus === 'NORMAL'}
              checkedChildren={t('common.btn.normal')}
              unCheckedChildren={t('common.btn.lock')}
              loading={fetches[record.userId]?.loading}
              onChange={(checked) => {
                const flag = !checked
                toggleStatus({ userId: record.userId, flag })
              }}
              disabled={isOnlyRead}
            />
          </PermissionTooltip>
        ),
        width: '90px',
      }
    ]
    if (isLoginCtrlByManager) {
      baseColumns.splice(7, 0, ...[
        {
          title: t('systemManagement.personManagement.table.column.loginAuth'),
          dataIndex: 'loginAuth',
          width: 140
        },
        {
          title: t('systemManagement.personManagement.table.column.twoFactor'),
          dataIndex: 'twoFactor',
          width: 160
        },
        {
          title: t('systemManagement.personManagement.table.column.effectTime'),
          dataIndex: 'effectTime',
          width: 180,
          render:(value:any) => {
            if (value) {
              const realValue = JSON.parse(value)
              const { day, startTime, endTime } = realValue
              const dayLabel = effectTimeTypeOptions.find((item: any) => item.value === day)?.label ?? ''
              const start = moment(startTime, 'HH:mm:ss').format('HH:mm')
              const end = moment(endTime, 'HH:mm:ss').format('HH:mm')
              return `${dayLabel} ${start} - ${end}`
            }
            else return ''
          }
        },
      ])
    }
    return baseColumns
  }, [isLoginCtrlByManager])

  const deptColumnsWithAction: ColumnsType<UserEntity> = [
    ...columns,
    {
      title: '',
      fixed: 'right',
      key: 'action',
      render: (_text, record) => {
        const { systemAuthCode, permitIps, createFrom = null } = record
        const isAdmin = Service.permService.checkAuthWithCode(
          'SystemSettings',
          systemAuthCode,
        )
        return (
          <>
            {/*<LinkButton*/}
            {/*  onClick={() => {*/}
            {/*    setSelectRecord(record)*/}
            {/*    dispatch(showModal('UserPermDetaiModal'))*/}
            {/*  }}*/}
            {/*>*/}
            {/*  数据源连接权限*/}
            {/*</LinkButton>*/}
            <PermissionTooltip
              permissionlist={permissionlist}
              title={t('systemManagement.personManagement.title')}
            >
              <Button
                type='link'
                onClick={() => {
                  setMode('edit')
                  const { createFrom, auditRange } = record || {}
                  setCreateFrom(createFrom)
                  // 修改允许登录ip的显示格式
                  if (Array.isArray(record.permitIps)) {
                    form.setFieldsValue({ permitIps: permitIps.join(';') })
                  }
                  form.setFieldsValue({
                    ...record,
                    rangeType: auditRange?.length ? 'custom' : 'all',
                    auditRange: auditRange === null ? undefined : auditRange
                  })
                  form.setFieldsValue({ departmentId: record.departmentId + '' })
                  // 设置 用户自定义参数的值到form中
                  const result = record?.parameterValues?.reduce((res, item) => {
                    if (item.parameter && item.parameter?.tagName !== "SystemRole") {
                      res[item.parameter.tagName] = item?.value;
                    }
                    return res;
                  }, {} as { [key: string]: any });
                  form.setFieldsValue({ dynamicFieldName: result })
                  dispatch(showModal('AddUser'))
                }}
                disabled={isOnlyRead}
              >
                {t('common.btn.edit')}
              </Button>
              <Button
                type='link'
                onClick={() => {
                  setMode('editAndAdd')
                  // 修改允许登录ip的显示格式
                  if (Array.isArray(record.permitIps)) {
                    form.setFieldsValue({ permitIps: permitIps.join(';') })
                  }
                  setCreateFrom(record.createFrom)
                  form.setFieldsValue(record)
                  form.setFieldsValue({ departmentId: record.departmentId + '' })
                  // 设置 用户自定义参数的值到form中
                  const result = record?.parameterValues?.reduce((res, item) => {
                    if (item.parameter && item.parameter?.tagName !== "SystemRole") {
                      res[item.parameter.tagName] = item?.value;
                    }
                    return res;
                  }, {} as { [key: string]: any });
                  form.setFieldsValue({ dynamicFieldName: result })
                  dispatch(showModal('AddUser'))
                }}
                disabled={isOnlyRead}
              >
                {t('common.btn.copy')}
              </Button>

              {record?.twoFactorSetting === 'OTP' && (
                <Popconfirm
                  title={t('systemManagement.personManagement.reset_otp.tip')}
                  onConfirm={() => {
                    resetOtpKey({ userId: record.userId })
                      .then(() => {
                        message.success(t('common.message.reset.success'))
                      })
                      .catch(() => {
                        message.success(t('common.message.reset.error'))
                      })
                  }}
                  okButtonProps={{ danger: true }}
                >
                  <Button type='link' danger disabled={isOnlyRead}>{t('systemManagement.personManagement.btn.reset_otp')}</Button>
                </Popconfirm>
              )}

              {record.userId !== currentUserId && (
                <Popconfirm
                  title={t('systemManagement.personManagement.reset_user_password.tip')}
                  onConfirm={() => {
                    const randomPassword = getRandomUserPassword()
                    resetPassword({
                      userId: record.userId,
                      newP: randomPassword,
                    }).then(({ code, message }) => {
                      Modal.info({
                        title: t('systemManagement.personManagement.reset_password.success'),
                        content: (
                          <>
                            <Descriptions
                              className={styles.descriptions}
                              column={1}
                            >
                              <Descriptions.Item label={t('systemManagement.personManagement.table.column.user')}>
                                <Text
                                  strong
                                >{`${record.userName}（${record.userId}）`}</Text>
                              </Descriptions.Item>
                              <Descriptions.Item label={t('common.text.password')}>
                                {
                                  <Text strong copyable>
                                    {randomPassword}
                                  </Text>
                                }
                              </Descriptions.Item>
                            </Descriptions>
                            {/* <Alert
                              message={message}
                              type={code === 'SUCCESS' ? 'success' : 'error'}
                            /> */}
                          </>
                        ),
                        centered: true,
                      })
                    })
                  }}
                  okButtonProps={{ danger: true }}
                  disabled={isOnlyRead}
                >
                  <Button type='link' danger disabled={isOnlyRead}>
                    {t('common.btn.resetPassword')}
                  </Button>
                </Popconfirm>
              )}

              {!isAdmin && (
                <Popconfirm
                  title={t('systemManagement.personManagement.delete_user.tip')}
                  onConfirm={() => {
                    deleteUser(record.userId)
                      .then(() => {
                        message.success(t('common.message.delete_success'))
                        if (dept) {
                          dispatch(
                            fetchDeptUserList({
                              dept,
                              search: searchOutput,
                              pageNum: 0,
                              pageSize: organizationPageSize,
                            }),
                          )
                        }
                        dispatch(fetchOrgTreeData())
                      })
                      .catch(() => { })
                  }}
                  okButtonProps={{ danger: true }}
                >
                  <Button type='link' danger disabled={isOnlyRead}>{t('common.btn.delete')}</Button>
                </Popconfirm>
              )}
            </PermissionTooltip>
          </>
        )
      },
      width: '254px',
    },
  ]

  const groupColumnsWithAction: ColumnsType<UserEntity> = [
    ...columns,
    {
      title: '',
      fixed: 'right',
      key: 'action',
      render: (_text, record) => {
        const { userId } = record
        return (
          <RemoveButton
            operation={() => {
              const { id: groupId, orgType } = dept!
              return editGroupUsers({
                groupId: toInteger(groupId),
                orgType,
                removeUsers: [userId],
                addUsers: [],
              })
                .then(() => {
                  message.success(t('systemManagement.personManagement.moveout.success'))
                  if (dept) {
                    dispatch(
                      fetchDeptUserList({
                        dept,
                        search: searchOutput,
                        pageNum: 0,
                        pageSize: organizationPageSize,
                      }),
                    )
                  }
                })
                .catch(() => { })
            }}
          />
        )
      },
    },
  ]
  return (
    <div className={styles.userList}>
      <div className={styles.actions}>
        <span>
          <Input
            className={styles.searchInput}
            prefix={<SearchOutlined />}
            placeholder={t('systemManagement.personManagement.search_user.placeholder')}
            value={organizationSearch}
            onChange={(e) => {
              setSearch(e.target.value)
              setSearchOutput(e.target.value);
              dispatch(setOrganizationSearch(e.target.value));
            }}
            allowClear
          />
          {isGroup ? (
            <Button
              onClick={() => {
                // 这里再请求一下,主要是解决,TransferTable里面右table数据无法分页问题,暂时这样解决
                //这里要改成直接根据公司id查
                dispatch(
                  fetchDeptUserList({
                    dept,
                    pageNum: 0,
                    pageSize: 999,
                  })
                );
                dispatch(showModal("UserManagement"));
              }}
            >
              {t('systemManagement.personManagement.btn.members')}
            </Button>
          ) : (
            <>
              <PermissionTooltip
                permissionlist={permissionlist}
                title={t('systemManagement.personManagement.btn.users')}
              >
                <Dropdown
                  overlay={disableds || isOnlyRead ? <></> : menu}
                  className={disableds || isOnlyRead ? styles.dropDownDisabled : styles.dropDown}
                >
                  <Button>
                    <Space>
                      {t('common.btn.batchText')}
                      <UpOutlined />
                    </Space>
                  </Button>
                </Dropdown>
              </PermissionTooltip>
              {
                isOnlyRead ?
                  <Tooltip title={t('systemManagement.personManagement.noPerm',{val: roleNameList.join(", ")})}
                    overlayStyle={{ width: 180 }}
                  >
                    <Button
                      type="primary"
                      icon={<PlusSquareOutlined />}
                      disabled={isOnlyRead}
                      className={styles.ml10}
                    >
                      {t('systemManagement.personManagement.btn.addUser')}
                    </Button>
                  </Tooltip>
                  : <CreateButton
                    onClick={() => {
                      setMode("add");
                      form.resetFields()
                      dispatch(showModal("AddUser"));
                    }}
                    className={styles.ml10}
                  >
                    {t('systemManagement.personManagement.btn.addUser')}
                  </CreateButton>
              }
            </>
          )}
        </span>
      </div>
      <Table
        className={styles.tableTop}
        loading={userListLoading}
        dataSource={userList}
        rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected': ''}
        rowKey={(record) => record.userId}
        columns={isGroup ? groupColumnsWithAction : deptColumnsWithAction}
        rowSelection={isGroup ? undefined : rowSelection}
        pagination={{
          current: organizationPageNum + 1,
          total: organizationTotalNum,
          showTotal: (total: number) => t('systemManagement.personManagement.table.pagination.total',{val: total}),
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        onChange={(val) => {
          dept &&
            dispatch(
              fetchDeptUserList({
                dept,
                search: searchOutput,
                pageNum: Number(val.current) - 1,
                pageSize: Number(val.pageSize),
              })
            );
        }}
        scroll={{ x: true, y: `calc(100vh - 330px)` }}
      ></Table>
      {/* Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop display类型组件,本不应该用form传递的 */}
      <AddUserModal mode={mode} form={form} createFrom={createFrom} />
      <UserManagementModal />
      <UserPermDetaiModal record={selectRecord} />
      <BatchUpdateEffectTime userIds={selectedRowKeys} refresh={() => { setRequestUserList(!requestUserList) }} effectTimeId={effectTimeId} />
    </div>
  );
}
