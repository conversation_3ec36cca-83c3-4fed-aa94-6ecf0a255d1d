import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, Form, Switch, Input, Typography, message } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { useRequest } from 'src/hook'
import { LinkButton } from 'src/components'
import { getCasConfig, putCasConfig } from 'src/api'
import { SettingFormContext } from '../SettingFormContext'
import styles from './index.module.scss'

const { Text } = Typography

export const CasCard = () => {

  const { t } = useTranslation()
  const [editing, setEditing] = useState(false)
  const form = useContext(SettingFormContext) as FormInstance

  const { data: casConfig, loading, run: getConfig } = useRequest(getCasConfig)
  const { run: putConfig } = useRequest(putCasConfig, {
    manual: true,
    onSuccess: () => {
      getConfig()
    }
  })

  const setFormVals = useCallback((val:any) => {
    form.setFields([
      { name: 'casSwitch', value: casConfig.casSwitch },
      { name: 'casServerLoginUrl', value: casConfig.casServerLoginUrl },
      { name: 'casServerValidateUrl', value: casConfig.casServerValidateUrl },
      { name: 'casServerLogoutUrl', value: casConfig.casServerLogoutUrl },
      { name: 'casCQServerName', value: casConfig.casCQServerName },
      { name: 'casUserNameSuffix', value: casConfig.casUserNameSuffix },
    ])
  }, [casConfig, form])

  const handleSave = useCallback(() => {
    form
      .validateFields([
        'casSwitch',
        'casServerLoginUrl',
        'casServerValidateUrl',
        'casServerLogoutUrl',
        'casCQServerName',
        'casUserNameSuffix',
      ])
      .then((val) => {
        putConfig(val).then((val) => {
          if(!!val.resMsg){
            message.success(val.resMsg)
          }
        })
        setFormVals(val)
      })
      .then(() => {
        setEditing(false)
      }).catch((error) => { console.error(error) })
  }, [form, putConfig, setFormVals])

  useEffect(() => {
    if (!!casConfig) {
      setFormVals(casConfig)
    }
  }, [casConfig, form, setFormVals])

  const extraOperations = (
    <div className={styles.settingCardExtra}>
      {editing ? (
        [
          <LinkButton key="save" onClick={handleSave}>
            {t('common.btn.save')}
          </LinkButton>,
          <LinkButton
            key="cancel"
            onClick={() => {
              if(!!casConfig) {
                setFormVals(casConfig)
              }
              setEditing(false)
            }}
          >
            {t('common.btn.cancel')}
          </LinkButton>,
        ]
      ) : (
        <LinkButton onClick={() => setEditing(true)}>{t('common.btn.edit')}</LinkButton>
      )}
    </div>
  )

  const urlValidator = (_: any, value: string) => {
    if(!value) {
      return Promise.reject(t('systemManagement.system.ad.url.hint'))
    }
    const passed = value.startsWith('http://') || value.startsWith('https://')
    if (!passed) {
      return Promise.reject(t('systemManagement.system.cas.url.hint'))
    } else {
      return Promise.resolve()
    }
  }

  return (
    <section className="cq-card flow-card" id="CasCard" style={{border: 'none'}}>
      <div className={styles.singleTitle}>
        {/* <h3 className="cq-card__title">
          CAS 配置
          &nbsp; 
          <span className={styles.casSettingTip}>
            (保存后需要重启 User 服务才能生效)
          </span>
        </h3> */}
        {extraOperations}
      </div>
      <section className="card__content">
        <Card
          className={styles.settingCardContent}
          bordered={false}
          loading={loading}
        >
          {/* <Form> */}
          <Form.Item label={t('systemManagement.system.openLdap.openLdapSwitch')}>
            <Form.Item
              hidden={!editing}
              name="casSwitch"
              valuePropName="checked"
              noStyle
            >
              <Switch />
            </Form.Item>
            {!editing && (
              <Text>
                {!!casConfig && casConfig.casSwitch ? t('common.btn.on') : t('common.btn.off')}
              </Text>
            )}
          </Form.Item>

          <Form.Item label={t('systemManagement.system.ad.openLdapUrl')}>
           { editing && <Form.Item
              hidden={!editing}
              name="casServerLoginUrl"
              noStyle
              rules={[{ required: true, validator: urlValidator }]}
            >
              <Input placeholder='http(s)://example:port/cas/login'></Input>
            </Form.Item>}
            {!editing && (
              <Text>{!!casConfig && casConfig.casServerLoginUrl}</Text>
            )}
          </Form.Item>

          <Form.Item label={t('systemManagement.system.cas.casServerValidateUrl')}>
            {editing && <Form.Item
              hidden={!editing}
              name="casServerValidateUrl"
              noStyle
              rules={[{ required: true, validator: urlValidator }]}
            >
              <Input placeholder='http(s)://example:port/cas'></Input>
            </Form.Item>}
            {!editing && (
              <Text>{!!casConfig && casConfig.casServerValidateUrl}</Text>
            )}
          </Form.Item>

          <Form.Item label={t('systemManagement.system.cas.casServerLogoutUrl')}>
            {editing && <Form.Item
              hidden={!editing}
              name="casServerLogoutUrl"
              noStyle
              rules={[{ required: true, validator: urlValidator }]}
            >
              <Input placeholder='http(s)://example:port/cas/logout'></Input>
            </Form.Item>}
            {!editing && (
              <Text>{!!casConfig && casConfig.casServerLogoutUrl}</Text>
            )}
          </Form.Item>

          <Form.Item label={t('systemManagement.system.cas.casCQServerName')}>
           {editing && <Form.Item
              hidden={!editing}
              name="casCQServerName"
              noStyle
              rules={[{ required: true, validator: urlValidator }]}
            >
              <Input placeholder='http(s)://cloudquery:port'></Input>
            </Form.Item>}
            {!editing && (
              <Text>{!!casConfig && casConfig.casCQServerName}</Text>
            )}
          </Form.Item>

          <Form.Item label={t('systemManagement.system.cas.casUserNameSuffix')}>
           {editing && <Form.Item
              hidden={!editing}
              name="casUserNameSuffix"
              noStyle
            >
              <Input></Input>
            </Form.Item>}
            {!editing && (
              <Text>{!!casConfig && casConfig.casUserNameSuffix}</Text>
            )}
          </Form.Item>
          {/* </Form> */}
        </Card>
      </section>
    </section>
  )
}
