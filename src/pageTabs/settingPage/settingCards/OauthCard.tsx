import React, { useCallback, useContext, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, Form, Switch, Input, Typography, Radio, Tooltip } from 'antd'
import { FormInstance } from 'antd/lib/form'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { useRequest } from 'src/hook'
import { LinkButton } from 'src/components'
import { putOauthConfig, getOauthConfig, IOauthConfig } from 'src/api'
import { SettingFormContext } from '../SettingFormContext'
import styles from './index.module.scss'
import { isEmpty } from 'lodash'

const { Text } = Typography

export const OauthCard = () => {

  const { t } = useTranslation()
  const [editing, setEditing] = useState(false)
  const form = useContext(SettingFormContext) as FormInstance

  const {
    data: oauthConfig,
    loading,
    run: getConfig,
  } = useRequest(getOauthConfig)

  const { run: putConfig } = useRequest<IOauthConfig>(putOauthConfig, {
    manual: true,
    onSuccess: () => {
      getConfig()
      setEditing(false)
    },
  })
  const setFormVals = useCallback(
    (val: any) => {
      form.setFields([
        { name: 'oauth2Switch', value: oauthConfig.oauth2Switch || false },
        { name: 'clientId', value: oauthConfig.clientId },
        { name: 'clientSecret', value: oauthConfig.clientSecret },
        {
          name: 'userAuthorizationUrl',
          value: oauthConfig.userAuthorizationUrl,
        },
        { name: 'accessTokenUrl', value: oauthConfig.accessTokenUrl },
        { name: 'oauth2CQServerUrl', value: oauthConfig.oauth2CQServerUrl },
        { name: 'resourceUserInfoUrl', value: oauthConfig.resourceUserInfoUrl },
        { name: 'resourceUserInfoParma', value: oauthConfig.resourceUserInfoParma },
        {
          name: 'oauth2IsStandard',
          value: oauthConfig.oauth2IsStandard
        },
        {name: 'logoutUrl', value: oauthConfig.logoutUrl }
      ])
    },
    [oauthConfig, form],
  )

  useEffect(() => {
    if (!!oauthConfig) {
      setFormVals(oauthConfig)
    }
  }, [oauthConfig, form, setFormVals])

  const handleSave = () => {
    form
      .validateFields([
        'oauth2Switch',
        'clientId',
        'clientSecret',
        'userAuthorizationUrl',
        'accessTokenUrl',
        'oauth2CQServerUrl',
        'resourceUserInfoUrl',
        'resourceUserInfoParma',
        'oauth2IsStandard',
        'logoutUrl'
      ])
      .then((val) => {
        if (isEmpty(val)) return 
        putConfig(val)
      })
  }

  const extraOperations = (
    <div className={styles.settingCardExtra}>
      {editing ? (
        [
          <LinkButton key="save" onClick={handleSave}>
            {t('common.btn.save')}
          </LinkButton>,
          <LinkButton
            key="cancel"
            onClick={() => {
              if (!!oauthConfig) {
                setFormVals(oauthConfig)
              }
              setEditing(false)
            }}
          >
            {t('common.btn.cancel')}
          </LinkButton>,
        ]
      ) : (
        <LinkButton onClick={() => setEditing(true)}>{t('common.btn.edit')}</LinkButton>
      )}
    </div>
  )

  const urlValidator = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve()
    }
    const passed = value.startsWith('http://') || value.startsWith('https://')
    if (!passed) {
      return Promise.reject(t('systemManagement.system.cas.url.hint'))
    } else {
      return Promise.resolve()
    }
  }

  return (
    <section className="cq-card flow-card" id="OauthCard" style={{border: 'none'}}>
      <div className={styles.singleTitle}>
        {extraOperations}
      </div>
      <section className="card__content">
        <Card
          className={styles.settingCardContent}
          bordered={false}
          loading={loading}
        >
          <Form.Item label={t('systemManagement.system.openLdap.openLdapSwitch')}>
            <Form.Item
              hidden={!editing}
              name="oauth2Switch"
              valuePropName="checked"
              noStyle
            >
              <Switch />
            </Form.Item>
            {!editing && (
              <Text>
                {!!oauthConfig && oauthConfig.oauth2Switch ? t('common.btn.on') : t('common.btn.off')}
              </Text>
            )}
          </Form.Item>
          <Form.Item
            label={
              <>
                {t('systemManagement.system.oauth.oauth2IsStandard')}
                <Tooltip title={t('systemManagement.system.oauth.oauth2IsStandard.tip')}>
                  <QuestionCircleOutlined style={{ marginLeft: 2 }} />
                </Tooltip>
              </>
            }
          >
            {editing && (
              <Form.Item hidden={!editing} name="oauth2IsStandard" noStyle>
                <Radio.Group
                  defaultValue={true}
                  options={[
                    { label: t('systemManagement.system.oauth.oauth2IsStandard.coem'), value: true },
                    { label: t('systemManagement.system.oauth.oauth2IsStandard.client'), value: false },
                  ]}
                />
              </Form.Item>
            )}
            {!editing && (
              <Text>
                {!!oauthConfig && oauthConfig.oauth2IsStandard
                  ? t('systemManagement.system.oauth.oauth2IsStandard.coem')
                  : t('systemManagement.system.oauth.oauth2IsStandard.client')}
              </Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.oauth.userAuthorizationUrl')}>
            {editing && (
              <Form.Item
                hidden={!editing}
                name="userAuthorizationUrl"
                noStyle
                rules={[{ required: false, validator: urlValidator }]}
              >
                <Input placeholder="http(s)://example:port"></Input>
              </Form.Item>
            )}
            {!editing && (
              <Text>{!!oauthConfig && oauthConfig.userAuthorizationUrl}</Text>
            )}
          </Form.Item>

          <Form.Item label={t('systemManagement.system.oauth.accessTokenUrl')}>
            {editing && (
              <Form.Item
                hidden={!editing}
                name="accessTokenUrl"
                noStyle
                rules={[{ required: false, validator: urlValidator }]}
              >
                <Input placeholder="http(s)://example:port"></Input>
              </Form.Item>
            )}
            {!editing && (
              <Text>{!!oauthConfig && oauthConfig.accessTokenUrl}</Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.oauth.oauth2CQServerUrl')}>
            {editing && (
              <Form.Item
                hidden={!editing}
                name="oauth2CQServerUrl"
                noStyle
                rules={[{ required: false, validator: urlValidator }]}
              >
                <Input placeholder="http(s)://cloudquery:port/waitOauthLogin"></Input>
              </Form.Item>
            )}
            {!editing && (
              <Text>{!!oauthConfig && oauthConfig.oauth2CQServerUrl}</Text>
            )}
          </Form.Item>

          <Form.Item label={t('systemManagement.system.oauth.clientId')}>
            {editing && (
              <Form.Item
                hidden={!editing}
                name="clientId"
                noStyle
              >
                <Input></Input>
              </Form.Item>
            )}
            {!editing && <Text>{!!oauthConfig && oauthConfig.clientId}</Text>}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.oauth.clientSecret')}>
            {editing && (
              <Form.Item hidden={!editing} name="clientSecret" noStyle>
                <Input></Input>
              </Form.Item>
            )}
            {!editing && (
              <Text>{!!oauthConfig && oauthConfig.clientSecret}</Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.oauth.resourceUserInfoUrl')}>
            {editing && (
              <Form.Item
                hidden={!editing}
                name="resourceUserInfoUrl"
                noStyle
                rules={[{ required: false, validator: urlValidator }]}
              >
                <Input placeholder="http(s)://example:port"></Input>
              </Form.Item>
            )}
            {!editing && (
              <Text>{!!oauthConfig && oauthConfig.resourceUserInfoUrl}</Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.oauth.resourceUserInfoParma')}>
            {editing && (
              <Form.Item hidden={!editing} name="resourceUserInfoParma" noStyle>
                <Input />
              </Form.Item>
            )}
            {!editing && (
              <Text>{!!oauthConfig && oauthConfig.resourceUserInfoParma}</Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.oauth.logoutUrl')}>
            {editing && <Form.Item
              hidden={!editing}
              name="logoutUrl"
              noStyle
              rules={[{ required: true, validator: urlValidator }]}
            >
              <Input placeholder='http(s)://example:port/oauth/logout'></Input>
            </Form.Item>}
            {!editing && (
              <Text>{!!oauthConfig && oauthConfig.logoutUrl}</Text>
            )}
          </Form.Item>
        </Card>
      </section>
    </section>
  )
}
