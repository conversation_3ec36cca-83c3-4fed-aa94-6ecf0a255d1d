import React, { useCallback, useContext, useEffect, useState, useMemo } from 'react'
import {
  Card,
  message,
  Form,
  Typography,
  Switch,
  Tooltip,
  InputNumber,
  Radio,
  Input,
  Checkbox,
  Button,
} from 'antd'
import { useTranslation } from 'react-i18next'
import { QuestionCircleOutlined, ExclamationCircleOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons'
import { Modal } from 'antd'
import { useRequest } from 'src/hook'
import { FormInstance } from 'antd/lib/form'
import { LinkButton } from 'src/components'
import { SettingFormContext } from '../SettingFormContext'
import { getSysPasswordPolicy, updateSysPasswordPolicy } from 'src/api'
import styles from './index.module.scss'
import { passwordSpecialCharacterValidator, passwordValidator, passwordValidatorSelf } from 'src/util/nameValidator'

const { Text } = Typography

export const PasswordCard = () => {

  const { t } = useTranslation()
  const [editing, setEditing] = useState(false)
  const [isSecretPassword, setIsSecretPassword] = useState(true)
  const [messageDayFlag, setMessageDayFlag] = useState(false)
  const form = useContext(SettingFormContext) as FormInstance

  const { loading, run } = useRequest(getSysPasswordPolicy, { manual: true })
  const [settingData, setSettingData] = useState<any>();
  const [userPasswordStrong, setUserPasswordStrong] = useState<string>('systemBuiltPwdStrong')

  // 保存[登出]状态
  // useEffect(() => {
  //   if (settingData) {
  //     dispatch(setExitPrompt(settingData[8] as boolean))
  //   }
  // }, [dispatch, settingData])

  const checkValue = (arr: any) => {
    let valueArr: any = [];
    for (let key in arr) {
      if (arr.hasOwnProperty(key)) {
        if (arr[key]) {
          valueArr[valueArr.length] = key;
        }
      }
    }
    return valueArr;
  }

  const setFormVal = useCallback(() => {
    if (settingData) {
      form.setFields([
        {
          name: 'maxDay',
          value: settingData?.maxDay,
          errors: undefined,
        },
        {
          name: 'noRepeatCount',
          value: settingData?.noRepeatCount,
          errors: undefined,
        },
        {
          name: 'expireWarning',
          value: settingData?.expireWarningDay ? true : false,
        },
        {
          name: 'expireWarningDay',
          value: settingData?.expireWarningDay,
          errors: undefined,
        },
        {
          name: 'maxFailureCount',
          value: settingData?.maxFailureCount,
          errors: undefined,
        },
        {
          name: 'lockoutMinute',
          value: settingData?.lockoutMinute,
          errors: undefined,
        },
        {
          name: 'systemPasswordStrong',
          value: settingData?.systemPasswordStrong,
          errors: undefined,
        },
        {
          name: 'passwordMin',
          value: settingData?.passwordMin,
          errors: undefined,
        },
        {
          name: 'passwordMax',
          value: settingData?.passwordMax,
          errors: undefined,
        },
        {
          name: 'pwdInclude',
          value: checkValue(
            {
              "containDigits": settingData?.containDigits,
              "containUpperLetters": settingData?.containUpperLetters,
              "containLowerLetters": settingData?.containLowerLetters,
              "containSymbols": settingData?.containSymbols,
            }),
          errors: undefined,
        },
        {
          name: 'defaultPassword',
          value: settingData?.defaultPassword,
          errors: undefined,
        },
      ])
      setMessageDayFlag(settingData?.expireWarningDay ? true : false)
    }
  }, [form, settingData])

  useEffect(() => {
    setFormVal()
  }, [settingData, setFormVal, form])

  let param: string[] = [];
  useEffect(() => {
    run().then(res => {
      setSettingData(res);
      if (res.systemPasswordStrong || !Object.keys(res).includes("systemPasswordStrong")) {
        setUserPasswordStrong('systemBuiltPwdStrong')
      } else {
        setUserPasswordStrong('customization')
      }
    })
  }, [])

  useEffect(() => {
    if (userPasswordStrong === "customization") {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      param = ['passwordMax', 'passwordMin', 'pwdInclude']
    }
  }, [userPasswordStrong])


  const handleSave = useCallback(() => {
    Modal.confirm({
      content: t('systemManagement.system.ps.maxDay.tip'),
      okText: t('common.btn.ok'),
      cancelText: t('common.btn.cancel'),
      onOk() {
        form
          .validateFields([
            'maxDay',
            'noRepeatCount',
            'expireWarning',
            'expireWarningDay',
            'maxFailureCount',
            'lockoutMinute',

            'userPwdStrong',
            ...param,
            'defaultPassword',
          ])
          .then((vals) => {
            let newParams: any
            if (userPasswordStrong === 'customization') {
              newParams = {
                containDigits: vals?.pwdInclude.includes('containDigits') ? true : false,
                containUpperLetters: vals?.pwdInclude.includes('containUpperLetters') ? true : false,
                containLowerLetters: vals?.pwdInclude.includes('containLowerLetters') ? true : false,
                containSymbols: vals?.pwdInclude.includes('containSymbols') ? true : false,
              }
            } else {
              newParams = {}
            }

            let params = {
              ...vals,
              expireWarningDay: vals?.expireWarning ? vals?.expireWarningDay : 0,

              systemPasswordStrong: userPasswordStrong === 'systemBuiltPwdStrong' ? true : false,
              ...newParams,

            } as any
            delete params.expireWarning
            delete params.userPwdStrong
            delete params.pwdInclude


            updateSysPasswordPolicy(params).then((val) => {
              message.success(t('common.message.editSuccess'))
              run().then(res => {
                setSettingData(res);
                if (res.systemPasswordStrong) {
                  setUserPasswordStrong('systemBuiltPwdStrong')
                } else {
                  setUserPasswordStrong('customization')
                }
              })
            })
              .catch(() => { })
              .finally(() => setEditing(false))
          })
        },
        onCancel() {
          // logout()
        },
      });
    }, [form, run, userPasswordStrong])
    
  // 用户密码强度radio
  const onChange = (e: any) => {
    const radioVal = e.target.value;
    setUserPasswordStrong(radioVal);
  };

  // 密码中需包含checkbox
  const options = [
    { label: t('common.text.number'), value: 'containDigits' },
    { label: t('common.text.containUpperLetters'), value: 'containUpperLetters' },
    { label: t('common.text.containLowerLetters'), value: 'containLowerLetters' },
    { label: t('common.text.containSymbols'), value: 'containSymbols' },
  ];


  const extraOperations = (
    <div className={styles.settingCardExtra}>
      {editing ? (
        [
          <LinkButton key="save" onClick={handleSave}>
            {t('common.btn.save')}
          </LinkButton>,
          <LinkButton
            key="cancel"
            onClick={() => {
              setFormVal()
              setEditing(false)
            }}
          >
            {t('common.btn.cancel')}
          </LinkButton>,
        ]
      ) : (
        <LinkButton onClick={() => setEditing(true)}>{t('common.btn.edit')}</LinkButton>
      )}
    </div>
  )

  const renderCustomization = useMemo(() => {
    return (
      <>
        <Form.Item label={t('systemManagement.system.ps.passwordMin')} name={t('systemManagement.system.ps.passwordMin')} rules={[{ required: true }]}>
          <Form.Item
            name="passwordMin"
            hidden={!editing}
            noStyle
            rules={[
              {
                validator: (_, value) => {
                 
                  const isPrescribedLimit =  value >= 1 && value <= 19;
                  //整数
                  const isIntegerValue = value && Number.isInteger(Number(value));
                
                  if (!(isPrescribedLimit && isIntegerValue)) {
                    return Promise.reject(t('systemManagement.system.ps.passwordMin.plac'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <InputNumber
              min={1}
              max={19}
              placeholder="> 0"
              style={{ width: '18%' }}
            />
          </Form.Item>
          {!editing && (
            //@ts-ignore
            <Text>{settingData && settingData?.passwordMin}</Text>
          )}
        </Form.Item>
        <Form.Item label={t('systemManagement.system.ps.passwordMax')} name={t('systemManagement.system.ps.passwordMax')} rules={[{ required: true }]}>
          <Form.Item
            name="passwordMax"
            hidden={!editing}
            noStyle
            dependencies={['passwordMin']}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
               
                  const isPrescribedLimit = value >= 2 && value <= 20;
                  //整数
                  const isIntegerValue = value && Number.isInteger(Number(value));
                  //passwordMin
                  const passwordMin = getFieldValue('passwordMin');
              
                  if (!(isPrescribedLimit && isIntegerValue && passwordMin < value)) {
                    return Promise.reject(t('systemManagement.system.ps.passwordMin.hint2'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <InputNumber
              min={2}
              max={20}
              placeholder={`> ${t('systemManagement.system.ps.min')}`}
              style={{ width: '18%' }}
            />
          </Form.Item>
          {!editing && (
            //@ts-ignore
            <Text>{settingData && settingData?.passwordMax}</Text>
          )}
        </Form.Item>
        <Form.Item label={t('systemManagement.system.ps.pwdInclude')} name={t('systemManagement.system.ps.pwdInclude')} rules={[{ required: true }]} >
          <Form.Item
            name="pwdInclude"
            noStyle
            rules={[
              {
                required: true,
                message: t('common.search.select.placeholder'),
              },
            ]}
          >
            <Checkbox.Group disabled={!editing} options={options} />
          </Form.Item>
        </Form.Item>
      </>
    )
  }, [editing, options, settingData]);

  const renderPasswordRules = useMemo(() => {
    if (userPasswordStrong === 'systemBuiltPwdStrong') {
      return [{ validator: passwordValidator }, { validator: passwordSpecialCharacterValidator }]
    } else if (userPasswordStrong === 'customization') {
      return [({ getFieldValue }: { getFieldValue: Function }) => ({
        validator(_rule: any, value: string, callback: any) {
          passwordValidatorSelf(_rule, value, callback,
            {
              passwordMax: getFieldValue('passwordMax') ? getFieldValue('passwordMax') : 0,
              passwordMin: getFieldValue('passwordMin') ? getFieldValue('passwordMin') : 0,
              containDigits: getFieldValue('pwdInclude')?.includes('containDigits') ? true : false,
              containUpperLetters: getFieldValue('pwdInclude')?.includes('containUpperLetters') ? true : false,
              containLowerLetters: getFieldValue('pwdInclude')?.includes('containLowerLetters') ? true : false,
              containSymbols: getFieldValue('pwdInclude')?.includes('containSymbols') ? true : false,
            })
        },
      }),]
    }
  }, [userPasswordStrong])

  return (
    <section className="cq-new-card flow-card" id="PasswordCard">
      <div className="cq-card__headerbar">
        <h3 className="cq-card__title">{t('systemManagement.system.password')}</h3>
        {extraOperations}
      </div>
      <section className="card__content">
        <Card
          className={styles.settingCardContent}
          bordered={false}
          loading={loading}
        >
          <Form.Item label={
            <>
              {t('systemManagement.system.ps.maxDay')}
              <Tooltip title={t('systemManagement.system.ps.maxDay.extra')}>
                <ExclamationCircleOutlined style={{ paddingLeft: 5, color: '#ff4d4f' }} />
              </Tooltip>
            </>
          }>
            <Form.Item
              name="maxDay"
              noStyle
              hidden={!editing}
              rules={[{ 
                min: 1,message: t('systemManagement.system.ps.maxFailureCount.hint'), type: 'number',
                transform: (v) => Number(v)
              }]}
            >
              <InputNumber 
                placeholder={t('systemManagement.system.ps.maxDay.tip2')} 
                min={1}
                style={{ width: '100%' }}
                precision={0}
              />
            </Form.Item>
            {!editing && <Text>{settingData && t('common.numberDispaly',{val: settingData?.maxDay}) }</Text>}
          </Form.Item>
          <Form.Item
            label={
              <>
                {t('systemManagement.system.ps.noRepeatCount')}
                <Tooltip title={t('systemManagement.system.ps.noRepeatCount.extra')}>
                  <QuestionCircleOutlined style={{ paddingLeft: 5 }} />
                </Tooltip>
              </>
            }
          >
            <Form.Item
              name={'noRepeatCount'}
              noStyle
              hidden={!editing}
              rules={[
                { 
                min: 1,max: 10,message: t('systemManagement.system.ps.noRepeatCount.hint'), type: 'number',
                transform: (v) => Number(v)
                }]}
            >
              <InputNumber
                min={0}
                max={10}
                placeholder={t('systemManagement.system.ps.noRepeatCount.tip')}
                style={{ width: '100%' }}
                precision={0}
              />
            </Form.Item>
            {!editing && (
              <Text>{settingData && settingData?.noRepeatCount}</Text>
            )}
          </Form.Item>
          <Form.Item
            label={
              <>
                {t('systemManagement.system.ps.expireWarning')}
                <Tooltip title={t('systemManagement.system.ps.expireWarning.extra')}>
                  <QuestionCircleOutlined style={{ paddingLeft: 5 }} />
                </Tooltip>
              </>
            }
          >
            <Form.Item
              name="expireWarning"
              hidden={!editing}
              noStyle
              valuePropName="checked"
            >
              <Switch
                onChange={(val) => {
                  form.setFieldsValue({
                    expireWarningDay: val
                      ? Math.max(settingData?.expireWarningDay, 1)
                      : 0,
                  })
                  setMessageDayFlag(val)
                }}
              />
            </Form.Item>
            {!editing && (
              <Text>
                {settingData && settingData?.expireWarningDay ? t('common.btn.on') : t('common.btn.off')}
              </Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.ps.expireWarningDay')}>
            <Form.Item
              name="expireWarningDay"
              hidden={!editing}
              noStyle
              rules={[
                {
                  min: messageDayFlag ? 1 : 0,
                  max: 15,
                  message: t('systemManagement.system.ps.expireWarningDay.hint'), 
                  type: 'number',
                  transform: (v) => Number(v)
                },
              ]}
            >
              <InputNumber
                min={messageDayFlag ? 1 : 0}
                max={15}
                placeholder={t('systemManagement.system.ps.expireWarningDay.tip')}
                style={{ width: '100%' }}
                precision={0}
                disabled={editing ? (messageDayFlag ? false : true) : false}
              />
            </Form.Item>
            {!editing && (
              //@ts-ignore
              <Text>{settingData && settingData?.expireWarningDay}</Text>
            )}
          </Form.Item>
          {/* 导出任务数 */}
          <Form.Item label={t('systemManagement.system.ps.maxFailureCount')}>
            <Form.Item
              name="maxFailureCount"
              hidden={!editing}
              noStyle
              rules={[{ 
                min: 1,message: t('systemManagement.system.ps.maxFailureCount.hint'), type: 'number',
                transform: (v) => Number(v) }]}
            >
              <InputNumber
                min={1}
                placeholder={t('systemManagement.system.ps.maxFailureCount.tip')}
                style={{ width: '100%' }}
                precision={0}
              />
            </Form.Item>
            {!editing && (
              //@ts-ignore
              <Text>{settingData && t('common.numberDispaly',{val: settingData?.maxFailureCount})}</Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.ps.lockoutMinute')}>
            <Form.Item
              name="lockoutMinute"
              hidden={!editing}
              noStyle
              rules={[
                {
                  min: 1,
                  max:2628000,
                  message: t('systemManagement.system.ps.lockoutMinute.hint'),
                  type: 'number',
                  pattern: /^[1-9][1-9]*$/,
                },
              ]}
            >
              <InputNumber
                min={1}
                max={2628000}
                placeholder={t('systemManagement.system.ps.lockoutMinute.tip')}
                style={{ width: '100%' }}
                precision={0}
              />
            </Form.Item>
            {!editing && (
              //@ts-ignore
              <Text>{settingData && t('common.numberDispaly',{ val: settingData?.lockoutMinute })}</Text>
            )}
          </Form.Item>
          <Form.Item label={t('systemManagement.system.ps.userPwdStrong')} name={t('systemManagement.system.ps.userPwdStrong')}>
            {editing ?
              <Form
                initialValues={{
                  'userPwdStrong': userPasswordStrong,
                }}
                name="userPwdStrong"
                onFieldsChange={() => { form.validateFields(["defaultPassword"]); }}
              >
                <Form.Item
                  name="userPwdStrong"
                  label="userPwdStrong"
                  hidden={!editing}
                  noStyle
                  rules={[
                    {
                      message: t('common.search.select.placeholder'),
                    },
                  ]}
                >
                  <Radio.Group onChange={onChange} value={userPasswordStrong}>
                    <Radio value={'systemBuiltPwdStrong'}>
                      {t('systemManagement.system.ps.userPwdStrong.tip2')}
                      <Tooltip title={t('systemManagement.system.ps.userPwdStrong.tip')}>
                        <QuestionCircleOutlined style={{ paddingLeft: 5 }} />
                      </Tooltip>
                    </Radio>
                    <Radio value={'customization'}>{t('systemManagement.personManagement.addUserModal.rangeType.custom')}</Radio>
                  </Radio.Group>
                </Form.Item>
              </Form>
              : <Text>
                {settingData && (settingData?.systemPasswordStrong || !Object.keys(settingData).includes("systemPasswordStrong")) ?
                  t('systemManagement.system.ps.userPwdStrong.tip2')
                  : t('systemManagement.personManagement.addUserModal.rangeType.custom')}
              </Text>}
          </Form.Item>
          {
            (editing && userPasswordStrong === 'customization') ||
              (!editing && (!settingData?.systemPasswordStrong || !Object.keys(settingData).includes("systemPasswordStrong"))) ?
              renderCustomization
              : <></>
          }
          <Form.Item label={t('systemManagement.system.ps.defaultPassword')}>
            {editing ?
              <Form.Item
                name='defaultPassword'
                hidden={!editing}
                noStyle
                dependencies={['passwordMin', 'passwordMax', 'pwdInclude', 'userPwdStrong']}
                rules={
                  renderPasswordRules
                }
              >
                <Input.Password
                  placeholder={t('common.search.input.placeholder')}
                  style={{ width: '100%' }}
                />
              </Form.Item>
              : <Text>
                {
                  isSecretPassword ?
                    <>******<Button type='link' onClick={() => { setIsSecretPassword(false) }}><EyeInvisibleOutlined /></Button></> :
                    <>{settingData?.defaultPassword}<Button type='link' onClick={() => { setIsSecretPassword(true) }}><EyeOutlined /></Button></>
                }
              </Text>}
          </Form.Item>
        </Card>
      </section>
    </section>
  )
}
