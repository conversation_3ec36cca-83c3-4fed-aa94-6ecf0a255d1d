import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Card, Form, message, Progress, Tooltip, Typography, Upload } from 'antd'
import { useRequest, useSelector } from 'src/hook'
import { deleteLicense, getAuthorizationMessage, getLicenseList, getLicenseStatus, updateLicense, uploadLicense } from 'src/api'
import {
  handleHttpError
} from 'src/api/customFetch/_util'
import styles from './index.module.scss'
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { ModalUpdateLicenseProgress } from './components/ModalUpdateLicenseProgress'

const { Text } = Typography

export const AuthorizationCard = () => {

  const { t } = useTranslation()
  const { productGrade } = useSelector(state => state.login)
  const [ visibleProgress, setVisibleProgress ] = useState<boolean>(false)
  const [ fileList, setFileList ] = useState<any>([])
  const [ canEditLicense, setCanEditLicense ] = useState<boolean>(false) // 是否可以上传、删除证书
  const [ canUpdate, setCanUpdate ] = useState<boolean>(false) // 是否可以更新

  const lisenceFileType = 'D2C';
  let getLicenseStatusIntervalId = useRef<any>(null);

  const {
    data,
    loading,
    run,
  } = useRequest(getAuthorizationMessage, {
    manual: true,
  })

  // 清除定时器
  const clearIntervalFuc = () => {
    if (getLicenseStatusIntervalId?.current) {
      clearInterval(getLicenseStatusIntervalId.current)
    }
  }

  // 获取证书列表
  const {
    data: licenseList,
    run: getLicenseListRun,
  } = useRequest(getLicenseList, {
    manual: true,
    onSuccess: (res) => {
      if (res?.length > 0) {
        getLicenseStatusRun()
        getLicenseStatusInterval()
        setFileList(() => {
          return licenseList.map((item: string, index: number) => {
            return { uid: index?.toString(), name: item }
          })
        })
      } else {
        setCanEditLicense(true)
        setCanUpdate(false)
        setFileList([])
      }
    },
    onError: () => {
      setCanEditLicense(true)
      setFileList([])
    }
  })

  // 删除证书
  const {
    run: deleteLicenseRun,
  } = useRequest(deleteLicense, {
    manual: true,
    onSuccess: (res) => {
      message.success(t('systemManagement.system.auth.deleteLicense.success'))
      getLicenseListRun()
    },
    onError: (e) => {
      console.log('删除证书失败', e);
      message.error(t('systemManagement.system.auth.deleteLicense.error'))
    },
  })

  // 更新证书
  const {
    run: updateLicenseRun,
  } = useRequest(updateLicense, {
    manual: true,
    onError: (e) => {
      console.log('证书更新失败：', e);
      message.error(t('systemManagement.system.auth.updateLicense.error'))
    },
  })

  // 获取证书更新状态
  const {
    data: licenseStatusData,
    run: getLicenseStatusRun,
  } = useRequest(getLicenseStatus, {
    manual: true,
    onSuccess: (res) => {
      // 正在走更新证书流程，更新未结束
      if (res?.isExecuting || res?.statusStr === t('systemManagement.system.auth.updateLicense.loading')) {
        setCanEditLicense(false)
        setCanUpdate(false)
      } else {
        setCanEditLicense(true)
        if (licenseList?.length > 0) {
          setCanUpdate(true)
        }
        clearIntervalFuc()
      }
    },
    onError: (e, params) => {
      console.log('获取证书更新状态失败', e, params);
    }
  })

  useEffect(() => {
    run()
    getLicenseListRun()
    return () => {
      clearIntervalFuc()
      setVisibleProgress(false)
      setFileList([])
      setCanEditLicense(false)
      setCanUpdate(false)
    }
  }, [])

  const getLicenseStatusInterval = () => {
    // 重复获取证书更新状态, 显示最新更新进度信息
    clearIntervalFuc()
    getLicenseStatusIntervalId.current = setInterval(getLicenseStatusRun, 2000)
  }

  const handleFileDownload = async () => {
    try {
      // 校验许可证服务是否可用
      // 不使用/license/generate，因为会使已上传的证书更新失效
      const response = await fetch('/license/list', {
        method: 'GET',
      });

      if (response.ok) {
        // 请求成功，打开新窗口进行下载许可证
        window.open('/license/generate', '_blank');
        getLicenseStatusRun()
      } else {
        handleHttpError(response, t('systemManagement.system.auth.license.uninstalled'));
      }
    } catch (error) {
      console.error('请求出错:', error);
    }
  }

  const handleFileUpload = (file: any) => {
    const params = {file: [file]}
    uploadLicense(params).then((res: any)=>{
      message.success(t('common.btn.updateSuccess'))
    }).catch((err: any)=>{
      console.error('上传失败', err)
      setFileList([])
    }).finally(()=>{
      getLicenseListRun()
    })
  }

  const handleLicenseUpdate = () => {
    setCanEditLicense(false)
    setCanUpdate(false)
    updateLicenseRun()
    getLicenseStatusRun()
    getLicenseStatusInterval()
  }

  const uploadProps = {
    fileList: fileList,
    beforeUpload: (file: any) => {
      if (!file) {
        message.error(t('common.downloadTemplate.selectFile')); 
        return false;
      }
      const name = file.name?.toString()!
      const suffix = name?.split('.')?.pop()!;
      // 校验是否为 D2C 类型文件
      if(suffix !== lisenceFileType){
        message.error(t('systemManagement.system.auth.fileType.tip',{lisenceFileType}))
        return false;
      }
      handleFileUpload(file)
      return false;
    },
    onRemove: (info: any) => {
      if (info?.name) {
        deleteLicenseRun(info?.name)
      } else {
        message.error(t('systemManagement.personManagement.delete.error'))
      }
      return false;
    },
  }

  const getProgressStatus = (status: string | undefined) => {
    if (status === t('common.message.uploadSuccess')) return 'success'
    if (status === t('common.message.updateError')) return 'exception'
    return 'normal'
  }

  return (
    <>
      <section className="cq-new-card flow-card" id="AuthorizationCard">
        <div className="cq-card__headerbar">
          <h3 className="cq-card__title">{t('systemManagement.system.auth')}</h3>
        </div>
        <section className="card__content">
          <Card
            className={styles.settingCardContent}
            bordered={false}
            loading={loading}
          >
            <Form.Item label={t('systemManagement.system.auth.startTime')}>
              <Text>{data?.startTime}</Text>
            </Form.Item>
            <Form.Item label={t('systemManagement.system.auth.endTime')}>
              <Text>{data?.endTime}</Text>
            </Form.Item>
            <Form.Item label={t('systemManagement.system.auth.databaseNumber')}>
              <Text>{ t('common.numberDispaly', {val: data?.databaseNumber})}</Text>
            </Form.Item>
            <Form.Item label={t('systemManagement.system.auth.connectionNumber')}>
              <Text>{data?.connectionNumber}</Text>
            </Form.Item>
            <Form.Item label={t('systemManagement.system.auth.databaseType')}>
              <Text>{data?.databaseType?.join(', ')}</Text>
            </Form.Item>
            <Form.Item label={t('systemManagement.system.auth.productGrade')}>
              <Text>{productGrade}</Text>
            </Form.Item>

            <Form.Item
              label={
                <>
                  {t('systemManagement.system.auth.licenseDownload')}
                  <Tooltip
                    title={t('systemManagement.system.auth.licenseDownload.extra')}
                  ><QuestionCircleOutlined />
                  </Tooltip>
                </>
              }
            >
              <Button
                type="primary"
                onClick={handleFileDownload}
              >
                {t('common.btn.download')}
              </Button>
            </Form.Item>
            <Form.Item label={t('systemManagement.system.auth.updateLicense')} className={styles.updateLicense}>
              <Upload {...uploadProps} disabled={!canEditLicense}>
                <Button type="primary" disabled={!canEditLicense}><PlusOutlined />{t('systemManagement.system.auth.newLicense')}</Button>
              </Upload>
              <span className={styles.updateBtnWrap}>
                {
                  canUpdate
                    ? <Button
                      type="primary"
                      onClick={handleLicenseUpdate}
                    >
                      {t('systemManagement.system.auth.updates')}
                    </Button>
                    : <Tooltip title={licenseList?.length > 0? "" : t('systemManagement.system.auth.notUpdate')}>
                        <Button disabled >{t('systemManagement.system.auth.updates')}</Button>
                      </Tooltip>
                }
                {
                  licenseList?.length > 0 &&
                  <span className={styles.progressWrap}>
                    <Progress
                      size="small"
                      showInfo={licenseStatusData && licenseStatusData?.statusStr !== t('common.message.init')}
                      percent={(licenseStatusData?.progress || 0)}
                      status={getProgressStatus(licenseStatusData?.statusStr)}
                    />
                    <Button
                      type="link"
                      className={styles.showBtn}
                      disabled={!licenseStatusData || licenseStatusData?.statusStr === t('common.message.init')}
                      onClick={() => {
                        if (!licenseStatusData || licenseStatusData?.statusStr === t('common.message.init')) {
                          return
                        }
                        setVisibleProgress(true)
                      }}
                    >
                      {(!licenseStatusData || licenseStatusData?.statusStr === t('common.message.init')) ? t('systemManagement.system.auth.toBeUpdated') : t('common.btn.view')}
                    </Button>
                  </span>
                }
              </span>
            </Form.Item>
          </Card>
          {/* 证书更新状态详情 */}
          <ModalUpdateLicenseProgress
            visible={visibleProgress}
            callback={() => setVisibleProgress(false)}
          />
        </section>
      </section>
    </>
  )
}