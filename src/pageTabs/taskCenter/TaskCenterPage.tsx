/* eslint-disable jsx-a11y/no-access-key */
/* 任务中心 */
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Button, DatePicker, Dropdown, Menu, Modal, Space, Tabs } from 'antd'
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary';
import { BdNavigation } from 'src/components';
import styles from './index.module.scss'
import { useLocation } from 'react-router-dom';
import classnames from 'classnames';
import { DownOutlined, ExclamationCircleOutlined, RedoOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons';
import { TabsPage } from './tabsPage';
import { 
  queryAndSetExportList, 
  queryAndSetImportList, 
  querydataReplicationDicList, 
  queryBatchSqlList, 
  querySyncDictList, 
  setTableInfo, 
  setTabsKey,
  setExportParamsFromMessage
 } from './taskCenterSlice';
import { useDispatch, useSelector } from 'src/hook';
import { BatchExecuteStatus, HandleExportStatus, HandleImportStatus, SyncDictStatus, TaskCenterTabsItems, TaskTypeMap } from 'src/constants';
import moment, { Moment } from 'moment';
import { EventValue } from "rc-picker/lib/interface";
import { cloneDeep, isEmpty } from 'lodash';
import { checkBatchFile } from 'src/api';
import { fetchGet } from 'src/api/customFetch';
import { handleDownload } from 'src/util';
import { useTranslation } from 'react-i18next';
import SyncResourceTreeSelector from './components/ResourceTreeSelector';

interface QueryParams {
  pageNumber?: number | string | undefined,
  pageSize?: number | string | undefined,
  dataPicker?: [EventValue<Moment>, EventValue<Moment>] | undefined | null,
  startAt?: string,
  endAt?: string,
}

const { RangePicker } = DatePicker;
const { confirm } = Modal;

export const TaskCenterPage = (props: any) => {

  const location = useLocation<any>();
  const dispatch = useDispatch();
  const tabsPageRef = useRef(null);
  const { tabsKey, exportParamsFromMessage } = useSelector(state => state.myTaskCenter);
  const initPagination = { pageNumber: 1, pageSize: 10 };
  const [activeKey, setActiveKey] = useState('handleExport');
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const [allowBatchStop, setAllowBatchStop] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>();
  const [queryParams, setQueryParams] = useState<QueryParams>({
    ...initPagination,
    //默认使用跳转日期设置
    dataPicker: exportParamsFromMessage?.dataPicker || [moment().subtract(30, 'days'), moment()],
  });

  const { t } = useTranslation();

  useEffect(()=>{
    if(tabsKey === activeKey) return
    setActiveKey(tabsKey)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabsKey])

  // 处理任务tab关闭后，重新打开还有消息入口进来的id参数等信息
  useEffect(()=>{
    return ()=>{
      // 组件销毁清空消息参数
      dispatch(setExportParamsFromMessage({}))
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[])

  // 请求 任务Table 数据
  const queryData = async () => {
    let paraments: any = { ...queryParams };
    let keys = Object.keys(paraments);
    if (exportParamsFromMessage?.taskId && activeKey === 'handleExport') {
      paraments.taskId = exportParamsFromMessage?.taskId;
      paraments.dataPicker = exportParamsFromMessage?.dataPicker;
    }
    if (paraments?.dataPicker?.length) {
      paraments.startAt = moment(paraments?.dataPicker?.[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
      paraments.endAt = moment(paraments?.dataPicker?.[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }
   
    let params = cloneDeep(paraments);
    params = {
      sortField: activeKey === 'batchExecute' ? 'createdAt' : 'createAt', // 接口同一字段，参数名不统一
      sortOrder: 'desc',
      ...params,
      current: paraments?.pageNumber
    }
    delete params?.dataPicker;
    switch (activeKey) {
      case "handleExport":
        if (!keys.includes("taskStatus")) {
          params.taskStatus = Object.keys(HandleExportStatus);
        }
        dispatch(queryAndSetExportList(params));
        break;
      case "handleImport":
        if (!keys.includes("taskStatus")) {
          params.taskStatus = Object.keys(HandleImportStatus);
        }
        dispatch(queryAndSetImportList(params));
        break;
      case "batchExecute":
        if (!keys.includes("statusList")) {
          params.statusList = Object.keys(BatchExecuteStatus);
        }
        dispatch(queryBatchSqlList(params));
        break;
      case "syncDict":
        if (!keys.includes("status")) {
          params.status = Object.keys(SyncDictStatus);
        }
        dispatch(querySyncDictList(params));
        break;
      case "dataReplication":
        dispatch(querydataReplicationDicList({
          pageNo: paraments?.pageNumber,
          pageSize: paraments?.pageSize,
          taskTypes: ["DATA_REPLICATION"],
          startAt: params?.startAt,
          endAt: params?.endAt,
          pageNumber: paraments?.pageNumber, //通用的查询逻辑需要
        }))
    }
  }

  // 删除
  const delTask = () => {
    setSelectedRowKeys((params: any) => {
      // @ts-ignore
      tabsPageRef.current?.delTask(params);
      return []
    })
  }

  // 终止 
  const batchStopTask = () => {
    setSelectedRowKeys((params: any) => {
      // @ts-ignore
      tabsPageRef.current?.batchStopTaskRun({ taskIds: params });
      return []
    })
  }

  useEffect(() => {
    if (isEmpty(exportParamsFromMessage)) return
    setQueryParams({
      ...queryParams,
      dataPicker: exportParamsFromMessage?.dataPicker,
    })
  },[JSON.stringify(exportParamsFromMessage)])

  useEffect(() => {
    queryData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams, activeKey,exportParamsFromMessage?.taskId])

  //全局搜索
  useEffect(() => {

    const globalSearchTabKey = location?.state?.globalSearchTabKey;
    if (globalSearchTabKey) {
      setActiveKey(globalSearchTabKey);
      dispatch(setTabsKey(globalSearchTabKey));
    }
  }, [location?.state])

  useEffect(() => {
    if (location?.state?.globalSearchRecordPosition && location?.state?.globalSearchTabKey === activeKey) {
      //处理分页 并选中
      const pageNum = Math.ceil(Number(location.state.globalSearchRecordPosition) / 10);
      setQueryParams({ ...queryParams, ...initPagination, pageNumber: pageNum, dataPicker: null })
    }
  }, [location?.state?.globalSearchRecordPosition, activeKey])

  const handleTimeCycleChange = (date: any) => {
    setQueryParams({
      ...queryParams,
      ...initPagination,
      dataPicker: date,
    })
  }

  // 同步数据字典批量删除确认
  const showDelConfirm = () => {
    confirm({
      title: t("personal:confirmDelete") + "?",
      icon: <ExclamationCircleOutlined />,
      okText: t("personal:confirm"),
      cancelText: t("personal:cancel"),
      onOk: () => {
        delTask()
      }
    });
  };

  // 同步数据字典批量终止确认
  const showBatchStopConfirm = () => {
    confirm({
      title: t("personal:confirmTerminate"),
      icon: <ExclamationCircleOutlined />,
      okText: t("personal:confirm"),
      cancelText: t("personal:cancel"),
      onOk: () => {
        batchStopTask()
      }
    });
  };

  const download = (href: string) => {
    handleDownload({
      href: href,
    });
  };

  const onHandleDelete = () => {

    Modal.confirm({
      centered: true,
      title: activeKey === 'handleImport' ? t('personal:textImportBatchDeleteTip') : t("personal:confirmDelete"),
      okText: t("personal:confirm"),
      cancelText: t("personal:cancel"),
      onOk: () => {
        delTask()
      }
    });
  }

  // 批量下载文件
  const handleFileBatchDownload = (taskIds: string) => {
    checkBatchFile(taskIds?.toString()).then(() => {
      // 检查批量文件是否有密钥
      fetchGet(`/export/export/check/encrypt/more/${taskIds}`).then((res: any) => {
        const { whetherToEncrypt, fileSecretName } = res || {}
        if (whetherToEncrypt) {
          Modal.info({
            width: 520,
            content: (
              <div>
                <div style={{ fontSize: '18px', marginBottom: '30px' }}>{t("personal:fileKey")}</div>
                {t("personal:exportFileEncrypted")}：
                <span style={{ color: "#0c2dc7" }}>
                  {fileSecretName}
                  <VerticalAlignBottomOutlined
                    style={{ color: "#0c2dc7", marginLeft: 3 }}
                    onClick={() => {
                      download(`/export/export/downloadEncrypt/${taskIds}`)
                    }}
                  />
                </span>
              </div>
            ),
            icon: null,
            onOk: () => {
              download(`/export/export/download/more/${taskIds}`)
            }
          });
        } else {
          download(`/export/export/download/more/${taskIds}`)
        }
      }).catch((err: any) => { console.error(err) })
    })
  }

  const renderRightTop = useMemo(() => {
    const { dataPicker } = queryParams;
    return (
      <div className={classnames(styles.timeSty)}>
        {
          ['syncDict'].includes(activeKey) &&
          <SyncResourceTreeSelector
            onChange={(checkedNodes: any) => {
              setQueryParams((param: any) => {
                return {
                  pageNumber: initPagination?.pageNumber,
                  pageSize: initPagination?.pageSize,
                  dataPicker: param?.dataPicker,
                  ...(checkedNodes ?? {})
                };
              })
            }}
          />
        }
        <span className="mr4">{t("personal:timePeriod")}</span>
        <RangePicker
          ranges={{
            [t("personal:last7Days")]: [moment().subtract(7, 'days'), moment()],
            [t("personal:last15Days")]: [moment().subtract(15, 'days'), moment()],
            [t("personal:last30Days")]: [moment().subtract(30, 'days'), moment()],
          }}
          format="YYYY-MM-DD"
          className="mr16"
          style={{ width: 280 }}
          value={dataPicker}
          onChange={handleTimeCycleChange}
          allowClear={false}
        />
        <Button
          icon={<RedoOutlined />}
          className={classnames("mr16", styles.redoOutlined)}
          onClick={() => {
            dispatch(setExportParamsFromMessage({}))
            setQueryParams((param: any) => {
              return {
                pageNumber: initPagination?.pageNumber,
                pageSize: initPagination?.pageSize,
                dataPicker: param?.dataPicker,
              };
            })
          }}
        />
        {
          !['handleExport', 'syncDict', 'dataReplication'].includes(activeKey) &&
          <Button
            type="primary"
            className="mr16"
            disabled={selectedRowKeys && selectedRowKeys?.length > 0 ? false : true}
            onClick={() => onHandleDelete()}
          >
            {t("personal:batchDelete")}
          </Button>
        }
        {
          ['handleExport', 'syncDict'].includes(activeKey) && <Dropdown
            disabled={!selectedRowKeys || selectedRowKeys?.length === 0}
            overlay={
              <Menu className={styles.options}>
                {
                  activeKey === 'handleExport' &&
                  <>
                    <Menu.Item key={1} onClick={() => handleFileBatchDownload(selectedRowKeys?.join(",") || '')}>
                      {t("personal:batchDownload")}
                    </Menu.Item>
                    <Menu.Item key={2} onClick={showDelConfirm}>
                      {t("personal:batchDelete")}
                    </Menu.Item>
                  </>
                }
                {
                  activeKey === 'syncDict' &&
                  <>
                    <Menu.Item key={1} onClick={showDelConfirm}>
                      {t("personal:batchDelete")}
                    </Menu.Item>
                    <Menu.Item key={2} disabled={!allowBatchStop} onClick={showBatchStopConfirm}>
                      {t("personal:batchTerminate")}
                    </Menu.Item>
                  </>
                }
              </Menu>
            }
          >
            <Button
              type="primary"
              onClick={() => {
                setDropdownVisible(true)
              }}
            >
              <Space>
                {t("personal:batchOperation")}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        }
      </div>
    )
  }, [initPagination?.pageNumber, initPagination?.pageSize, allowBatchStop, queryParams, selectedRowKeys, activeKey, dropdownVisible])

  const generateTabs = useMemo(() => {

    return TaskCenterTabsItems.map((item: string) => {

      return (
        <Tabs.TabPane tabKey={item} key={item} tab={TaskTypeMap[item]} >
          <ErrorBoundary>
            <TabsPage
              setTableParams={
                (params: any) => {
                  setQueryParams((queryParams: any) => {
                    return { ...queryParams, pageNumber: initPagination?.pageNumber, ...params }
                  })
                }
              }
              initPagination={initPagination}
              refreshData={queryData}
              getSelectedRowKeys={
                (params: (string | number)[]) => {
                  setSelectedRowKeys(params)
                }
              }
              setAllowBatchStop={setAllowBatchStop}
              ref={tabsPageRef}
              isCurTabKey={location?.state?.globalSearchTabKey === item}
              globalSearchRecordPosition={location?.state?.globalSearchRecordPosition}
            />
          </ErrorBoundary>
        </Tabs.TabPane>
      )
    })
  }, [location?.state?.globalSearchRecordPosition, activeKey])

  return (
    <>
      <div className={styles.taskCenterPageWrap}>
        <BdNavigation
          showIconSty={{ top: 78 }}
          showBdNavigation={location?.state?.showBdNavigation || true}
        />
        <div className={styles.taskCenterTabsWrap}>
          <Tabs
            className={styles.contentStyle}
            activeKey={activeKey}
            style={{ overflow: 'auto' }}
            onChange={(key) => {
              setActiveKey(key);
              dispatch(setTabsKey(key));
              dispatch(setTableInfo({}))
              //tab切换清空原有条件
              dispatch(setExportParamsFromMessage({}))
              setQueryParams((param: any) => {
                return {
                  pageNumber: initPagination?.pageNumber,
                  pageSize: initPagination?.pageSize,
                  dataPicker: param?.dataPicker,
                };
              });
              setSelectedRowKeys([]);
            }}
            tabBarExtraContent={renderRightTop}
          >
            {generateTabs}
          </Tabs>
        </div>
      </div>
    </>
  )
}
