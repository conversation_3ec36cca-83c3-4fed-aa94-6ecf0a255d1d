/* 任务中心 - 导出 */
import React, { ReactNode, forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { Badge, Checkbox, Modal, Popconfirm, Radio, Space, Table, message } from 'antd';
import { Iconfont, LinkButton, TooltipWithQuestionIcon } from 'src/components';
import {
  columnsBatchExecute,
  columnsSyncDict,
  columnsExport,
  columnsImport,
  SortFilters,
  SyncDictStatus,
  BatchExecuteStatus,
  HandleImportStatus,
  HandleExportStatus,
  StatusIconColor,
  dataReplicationColumns
} from 'src/constants';
import { stopTransferTask, checkTransaction } from "src/api/dataTransfer";

import { useRequest, useSelector } from 'src/hook';
import { TaskInfoDrawer } from './TaskInfoDrawer';
import styles from '../index.module.scss';
import classnames from 'classnames';
import {
  batchStopTask,
  checkFileExistance,
  delBatchSqlTask,
  delExportTask,
  delFullTextSearchTask,
  delImportTask,
  stopTask,
  batchStopExportTask
} from 'src/api';
import { handleDownload } from 'src/util';
import { CopyOutlined } from '@ant-design/icons';
import copy from 'copy-to-clipboard';
import { fetchGet } from 'src/api/customFetch';
import { ViewDataReplicationDrawer } from './ViewDataReplicationDrawer';
import { useTranslation } from 'react-i18next';
interface IProps {
  initPagination: { pageNumber: number, pageSize: number }
  setTableParams: (a: any) => void,
  refreshData: () => void,
  getSelectedRowKeys: (a: any) => void,
  setAllowBatchStop?: any,
  isCurTabKey: boolean;
  globalSearchRecordPosition?: number;
}

export const TabsPage = forwardRef((props: IProps, ref) => {
  const {
    initPagination,
    setTableParams,
    refreshData,
    getSelectedRowKeys,
    setAllowBatchStop,
    isCurTabKey,
    globalSearchRecordPosition
  } = props;

  const { t } = useTranslation();

  const { tabsKey, tableInfo, loading } = useSelector((state) => state.myTaskCenter);
  const { data = [], totalItem, paraments } = tableInfo;
  const [columns, setColumns] = useState<any>();
  const [visibleDraw, setVisibleDraw] = useState<boolean>(false);
  const [pagination, setPagination] = useState<any>({
    current: initPagination?.pageNumber,
    pageSize: initPagination?.pageSize,
    total: 0,
  });
  const [detailId, setDetailId] = useState<string | undefined>();
  const [detailData, setDetailData] = useState<any>({});
  const [detailStatusInfo, setDetailStatusInfo] = useState<{ key: string, text: string, color: string }>({ key: "", text: "", color: "" });
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>();
  const [visibleDataReplicationDetail, setVisibleDataReplicationDetail] = useState<null | any>(null);
  //文本导入 删除操作异步获取提示
  const [textImportDeleteTip, setTextImportDeleteTip] = useState(''); // 默认提示
  const [textImportDeleteId, setTextImportDeleteId] = useState<string | undefined>();

  /** 取消任务 */
  const { run: cancelTask } = useRequest(stopTransferTask, {
    manual: true,
    onSuccess() {
      message.success(t("personal:cancelSuccess"));
      refreshData();
    },
  });
  // 删除
  const delTask = (ids: string[]) => {
    let run: any;
    switch (tabsKey) {
      case "handleExport":
        run = delExportTask;
        break;
      case "handleImport":
        run = delImportTask;
        break;
      case "batchExecute":
        run = delBatchSqlTask;
        break;
      case "syncDict":
        run = delFullTextSearchTask;
        break;
    }
    run({ ids: ids }).then(() => {
      message.success(t("personal:deleteSuccess"));
      refreshData();
      setSelectedRowKeys([]);
      setTextImportDeleteId(undefined)
      setTextImportDeleteTip(t("personal:confirmDelete"));
    }).catch(() => {
      setTextImportDeleteId(undefined)
      setTextImportDeleteTip(t("personal:confirmDelete"));
    });
  }

  // set 查看的记录
  const handleDetailPromise = (data: any, id: string | undefined, statusFieldName: string, statusMap: any, StatusIconColor: any) => {
    return Promise.all([
      setDetailData(data),
      setDetailId(id),
      setDetailStatusInfo({
        key: data?.[statusFieldName],
        text: statusMap?.[data?.[statusFieldName]],
        color: StatusIconColor?.[data?.[statusFieldName]]
      })
    ]).then(() => setVisibleDraw(true));
  };

  const copyMet = (mes: string) => {
    copy(mes);
    message.success(t("personal:copySuccessful"));
  };

  // 同步数据字典-终止任务
  const { run: stopTaskRun } = useRequest(stopTask, {
    manual: true,
    onSuccess: () => {
      message.success(t("personal:operationSuccess"));
      refreshData();
    }
  })

  // 同步数据字典 - 批量终止任务
  const { run: batchStopTaskRun } = useRequest(batchStopTask, {
    manual: true,
    onSuccess: () => {
      message.success(t("personal:operationSuccess"));
      refreshData();
    }
  })

  // 导出-中止任务
  const { run: batchStopExportTaskRun } = useRequest(batchStopExportTask, {
    manual: true,
    onSuccess: () => {
      message.success(t("personal:operationSuccess"));
      refreshData();
    }
  })

  // 检查导出文件是否存在
  const { run: checkFile } = useRequest(
    checkFileExistance,
    {
      manual: true,
      fetchKey: (id, origin) => id,
    },
  )

  // 下载导出文件
  const handleFileDownload = (taskId: any, origin: any) => {
    checkFile(taskId?.toString(), origin).then(() => {
      fetchGet(`/export/export/check/encrypt/${taskId}`).then((res: any) => {
        const { whetherToEncrypt, fileSecretKey } = res || {}
        if (whetherToEncrypt) {
          Modal.info({
            width: 520,
            content: (
              <div>
                <div style={{ fontSize: '18px', marginBottom: '30px' }}>{t("personal:fileKey")}</div>
                {t("personal:exportFileEncrypted")}:
                <span style={{ fontWeight: "bold" }}>
                  {fileSecretKey}
                </span>
                <CopyOutlined
                  style={{ color: "#0c2dc7" }}
                  onClick={() => {
                    copyMet(fileSecretKey);
                  }}
                />
              </div>
            ),
            icon: null,
            onOk: () => {
              handleDownload({
                href: `/${origin}/export/download/${taskId}`,
              });
            }
          });
        } else {
          handleDownload({
            href: `/${origin}/export/download/${taskId}`,
          });
        }
      }).catch((err: any) => { console.error(err) })
    })
  }

  function renderOperationBtn(record: any): ReactNode {
    //0:初始化, 1:等待中, 2:运行中, 3:暂停, 4:中止, 5:成功, 6:失败
    switch (record.state) {
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
        return (
          <Space>
            <Popconfirm
              title={t("personal:confirmCancelTask") + "?"}
              onConfirm={() => {
                cancelTask(record.id)
              }}
              disabled={[4, 5, 6].includes(record?.state)}
            >
              <span className={classnames(
                {
                  'nonmodifiableColor': [4, 5, 6].includes(record?.state),
                  'linkStyle': [2].includes(record?.state)
                })}>{t("personal:cancelTask")}</span>
            </Popconfirm>

            <span className='linkStyle' onClick={() => { setVisibleDataReplicationDetail(record) }}>{t("personal:viewDetails")}</span>
          </Space >
        );
      default:
        return <div>-</div>;
    }
  }

  const onHandleDeleteBtnTip = async (tabType: string, record: any) => {

    if (tabType === 'handleImport' && record?.taskStatus === 'PROCESSING' && record?.taskId) {
      try {
        const res = await checkTransaction({ ids: [record.taskId] });
        setTextImportDeleteTip(res?.[0] ? t('personal:textImportDeleteTip1') : t('personal:textImportDeleteTip2'))
        setTextImportDeleteId(record.taskId);
      } catch (error) {
        console.log(error, 'taskImport')
      }
    }
  }

  const getDeleteBtnTipState = (tabsKey: string, record: any) => {

    if (tabsKey === 'handleImport' && record?.taskStatus === 'PROCESSING' && record?.taskId) {
      onHandleDeleteBtnTip(tabsKey, record)
    } else {
      setTextImportDeleteTip(t("personal:confirmDelete"))
      setTextImportDeleteId(record?.taskId || record?.id);
    }
  }

  useEffect(() => {
    setSelectedRowKeys([]);
    setPagination({
      total: totalItem,
      pageSize: paraments?.pageSize,
      current: paraments?.pageNumber,
    });

    let newColumns: any[] = [];
    let typeFieldName: string = "connectionType"; // 数据库类型 接口返回参数名
    let resourceNameTitle: string | ReactNode = <>{t("personal:exportedResources")}<TooltipWithQuestionIcon tooltipTitle={t("personal:exportResultSetNotSupported")} /></>; // 资源列 列名
    let statusFieldName: string = "taskStatus"; // 状态 接口返回参数名
    let statusKeyParamName: string = "taskStatus"; // 状态 传递给接口的参数名
    let statusMap: { [p: string]: string } = {};  // 任务对应状态
    let resourceNameColumnIndex: number = 2;  //  资源列 列位置
    let taskIdFieldName: string = "taskId";  //  任务id 接口返回参数名
    switch (tabsKey) {
      case "handleExport":
        newColumns = [...columnsExport];
        typeFieldName = "connectionType";
        resourceNameColumnIndex = 3;
        statusMap = { ...HandleExportStatus };
        break;
      case "handleImport":
        newColumns = [...columnsImport];
        typeFieldName = "dataSourceType";
        resourceNameTitle = t("personal:targetTable");
        statusMap = { ...HandleImportStatus };
        break;
      case "batchExecute":
        newColumns = [...columnsBatchExecute];
        typeFieldName = "dataSourceType";
        statusKeyParamName = "statusList";
        statusFieldName = "status";
        statusMap = { ...BatchExecuteStatus };
        taskIdFieldName = "id";
        break;
      case "syncDict":
        newColumns = [...columnsSyncDict];
        statusFieldName = "processStatus";
        statusMap = { ...SyncDictStatus };
        typeFieldName = "datasourceType";
        resourceNameTitle = t("personal:synchronizedResources");
        resourceNameColumnIndex = 3;
        statusKeyParamName = "status";
        taskIdFieldName = "id";
        break;
      case 'dataReplication':
        newColumns = [...dataReplicationColumns];
    }
    // 插入 资源列
    if (["handleExport", "handleImport", "syncDict"].includes(tabsKey)) {
      newColumns.splice(resourceNameColumnIndex, 0,
        {
          title: resourceNameTitle,
          dataIndex: 'resourceName',
          key: 'resourceName',
          render: (text: string, record: any) => {
            return text ? (
              <div className={styles.displayFlex}>
                <Iconfont
                  type={record.hasOwnProperty(typeFieldName) ? `icon-connection-${record[typeFieldName]}` : ``}
                  className={classnames(styles.mr3, styles.iconsty)}
                />
                <span
                  className={classnames(styles.textEsllipse, styles.fontSty)}
                  title={text || '-'}
                  onClick={() => {
                    let keyName: string = tabsKey === "syncDict" ? "path" : "resourceName";
                    setTableParams({
                      [keyName]: tabsKey === "syncDict" ? record?.path : text,
                    })
                  }}
                >
                  {text || '-'}
                </span>
              </div>
            ) : '-'
          }
        },
      );
    }
    if (tabsKey !== 'dataReplication') {
      // 操作
      newColumns?.push(
        {
          title: t("personal:operation"),
          dataIndex: 'action',
          key: 'action',
          fixed: 'right',
          render: (val: any, record: any) => {
            let detailItem: any;
            let id: any = record?.[taskIdFieldName];
            detailItem = data?.find((i: any) => (i?.[taskIdFieldName] === id))
            return (
              <>
                {
                  tabsKey === "syncDict" &&
                  <Popconfirm
                    title={t("personal:confirmTerminateTask") + "？"}
                    onConfirm={() => { stopTaskRun(id) }}
                    okText={t("personal:yes")}
                    cancelText={t("personal:no")}
                  >
                    <LinkButton
                      size="small"
                      className="mr-8"
                      disabled={!['WAITING', 'RUNNING'].includes(record?.[statusFieldName])}
                    >
                      {t("personal:terminate")}
                    </LinkButton>
                  </Popconfirm>
                }
                {
                  tabsKey === "handleExport" &&
                  <LinkButton
                    onClick={() => handleFileDownload(id, detailItem?.origin)}
                    size="small"
                    className="mr-8"
                    disabled={!['SUCCESS', 'DOWNLOADED'].includes(record?.[statusFieldName])}
                    title={!['SUCCESS', 'DOWNLOADED'].includes(record?.[statusFieldName]) ? t("personal:fileNotFound") : ""}
                  >
                    {t("personal:download")}
                  </LinkButton>
                }
                <LinkButton
                  onClick={() => {
                    switch (tabsKey) {
                      case "handleExport":
                        detailItem = { ...detailItem, exportFile: [{ "fileName": detailItem?.fileName, "filePath": detailItem?.filePath }] };
                        break;
                      case "batchExecute":
                        detailItem = { ...detailItem, indexFile: [{ "fileName": detailItem?.indexFileName, "filePath": detailItem?.indexFilePath }] };
                        break;
                    }
                    handleDetailPromise(
                      {
                        ...detailItem,
                        indexFile: [{ "fileName": detailItem?.indexFileName, "filePath": detailItem?.indexFilePath }],
                        sqlCheckFile: [{ fileName: detailItem?.sqlCheckFileName, filePath: detailItem?.sqlCheckFilePath }]
                      },
                      id,
                      statusFieldName,
                      statusMap,
                      StatusIconColor,

                    )
                  }}
                >
                  {t("personal:view")}
                </LinkButton>
                {
                  tabsKey === "handleImport" ?
                    <Popconfirm
                      visible={record?.taskId === textImportDeleteId && record?.taskId !== undefined}
                      placement="topLeft"
                      title={textImportDeleteTip}
                      okText={t("personal:confirm")}
                      cancelText={t("personal:cancel")}
                      onConfirm={() => delTask([record?.[taskIdFieldName]])}
                      onCancel={() => {
                        setTextImportDeleteId(undefined);
                        setTimeout(() => {
                          setTextImportDeleteTip('');
                        }, 0)
                      }}
                    >
                      <LinkButton onClick={() => getDeleteBtnTipState(tabsKey, record)}>{t("personal:delete")}</LinkButton>
                    </Popconfirm>
                    :
                    <Popconfirm
                      placement="topLeft"
                      title={t("personal:confirmDelete")}
                      onConfirm={() => delTask([record?.[taskIdFieldName]])}
                      okText={t("personal:confirm")}
                      cancelText={t("personal:cancel")}
                    >
                      <LinkButton>{t("personal:delete")}</LinkButton>
                    </Popconfirm>
                }
                {
                  tabsKey === "handleExport" &&
                  <LinkButton
                    onClick={() => {batchStopExportTaskRun([record?.[taskIdFieldName]])}}
                    size="small"
                    className="mr-8"
                    disabled={!["CREATE", "PROCESSING"]?.includes(record?.[statusFieldName])}
                  >
                    {t("personal:abort")}
                  </LinkButton>
                }
              </>
            )
          }
        }
      )
      // 处理 下拉过滤列
      newColumns = newColumns.map((items: any) => {
        // 任务的状态参数名不统一（接口返回、传到接口的参数，都不统一）
        if (["taskStatus", "status", "processStatus"].includes(items?.key)) {
          // 多选Options
          const statusMapOption = Object.keys(statusMap)?.map((item: any) => statusMap?.[item]) || []
          return {
            ...items,
            render: (val: string, record: any) => {
              const { errorFlag = false } = record
              // 增加 errorFlag 标记，代表状态：部分成功 -- 红色标记，文本还是显示val对应的文本
              const value = errorFlag && tabsKey === 'syncDict' ? 'PARTIAL_SUCCESS' : val
              return (
                <Badge color={StatusIconColor?.[value]} text={statusMap?.[val]} />
              )
            },
            filterDropdown:
              // 多选
              <CheckboxFilterDropdown
                options={statusMapOption}
                getList={
                  (res: any) => {
                    const keyArr = Object.keys(statusMap).filter(key => res.includes(statusMap[key]));
                    setTableParams({ [statusKeyParamName]: keyArr || [] })
                  }
                }
              />,
          }
        }
        if (["createdAt", "createAt", "startTime"].includes(items?.key)) {
          return {
            ...items,
            filterDropdown:
              <RadioFilterDropdown
               value={paraments?.sortOrder}
                optionsEnums={SortFilters}
                getE={
                  (e: any) => {
                    setTableParams({ "sortOrder": e?.e?.target?.value || "desc" })
                  }
                }
              />,
          }
        }
        return { ...items }
      })
    } else {
      newColumns?.push(
        {
          title: t("personal:operation"),
          dataIndex: 'action',
          key: 'action',
          fixed: 'right',
          render: (val: any, record: any) => {

            return renderOperationBtn(record);
          }
        }
      )
    }
    setColumns(newColumns);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(data), tabsKey, paraments, totalItem, t, textImportDeleteId])

  // 使用 useImperativeHandle 公开函数给父组件使用
  useImperativeHandle(ref, () => ({
    delTask,
    batchStopTaskRun
  }));

  const isSelectedRowIndex = useMemo(() => {
    if (!globalSearchRecordPosition) {
      return null;
    }
    const pageNum = Math.ceil(Number(globalSearchRecordPosition) / 10);
    if (pageNum === pagination?.current && isCurTabKey) {
      const itemIndexInPage = globalSearchRecordPosition % pagination.pageSize;
      return itemIndexInPage === 0 ? pagination.pageSize - 1 : itemIndexInPage - 1;
    }
  }, [pagination?.current, globalSearchRecordPosition, isCurTabKey])

  const renderTable = useMemo(() => {
    const rowSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
        if (tabsKey === 'syncDict') {
          const allowBatchStop: boolean = selectedRows.filter((item: any) => ['WAITING', 'RUNNING'].includes(item?.processStatus))?.length > 0
          setAllowBatchStop(allowBatchStop)
        }
        getSelectedRowKeys(selectedRowKeys);
        setSelectedRowKeys(selectedRowKeys);
      },
    }

    return (
      <Table
        loading={loading}
        columns={columns}
        dataSource={data}
        rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected' : ''}
        rowKey={(record) => (record?.taskId || record?.id)} // 任务id参数名不一致
        rowSelection={tabsKey !== 'dataReplication' ? rowSelection : undefined}
        pagination={{
          ...pagination,
          size: 'default',
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: () => t("personal:totalRecords", { total: totalItem }),
          onChange: (pageNumber, pageSize) => {
            setTableParams({ pageNumber, pageSize })
          }
        }}
        size='middle'
        scroll={{ x: 1900, y: `calc(100vh - 330px)` }}
        className={styles.tabsPageTable}
      />
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(columns), JSON.stringify(data), loading, pagination, selectedRowKeys])

  return (
    <>
      {renderTable}
      {
        visibleDraw && <TaskInfoDrawer
          visible={visibleDraw}
          setVisible={setVisibleDraw}
          detailData={detailData}
          detailStatusInfo={detailStatusInfo}
          detailId={detailId}
          refreshData={refreshData}
        />
      }
      {
        visibleDataReplicationDetail &&
        <ViewDataReplicationDrawer
          record={visibleDataReplicationDetail}
          onClose={() => setVisibleDataReplicationDetail(null)}
        />
      }
    </>
  )
})

// 自定义筛选下拉框-单选
const RadioFilterDropdown = ({
  value,
  optionsEnums,
  getE,
}: {
  value: 'acs' | 'desc',
  optionsEnums: { text: string, value: string }[],
  getE: (a: any) => void,
}) => {
  return (
    <div style={{ padding: 8 }}>
      <Radio.Group
        value={value}
        onChange={(e: any) => {
          getE({ e: e })
        }}
      >
        <Space direction="vertical">
          {
            optionsEnums?.map((item: { text: string, value: string }) => {
              return <Radio value={item?.value}>{item?.text}</Radio>
            })
          }
        </Space>
      </Radio.Group>
    </div>
  )
};

// 自定义筛选下拉框-多选
const CheckboxFilterDropdown = ({
  options,
  getList,
}: {
  options: string[],
  getList: (a: any) => void,
}) => {
  const [checkedList, setCheckedList] = useState<any>(options);
  const CheckboxGroup = Checkbox.Group;
  const onChange = (list: any) => {
    getList(list);
    setCheckedList(list);
  };
  return (
    <div style={{ padding: 8 }} className={styles.statusCheckboxGroup}>
      <CheckboxGroup options={options} value={checkedList} onChange={onChange} />
    </div>
  )
};