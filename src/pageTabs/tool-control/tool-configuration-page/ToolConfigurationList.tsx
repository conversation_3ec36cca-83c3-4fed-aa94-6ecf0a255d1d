import React, { useEffect, useState, useMemo } from 'react';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import * as _ from 'lodash';
import { useLocation } from 'react-router-dom';
import { Table, Input, Button, Space, message } from 'antd';
import {
  addControlItem,
  editControlItem,
  deleteControlItem,
  changeControlItemState,
  getAllRulesByPage,
  IControlItemParams,
  IRuleListData,
  IDeleteCtrolItemParams,
  IChangeCtrolStateParams,
  ISearchParams
} from 'src/api';
import { useRequest, useSelector } from 'src/hook';
import { PermissionTooltip } from 'src/components';
import { getCurrentModulePermissionByUrl } from 'src/util/utils';
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import { columns } from './columns';
import { ToolConfigModal } from './modals';
import styles from './index.module.scss';
import i18n from 'i18next';

export const ToolConfigurationList = () => {

 const location = useLocation();
 const { state = {} } = location as {state: GloablSearchLocationState};
  //菜单权限
  const { permissionList } = useSelector((state) => state?.login)

  //模块权限查询
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const modulePermissionObj: { isOnlyRead: boolean; roleNameList: string[] } = useMemo(() => {
    return getCurrentModulePermissionByUrl(permissionList, 'CLIENT_MANAGER')
  }, [JSON.stringify(permissionList)])

  const [searchParams, setSearchParams] = useState<ISearchParams>({ pageSize: 10, pageNum: 1 });

  const [selectedRecordInfo, setSelectedRecordInfo] = useState<IControlItemParams | null>(null);
  const [toolConfigModalVisible, setToolConfiModalVisible] = useState(false);

  //配置信息
  const { data: toolList, loading, run: runGetAllConfigurationData, refresh: refreshAllConfiguration } = useRequest<IRuleListData>(
    getAllRulesByPage, {
    manual: true,
    debounceInterval: 300
  })
  // add configuration
  const { run: runAddControlItem, loading: addToolLoading } = useRequest<IControlItemParams>(addControlItem, {
    manual: true,
    onSuccess() {
      message.success(i18n.t("toolControl:addSuccess"));
      setToolConfiModalVisible(false);
      setSearchParams({ ...searchParams, pageNum: 1 })
    }
  })
  //edit configuration
  const { run: runEditControlItem, loading: ediltToolLoading } = useRequest<IControlItemParams>(editControlItem, {
    manual: true,
    onSuccess() {
      message.success(i18n.t("toolControl:editSuccess"));
      setToolConfiModalVisible(false);
      setSelectedRecordInfo(null);
      setSearchParams({ ...searchParams, pageNum: 1 })
    }
  })

  //delete configuration
  const { run: runDeleteControlItem } = useRequest<IDeleteCtrolItemParams>(deleteControlItem, {
    manual: true,
    onSuccess() {
      message.success(i18n.t("toolControl:deleteSuccess"));
      setToolConfiModalVisible(false);
      setSearchParams({ ...searchParams, pageNum: 1 })
    }
  })
  //开启/关闭管控 
  const { run: runChangeControlItemState } = useRequest<IChangeCtrolStateParams>(changeControlItemState, {
    manual: true,
    onSuccess() {
      message.success(i18n.t("toolControl:modifySuccess"));
      setToolConfiModalVisible(false);
      refreshAllConfiguration();
    }
  })
  
  useEffect(() => {

    runGetAllConfigurationData(searchParams);

  }, [searchParams])
  
  useEffect(() => {
    if (state?.globalSearchRecordPosition) {
      //处理分页 并选中
      const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
      setSearchParams({ ...searchParams, pageNum: pageNum })
    }
  }, [state?.globalSearchRecordPosition])
  
  const isSelectedRowIndex = useMemo(() => {
    if (!state?.globalSearchRecordPosition) {
      return null;
    }
    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
    if (pageNum === searchParams?.pageNum) {
      const itemIndexInPage = state.globalSearchRecordPosition % searchParams.pageSize;
      return itemIndexInPage === 0 ? searchParams.pageSize - 1 : itemIndexInPage - 1;
    }
  },[searchParams?.pageNum, state?.globalSearchRecordPosition])

  return (
    <div className={styles.toolConfigurationContainer}>
      <div className={styles.toolConfigurationHeader}>
        <Space>
          <Input
            placeholder={i18n.t("toolControl:searchAuthorizedToolsOrDatabaseType")}
            onChange={(e: any) => setSearchParams({
              ...searchParams, searchFilter: e.target.value,
              pageNum: 1
            })}
            prefix={<SearchOutlined />}
            value={searchParams?.searchFilter}
            allowClear
            className={styles.toolInput}
          />
          <PermissionTooltip
            title={i18n.t("toolControl:toolControl")}
            permissionlist={modulePermissionObj}
          >
            <Button
              icon={<PlusOutlined />}
              type='primary'
              disabled={modulePermissionObj.isOnlyRead}
              onClick={() => setToolConfiModalVisible(true)}
              >
              {i18n.t("toolControl:add")}
            </Button>
          </PermissionTooltip>
        </Space>
      </div>
      <div className={styles.userAuditWrap}>
        <Table
          loading={loading}
          rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected': ''}
          dataSource={toolList?.clientManagerDTOList || []}
          columns={columns({
            permissionList: modulePermissionObj,
            onEditRecord: (record: any) => { setSelectedRecordInfo(record); setToolConfiModalVisible(true) },
            onDeleteRecord: (params: IDeleteCtrolItemParams) => runDeleteControlItem(params),
            onChangeRecordState: (params: IChangeCtrolStateParams) => runChangeControlItemState(params)
          })}
          scroll={{ y: `calc(100vh - 300px)` }}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
            total: toolList?.totalNum || 0,
            current: searchParams?.pageNum || 10,
            showTotal: (total: number) => i18n.t('systemManagement.personManagement.table.pagination.total',{val: total}),
            pageSize: searchParams?.pageSize,
            onChange: (page: number, pageSize?: number) => {
              setSearchParams({
                ...searchParams,
                pageNum: page,
                pageSize: pageSize || 10,
              })
            },
          }}
        />
      </div>
      {/* 新增/编辑工具弹框 */}
      {
        toolConfigModalVisible &&
        <ToolConfigModal
          visible={toolConfigModalVisible}
          selectedRecordInfo={selectedRecordInfo}
          okBtnLoading={addToolLoading || ediltToolLoading}
          onCancel={() => { setToolConfiModalVisible(false); setSelectedRecordInfo(null) }}
          onOk={(params: any) => {
            if (selectedRecordInfo) {
              runEditControlItem({ ...params, id: selectedRecordInfo.id, manager: selectedRecordInfo.manager })
            } else {
              runAddControlItem({ ...params, manager: true })
            }
          }}
        />
      }
    </div>
  )
}