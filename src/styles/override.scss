@import 'src/styles/variables';

.menu-dropdown {
  top: 48px !important;

  .ant-dropdown-menu {
    background-color: #fff;
    padding: 6px 0;
  }

  .ant-dropdown-menu-item {
    padding: 8px 16px;
    color: #0F244C;
    position: relative;
    &:hover {
      color:var(--primary-color);
      &::before {
        position: absolute;
        content: '';
        width: 4px;
        height: 12px;
        left: 0;
        top: calc(50% - 6px);
        background-color: var(--primary-color);
      }
    }
  }

  .ant-dropdown-menu-submenu-title {
    padding: 8px 16px;
    color: #0F244C;
    &:hover {
      color:var(--primary-color);
    }
  }

  .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {
    color: #0F244C;
  }
}

.drop-submenu {
  background-color: #ffffff;
  padding: 0;

  ul {
    background-color: #ffffff;
    margin: 0;
  }

  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    background-color: #fff;
    padding: 8px 16px;
    color: #0F244C;

    &:hover {
      color:var(--primary-color);
    }
  }
}

.menu-group-dropdown {
  min-width: 178px !important;
  .ant-dropdown-menu-item-group-title {
    padding: 5px 24px;
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-dropdown-menu-item-group-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .ant-dropdown-menu-item {
    padding: 8px 16px;
    color: #ffffff;
    margin-bottom: 12px;

    &:hover {
      background: transparent;
      color: var(--primary-color);
    }
  }
}

// ant tree title text overflow ellipsis
.ant-tree-node-content-wrapper {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// card 副标题高度设置
.ant-card-head-title {
  height: 56px;
  display: flex;
  align-items: center;
}

.ant-table-thead > tr > th,
.ant-table-tbody > tr > td{
  padding: 12px 16px 13px;
  border-bottom: 1px solid #DFE1E6;
}
.ant-table-thead > tr > th,
.ant-table-small .ant-table-thead > tr > th {
  background-color: #F7F9FC;
  color: #667084;
}
.ant-table-tbody > tr > td {
  color: #0f244c;
}

// 修复按钮loading
.ant-btn > .ant-btn-loading-icon {
  .anticon {
    animation: none;
    svg {
      animation: loadingCircle 1s infinite linear;
    }
  }
}

// 分页组件样式调整
.ant-pagination {

  .ant-pagination-options {
    margin-top: -2px;
  }
  .ant-pagination-options-size-changer {
    margin-top: 2px;
  }
  .ant-pagination-options-quick-jumper {
    margin-top: 2px;
  }
}

// 重置Ag Grid组件滚动条样式
.ag-body-horizontal-scroll {
  height: 14px !important;
  max-height: 14px !important;
  min-height: 14px !important;
  .ag-body-horizontal-scroll-viewport {
    height: 14px !important;
    max-height: 14px !important;
    min-height: 14px!important;
    // 水平滚动条样式重置
    &::-webkit-scrollbar-thumb {
      background-color: #ccc !important;
      &:hover {
        background-color: #666 !important;
      }
    }
    .ag-body-horizontal-scroll-container {
      height: 14px !important;
      max-height: 14px !important;
      min-height: 14px!important;
    }
  }
}

// 去掉内部的重复滚动，只有滚动到最底部才能看见，直接用外层滚动
.ag-center-cols-viewport {
  height: 100% !important;
  overflow-x: hidden !important;
}

// 垂直滚动条样式重置
.ag-body-viewport.ag-layout-normal {
  &::-webkit-scrollbar-thumb {
    background-color: #ccc !important;
    &:hover {
      background-color: #666 !important;
    }
  }
}

// 黑夜模式下的滚动条样式适配
[data-theme='dark'] {
  // 水平滚动条在黑夜模式下的样式
  .ag-body-horizontal-scroll {
    .ag-body-horizontal-scroll-viewport {
      &::-webkit-scrollbar-thumb {
        background-color: #5A5A62 !important;
        &:hover {
          background-color: #6A6A72 !important;
        }
      }
      &::-webkit-scrollbar-track {
        background-color: #313137 !important;
      }
    }
  }
  
  // 垂直滚动条在黑夜模式下的样式
  .ag-body-viewport.ag-layout-normal {
    &::-webkit-scrollbar-thumb {
      background-color: #5A5A62 !important;
      &:hover {
        background-color: #6A6A72 !important;
      }
    }
    &::-webkit-scrollbar-track {
      background-color: #313137 !important;
    }
  }
  
  // 所有ag-grid相关元素的滚动条样式
  .ag-body-horizontal-scroll,
  .ag-body-vertical-scroll,
  .ag-body-horizontal-scroll-viewport,
  .ag-body-horizontal-scroll-container,
  .ag-body-viewport,
  .ag-root-wrapper,
  .ag-center-cols-viewport,
  .ag-horizontal-left-spacer,
  .ag-horizontal-right-spacer {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #313137;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #5A5A62;
      border-radius: 4px;
      border: 1px solid #434348;
      
      &:hover {
        background: #6A6A72;
      }
      
      &:active {
        background: #7A7A82;
      }
    }
    
    &::-webkit-scrollbar-corner {
      background: #313137;
    }
  }
  
  // 特别针对水平滚动条的spacer元素
  .ag-horizontal-left-spacer,
  .ag-horizontal-right-spacer {
    background-color: #313137 !important;
    border-color: #3A3A42 !important;
  }
}