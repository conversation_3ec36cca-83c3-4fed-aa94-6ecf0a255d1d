@import '~ag-grid-community/styles/ag-grid.css';
@import '~ag-grid-community/styles/ag-theme-balham.css';
@import 'src/styles/variables';

// 定义颜色变量
:root {
  --Color05: #DFE2E7;
  --Color06: #F0F3F8;
  --Color07: #F6F8FA;
  --Color6: #2679FE;
  --Color3: #7D848E;
  --N01: #1F2630;
  --N07: #F6F8FA;
}

.ag-theme-balham {
  /* ag-grid 29+ 版本使用 CSS 变量而不是 Sass mixins */
  --ag-header-background-color: var(--Color07);
  --ag-odd-row-background-color: #fff;
  --ag-row-hover-color: #e3edfc;
  --ag-borders: none;

  // 阻止文本选择，特别是在 Shift + 点击时
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  // 确保所有表格元素都不可选择
  .ag-root-wrapper,
  .ag-header,
  .ag-body,
  .ag-cell,
  .ag-header-cell {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }



  // 列头样式
  .ag-header-cell:not([col-id="__rowIndex"]) {
    padding: 9px 8px !important;
    background: var(--Color07, #F6F8FA) !important;
    color: var(--Color3, #7D848E) !important;


    border:#efeff0 1px solid !important;

  }

  // 确保 React 容器占满整个列头宽度
  .ag-header-cell .ag-react-container {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  // 列头选中状态
  .ag-header-cell:not([col-id="__rowIndex"]).ag-header-cell-hover,
  .ag-header-cell:not([col-id="__rowIndex"]):hover {
    background: var(--Color05, #DFE2E7) !important;
    color: var(--Color6, #2679FE) !important;
  }

  // 普通单元格样式 (排除序号列)
  .ag-cell:not(.number-cell) {
    background: #FFF !important;
    color: var(--N01, #1F2630) !important;
    border: #efeff0 1px solid !important;
  }

  // 普通单元格选中状态
  .ag-cell:not(.number-cell).ag-cell-focus {
    background-color: rgba(24, 144, 255, 0.2) !important;
  }

  .ag-cell.number-cell {
    background: var(--N07, #F6F8FA) !important;
    border: #efeff0 1px solid !important;
    padding-left: 0px !important;  // 覆盖默认的 11px padding
    padding-right: 0px !important;
    text-align: center !important; // 序号居中显示
  }

  // 行选中状态覆盖
  .ag-row-selected .ag-cell {
    background-color: rgba(38, 121, 254, 0.1) !important;
  }

  .ag-row-selected .ag-row-index-cell {
    background: var(--Color06, #F0F3F8) !important;
    color: var(--Color6, #2679FE) !important;
  }

  // 所有其他列头样式 (先定义通用样式)
  .ag-header-cell:not([col-id="__rowIndex"]) {
    background: var(--N07, #F6F8FA) !important;
    color: var(--N01, #1F2630);
    height: 32px !important;
  }

  // 行头样式 (序号列) - 放在后面确保优先级
  .ag-header-cell[col-id="__rowIndex"] {
    background: var(--N07, #F6F8FA) !important;
    height: 32px !important;
  }

  //某些列禁止编辑 添加了row calss
  .ag-disabled-edit {
    opacity: 0.5;
    cursor: not-allowed;
    user-select: none;
    .ag-cell {
      pointer-events: none;
    }
    .ag-cell-value {
      &::selection {
        background: transparent;
        color: var(--ag-data-color, var(--ag-foreground-color, #000));
      }
    }
  }
}

.ag-theme-balham-dark {
  /* ag-grid 29+ 版本使用 CSS 变量而不是 Sass mixins */
  --ag-background-color: #222227;
  --ag-header-background-color: #313137;
  --ag-header-column-resize-handle: block;
  --ag-header-column-resize-handle-color: rgba(255, 255, 255, 0.5);
  --ag-header-column-resize-handle-width: 2px;
  --ag-odd-row-background-color: #222227;
  --ag-row-hover-color: rgba(64, 169, 255, 0.08);
  --ag-selected-row-background-color: rgba(64, 169, 255, 0.15);
  --ag-borders: none;

  // 表头样式
  .ag-header-cell:not([col-id="__rowIndex"]) {
    padding: 9px 8px !important;
    background: #313137 !important;
    color: #C3C7CD !important;
    height: 32px !important;
  }

  // 确保 React 容器占满整个列头宽度（黑夜模式）
  .ag-header-cell .ag-react-container {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  // 表头选中/悬停状态
  .ag-header-cell:not([col-id="__rowIndex"]).ag-header-cell-hover,
  .ag-header-cell:not([col-id="__rowIndex"]):hover {
    background: #394353 !important;
    color: #2679FE !important;
  }

  // 行序号表头样式
  .ag-header-cell[col-id="__rowIndex"] {
    background: #313137 !important;
    color: #C3C7CD !important;
    height: 32px !important;
  }

  // 行序号表头选中/悬停状态
  .ag-header-cell[col-id="__rowIndex"].ag-header-cell-hover,
  .ag-header-cell[col-id="__rowIndex"]:hover {
    background: #313137 !important;
    color: #2679FE !important;
  }

  // 普通数据单元格样式
  .ag-cell:not(.number-cell) {
    background: #43434A !important;
    color: #FAFCFF !important;
    border: #3A3A42 1px solid !important;
    padding: 0px;
  }

  // 普通单元格选中状态
  .ag-cell:not(.number-cell).ag-cell-focus {
    background: #394353 !important;
    border: 1.5px solid #2679FE !important;
    color: #FAFCFF !important;
  }

  // 行序号单元格选中状态 - 移除边框
  .ag-cell.number-cell.ag-cell-focus {
    background: #313137 !important;
    color: #2679FE !important;
    border: none !important;
    padding-left: 0px !important;  // 保持一致的 padding
    padding-right: 0px !important;
  }

  // 行序号单元格基础样式 - 覆盖默认 padding
  .ag-cell.number-cell {
    background: #313137 !important;
    color: #C3C7CD !important;
    border: #3A3A42 1px solid !important;
    padding-left: 0px !important;  // 覆盖默认的 11px padding
    padding-right: 0px !important;
    text-align: center !important; // 序号居中显示
  }

  // 确保行头单元格点击时不显示焦点边框
  .ag-cell.number-cell.ag-cell-focus:not(.ag-cell-range-selected),
  .ag-cell.number-cell.ag-context-menu-open,
  .ag-cell.number-cell.ag-cell-range-single-cell,
  .ag-cell.number-cell.ag-cell-range-single-cell.ag-cell-range-handle {
    border: none !important;
  }

  // 排序图标颜色
  .ag-icon {
    color: #C3C7CD !important;
  }

  .ag-header-cell:hover .ag-icon {
    color: #7D848E !important;
  }

  // 编辑状态
  .ag-cell-inline-editing {
    color: #FAFCFF !important;
    background: #434C5B !important;
  }

  // 行选中状态覆盖
  .ag-row-selected .ag-cell {
    background-color: rgba(38, 121, 254, 0.15) !important;
  }

  .ag-row-selected .ag-row-index-cell {
    background: #2679FE !important;
    color: white !important;
  }

  // 禁用编辑行的样式调整
  .ag-disabled-edit {
    opacity: 0.6;
    
    .ag-cell {
      color: #7D848E !important;
    }
  }

  // **滚动条样式优化**
  .ag-body-horizontal-scroll,
  .ag-body-vertical-scroll {
    // 通用滚动条尺寸
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    // 水平滚动条
    &::-webkit-scrollbar:horizontal {
      height: 8px;
    }
    
    // 垂直滚动条
    &::-webkit-scrollbar:vertical {
      width: 8px;
    }
    
    // 滚动条轨道
    &::-webkit-scrollbar-track {
      background: #313137;
      border-radius: 4px;
    }
    
    // 滚动条滑块
    &::-webkit-scrollbar-thumb {
      background: #5A5A62;
      border-radius: 4px;
      border: 1px solid #434348;
      
      &:hover {
        background: #6A6A72;
      }
      
      &:active {
        background: #7A7A82;
      }
    }
    
    // 滚动条角落
    &::-webkit-scrollbar-corner {
      background: #313137;
    }
  }

  // 专门针对水平滚动条的额外优化
  .ag-body-horizontal-scroll-viewport,
  .ag-body-horizontal-scroll-container {
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #313137;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #5A5A62;
      border-radius: 4px;
      border: 1px solid #434348;
      
      &:hover {
        background: #6A6A72;
      }
      
      &:active {
        background: #7A7A82;
      }
    }
  }

  // 表格主体滚动条
  .ag-body-viewport {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #313137;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #5A5A62;
      border-radius: 4px;
      border: 1px solid #434348;
      
      &:hover {
        background: #6A6A72;
      }
      
      &:active {
        background: #7A7A82;
      }
    }
    
    &::-webkit-scrollbar-corner {
      background: #313137;
    }
  }

  // 表格容器滚动条
  .ag-root-wrapper {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #313137;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #5A5A62;
      border-radius: 4px;
      border: 1px solid #434348;
      
      &:hover {
        background: #6A6A72;
      }
      
      &:active {
        background: #7A7A82;
      }
    }
    
    &::-webkit-scrollbar-corner {
      background: #313137;
    }
  }
}


.ag-cell-inline-editing {
  color: #000000 !important;
}

.ag-theme-balham .ag-header-cell::after, .ag-theme-balham .ag-header-group-cell::after {
  content: none !important; 
}




